# 使用官方OpenJDK Alpine镜像（轻量级）
FROM harbor.wbcro.com/common/openjdk-17:v1.0.0

# 维护者信息
LABEL maintainer="wangjindu"

ENV  LANG C.UTF-8

# 创建工作目录并设置权限
WORKDIR /home/<USER>
VOLUME /home/<USER>
# 复制JAR文件并设置所有权
COPY ./wbyy-gateway.jar /home/<USER>/wbyy-gateway.jar



ENTRYPOINT ["java","-jar","wbyy-gateway.jar", "--spring.profiles.active=test", "--spring.cloud.nacos.discovery.ip=***********", "--spring.cloud.nacos.discovery.port=11000"]

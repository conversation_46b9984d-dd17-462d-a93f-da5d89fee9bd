#!/bin/bash
# 配置参数
APP_CODE=$1

LOCK_TIMEOUT=3

mkdir -p /var/jenkins_home/.auto_increment/dat

COUNTER_DIR="/var/jenkins_home/.auto_increment/dat"
COUNTER_FILE="$COUNTER_DIR/auto_inc_counter_$APP_CODE.dat"

init_counter() {
    [ -d "$COUNTER_DIR" ] || COUNTER_DIR="${HOME}"
    [ -f "$COUNTER_FILE" ] || echo 0 > "$COUNTER_FILE"
    chmod 644 "$COUNTER_FILE" 2>/dev/null
}

get_next_id() {
    exec 200>"$COUNTER_FILE.lock" || { echo "ERROR: Lock init failed" >&2; return 1; }
    flock -w $LOCK_TIMEOUT -x 200 || { echo "ERROR: Lock timeout" >&2; return 1; }
    
    local current=$(cat "$COUNTER_FILE" 2>/dev/null || echo 0)
    local next=$((current + 1))
    echo $next > "$COUNTER_FILE"
    
    flock -u 200
    echo $next
    return 0
}

case "$2" in
    --get) init_counter && get_next_id ;;
    *) echo "Usage: $0 --get" ;;
esac


#!/bin/sh

TYPE=$1
BUILD_USER=$2
BUILD_TIMESTAMP=$3
BUILD_NUMBER=$4
BUILD_URL=$5
JOB_BASE_NAME=$6
RUN_DISPLAY_URL=$7
RUN_CHANGES_DISPLAY_URL=$8

#替换成自己的群机器人key值
CHAT_WEBHOOK_URL='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key'
CHAT_WEBHOOK_KEY="4c7a7320-d16b-47d3-b188-bd2ad5ccf6cd"
CHAT_CONTENT_TYPE='Content-Type: application/json'

#-o代表或的意思，成功或失败 CHAT_WEBHOOK_URL都是一样的WEBHOOK url
# if [ _"${TYPE}" = _"success" -o  _"${TYPE}" = _"failure" ]; then
#
# fi

if [ _"${CHAT_WEBHOOK_KEY}" = _"" ]; then
echo "please make sure CHAT_WEBHOOK_KEY has been exported as environment variable"

fi
echo "## send message for : ${TYPE}"
if [ _"${TYPE}" = _"success" ]; then
curl "${CHAT_WEBHOOK_URL}=${CHAT_WEBHOOK_KEY}" \
-H "${CHAT_CONTENT_TYPE}" \
-d '
   {
        "msgtype": "markdown",
        "markdown": {
            "content": "# **Jenkins任务通知** \n
                 ## <font color=\"info\"> 😀😀 部署成功 😀😀 </font>
                 > 构建人：<font color=\"comment\"> '"${BUILD_USER}"' </font>
                 > 任务名称：<font color=\"comment\"> '"${JOB_BASE_NAME}"' </font>
                 > 任务批次：<font color=\"comment\"> '"${BUILD_NUMBER}"' </font>
                 > 构建时间：<font color=\"comment\"> '"${BUILD_TIMESTAMP}"' </font>
                 > 任务总览：<font color=\"comment\"> [点击查看]('"${BUILD_URL}"') </font>
                 > 变更记录：<font color=\"comment\"> [点击查看]('"${RUN_CHANGES_DISPLAY_URL}"') </font>\n
                 ### <font color=\"info\"> 本次构建已结束 - 部署成功 </font>"

        }
   }
'
elif [ _"${TYPE}" = _"failure" ]; then
curl "${CHAT_WEBHOOK_URL}=${CHAT_WEBHOOK_KEY}" \
-H "${CHAT_CONTENT_TYPE}" \
-d '
   {
        "msgtype": "markdown",
        "markdown": {
            "content": "# **Jenkins任务通知** \n
                 ## <font color=\"warning\"> 😭😭 部署失败 😭😭 </font>
                 > 构建人：<font color=\"comment\"> '"${BUILD_USER}"' </font>
                 > 任务名称：<font color=\"comment\"> '"${JOB_BASE_NAME}"' </font>
                 > 任务批次：<font color=\"comment\"> '"${BUILD_NUMBER}"' </font>
                 > 构建时间：<font color=\"comment\"> '"${BUILD_TIMESTAMP}"' </font>
                 > 构建日志：<font color=\"comment\"> [点击查看]('"${BUILD_URL}"console') </font> \n
                 ### <font color=\"warning\"> 本次构建已结束 - 部署失败 <@'"${BUILD_USER}"'></font>"

        }
   }
'
elif [ _"${TYPE}" = _"begin" ]; then
curl "${CHAT_WEBHOOK_URL}=${CHAT_WEBHOOK_KEY}" \
-H "${CHAT_CONTENT_TYPE}" \
-d '
   {
        "msgtype": "markdown",
        "markdown": {
            "content": "# **Jenkins任务通知** \n
                 ## <font color=\"comment\"> 🙂🙂 开始构建 🙂🙂 </font>
                 > 构建人：<font color=\"comment\"> '"${BUILD_USER}"' </font>
                 > 任务名称：<font color=\"comment\"> '"${JOB_BASE_NAME}"' </font>
                 > 任务批次：<font color=\"comment\"> '"${BUILD_NUMBER}"' </font>
                 > 构建时间：<font color=\"comment\"> '"${BUILD_TIMESTAMP}"' </font>
                 > 任务总览：<font color=\"comment\"> [点击查看]('"${BUILD_URL}"') </font>
                 > 变更记录：<font color=\"comment\"> [点击查看]('"${RUN_CHANGES_DISPLAY_URL}"') </font>
                 > 构建步骤：<font color=\"comment\"> [点击查看]('"${RUN_DISPLAY_URL}"') </font>
                 > 构建日志：<font color=\"comment\"> [点击查看]('"${BUILD_URL}"console') </font> \n
                 ### <font color=\"comment\"> 开始构建... </font>"

        }
   }
'

fi


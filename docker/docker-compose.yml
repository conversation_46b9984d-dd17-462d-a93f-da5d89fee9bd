version : '3.8'
services:
  wbyy-nacos:
    container_name: wbyy-nacos
    image: nacos/nacos-server
    build:
      context: ./nacos
    environment:
      - MODE=standalone
    volumes:
      - ./nacos/logs/:/home/<USER>/logs
      - ./nacos/conf/application.properties:/home/<USER>/conf/application.properties
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    depends_on:
      - wbyy-mysql
  wbyy-mysql:
    container_name: wbyy-mysql
    image: mysql:5.7
    build:
      context: ./mysql
    ports:
      - "3306:3306"
    volumes:
      - ./mysql/conf:/etc/mysql/conf.d
      - ./mysql/logs:/logs
      - ./mysql/data:/var/lib/mysql
    command: [
          'mysqld',
          '--innodb-buffer-pool-size=80M',
          '--character-set-server=utf8mb4',
          '--collation-server=utf8mb4_unicode_ci',
          '--default-time-zone=+8:00',
          '--lower-case-table-names=1'
        ]
    environment:
      MYSQL_DATABASE: 'ry-cloud'
      MYSQL_ROOT_PASSWORD: password
  wbyy-redis:
    container_name: wbyy-redis
    image: redis
    build:
      context: ./redis
    ports:
      - "6379:6379"
    volumes:
      - ./redis/conf/redis.conf:/home/<USER>/redis/redis.conf
      - ./redis/data:/data
    command: redis-server /home/<USER>/redis/redis.conf
  wbyy-nginx:
    container_name: wbyy-nginx
    image: nginx
    build:
      context: ./nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/html/dist:/home/<USER>/projects/wbyy-ui
      - ./nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/logs:/var/log/nginx
      - ./nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - wbyy-gateway
    links:
      - wbyy-gateway
  wbyy-gateway:
    container_name: wbyy-gateway
    build:
      context: ./wbyy/gateway
      dockerfile: dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - wbyy-redis
    links:
      - wbyy-redis
  wbyy-auth:
    container_name: wbyy-auth
    build:
      context: ./wbyy/auth
      dockerfile: dockerfile
    ports:
      - "9200:9200"
    depends_on:
      - wbyy-redis
    links:
      - wbyy-redis
  wbyy-modules-system:
    container_name: wbyy-modules-system
    build:
      context: ./wbyy/modules/system
      dockerfile: dockerfile
    ports:
      - "9201:9201"
    depends_on:
      - wbyy-redis
      - wbyy-mysql
    links:
      - wbyy-redis
      - wbyy-mysql
  wbyy-modules-gen:
    container_name: wbyy-modules-gen
    build:
      context: ./wbyy/modules/gen
      dockerfile: dockerfile
    ports:
      - "9202:9202"
    depends_on:
      - wbyy-mysql
    links:
      - wbyy-mysql
  wbyy-modules-job:
    container_name: wbyy-modules-job
    build:
      context: ./wbyy/modules/job
      dockerfile: dockerfile
    ports:
      - "9203:9203"
    depends_on:
      - wbyy-mysql
    links:
      - wbyy-mysql
  wbyy-modules-file:
    container_name: wbyy-modules-file
    build:
      context: ./wbyy/modules/file
      dockerfile: dockerfile
    ports:
      - "9300:9300"
    volumes:
    - ./wbyy/uploadPath:/home/<USER>/uploadPath
  wbyy-visual-monitor:
    container_name: wbyy-visual-monitor
    build:
      context: ./wbyy/visual/monitor
      dockerfile: dockerfile
    ports:
      - "9100:9100"

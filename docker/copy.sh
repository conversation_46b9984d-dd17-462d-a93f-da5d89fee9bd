#!/bin/sh

tag=$1
harborAddress=$2
harborRepo=$3


# 复制项目的文件到对应docker路径，便于一键生成镜像。
usage() {
	echo "Usage: sh copy.sh"
	exit 1
}

# copy jar
echo "begin copy wbyy-gateway "
cp ./wbyy-gateway/target/wbyy-gateway.jar ./docker/wbyy/gateway/
docker buildx build -t $harborAddress/$harborRepo/wbyy-gateway:$tag ./docker/wbyy/gateway


echo "begin copy wbyy-auth "
cp ./wbyy-auth/target/wbyy-auth.jar ./docker/wbyy/auth/
docker buildx build -t $harborAddress/$harborRepo/wbyy-auth:$tag ./docker/wbyy/auth

echo "begin copy wbyy-modules-system "
cp ./wbyy-modules/wbyy-system/target/wbyy-modules-system.jar ./docker/wbyy/modules/system/
docker buildx build -t $harborAddress/$harborRepo/wbyy-modules-system:$tag ./docker/wbyy/modules/system

echo "begin copy wbyy-modules-crm "
cp ./wbyy-modules/wbyy-crm/target/wbyy-modules-crm.jar ./docker/wbyy/modules/crm/
docker buildx build -t $harborAddress/$harborRepo/wbyy-modules-crm:$tag ./docker/wbyy/modules/crm

echo "begin copy wbyy-modules-pms "
cp ./wbyy-modules/wbyy-pms/target/wbyy-modules-pms.jar ./docker/wbyy/modules/pms/
docker buildx build -t $harborAddress/$harborRepo/wbyy-modules-pms:$tag ./docker/wbyy/modules/pms

echo "begin copy wbyy-modules-kingdee "
cp ./wbyy-modules/wbyy-kingdee/target/wbyy-modules-kingdee.jar ./docker/wbyy/modules/kingdee/
docker buildx build -t $harborAddress/$harborRepo/wbyy-modules-kingdee:$tag ./docker/wbyy/modules/kingdee

echo "begin copy wbyy-modules-gen "
cp ./wbyy-modules/wbyy-gen/target/wbyy-modules-gen.jar ./docker/wbyy/modules/gen/
docker buildx build -t $harborAddress/$harborRepo/wbyy-modules-gen:$tag ./docker/wbyy/modules/gen


echo "begin copy wbyy-visual "
cp ./wbyy-visual/wbyy-monitor/target/wbyy-visual-monitor.jar  ./docker/wbyy/visual/monitor/
docker buildx build -t $harborAddress/$harborRepo/wbyy-visual-monitor:$tag ./docker/wbyy/visual/monitor

echo "begin copy wbyy-modules-log "
cp ./wbyy-modules/wbyy-log/target/wbyy-modules-log.jar ./docker/wbyy/modules/log/
docker buildx build -t $harborAddress/$harborRepo/wbyy-modules-log:$tag ./docker/wbyy/modules/log

echo "begin copy wbyy-modules-weaver "
cp ./wbyy-modules/wbyy-weaver/target/wbyy-modules-weaver.jar ./docker/wbyy/modules/weaver/
docker buildx build -t $harborAddress/$harborRepo/wbyy-modules-weaver:$tag ./docker/wbyy/modules/weaver

echo "begin copy wbyy-modules-file "
cp ./wbyy-modules/wbyy-file/target/wbyy-modules-file.jar ./docker/wbyy/modules/file/
docker buildx build -t $harborAddress/$harborRepo/wbyy-modules-file:$tag ./docker/wbyy/modules/file

echo "begin copy wbyy-modules-thirdauth "
cp ./wbyy-modules/wbyy-thirdauth/target/wbyy-modules-thirdauth.jar ./docker/wbyy/modules/thirdauth/
docker buildx build -t $harborAddress/$harborRepo/wbyy-modules-thirdauth:$tag ./docker/wbyy/modules/thirdauth
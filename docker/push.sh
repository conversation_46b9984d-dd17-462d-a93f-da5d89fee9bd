#!/bin/sh

tag=$1
harborAddress=$2
harborRepo=$3
harborUser=$4
harborPasswd=$5

docker login https://harbor.wbcro.com -u $harborUser -p $harborPasswd

echo "docker push wbyy-auth "
docker push $harborAddress/$harborRepo/wbyy-auth:$tag

echo "docker push wbyy-gateway "
docker push $harborAddress/$harborRepo/wbyy-gateway:$tag

echo "docker push wbyy-modules-system "
docker push $harborAddress/$harborRepo/wbyy-modules-system:$tag

echo "docker push wbyy-modules-crm "
docker push $harborAddress/$harborRepo/wbyy-modules-crm:$tag

echo "docker push wbyy-modules-pms "
docker push $harborAddress/$harborRepo/wbyy-modules-pms:$tag

echo "docker push wbyy-modules-kingdee "
docker push $harborAddress/$harborRepo/wbyy-modules-kingdee:$tag

echo "docker push wbyy-modules-gen "
docker push $harborAddress/$harborRepo/wbyy-modules-gen:$tag

echo "docker push wbyy-visual-monitor "
docker push $harborAddress/$harborRepo/wbyy-visual-monitor:$tag

echo "docker push wbyy-modules-log "
docker push $harborAddress/$harborRepo/wbyy-modules-log:$tag

echo "docker push wbyy-modules-weaver "
docker push $harborAddress/$harborRepo/wbyy-modules-weaver:$tag

echo "docker push wbyy-modules-file "
docker push $harborAddress/$harborRepo/wbyy-modules-file:$tag

echo "docker push wbyy-modules-thirdauth "
docker push $harborAddress/$harborRepo/wbyy-modules-thirdauth:$tag

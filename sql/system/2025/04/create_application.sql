CREATE TABLE `application`
(
    `id`          bigint NOT NULL COMMENT 'id',
    `name`        varchar(128) NULL DEFAULT '' COMMENT '应用名称',
    `short_name`  varchar(64) NULL DEFAULT '' COMMENT '应用简称',
    `code`        varchar(64) NULL DEFAULT '' COMMENT '应用编码',
    `remark`      varchar(255) NULL DEFAULT '' COMMENT '备注',
    `order_sort`  int NULL DEFAULT 0 COMMENT '显示顺序',
    `status`      tinyint NULL DEFAULT 0 COMMENT '应用状态（0正常 1停用）',
    `del_flag`    tinyint(1) NULL DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    `create_by`   bigint NULL DEFAULT null COMMENT '创建者',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   bigint NULL DEFAULT null COMMENT '更新者',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT = '应用表';

package com.wbyy.common.redis.aspect;

import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.SpelUtils;
import com.wbyy.common.redis.annotation.RedisLock;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
@Slf4j
@AllArgsConstructor
@Order(-9999)  // 重要 数值越小优先级越高，确保早于 @Transactional 切面执行
@ConditionalOnProperty(name = "redisson.enabled", havingValue = "true")
public class RedisLockAspect {

    private final RedissonClient redissonClient;


    @Around("@annotation(redisLock)")
    public Object around(ProceedingJoinPoint joinPoint, RedisLock redisLock) throws Throwable {
        // 构造锁 key
        String prefix = redisLock.prefix();
        String keySpEL = redisLock.key();

        String lockKey;
        if (StrUtil.isBlank(keySpEL)) {
            // 只配置了 prefix，就用 prefix 作为完整 key
            lockKey = prefix;
        } else {
            // 有 SPEL 表达式，解析后拼接
            Object keyValue;
            try {
                Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
                Object[] args = joinPoint.getArgs();
                keyValue = SpelUtils.evaluate(keySpEL,method,args);
            }catch (Exception e){
                log.warn("SPEL【{}】解析报错",keySpEL,e);
                keyValue = keySpEL;
            }
            lockKey = prefix + (keyValue != null ? keyValue.toString() : "");
        }

        long waitTime = redisLock.waitTime();
        long leaseTime = redisLock.leaseTime();
        TimeUnit timeUnit = redisLock.timeUnit();

        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;

        try {
            locked = lock.tryLock(waitTime, leaseTime, timeUnit);
            if (!locked) {
                log.warn("获取锁失败：{}", lockKey);
                throw new ServiceException("获取锁失败：" + lockKey);
            }

            return joinPoint.proceed();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取锁时被中断：{}", lockKey, e);
            throw new ServiceException("操作中断", e);
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}

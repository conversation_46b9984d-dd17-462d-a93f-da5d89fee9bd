package com.wbyy.common.redis.service;

import com.alibaba.fastjson2.JSONArray;
import com.wbyy.common.core.constant.CacheConstants;
import com.wbyy.common.core.utils.SpringUtils;
import com.wbyy.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

@Component
public class CommonCacheService<T> {
    @Autowired
    private RedisService redisService;

    /**
     * 设置字典缓存
     *
     * @param key 参数键
     * @param datas 字典数据列表
     */
    public void setDictCache(String keyPrefix, String key, List<T> datas)
    {
        redisService.setCacheObject(keyPrefix  + key, datas);
    }

    /**
     * 获取字典缓存
     *
     * @param key 参数键
     * @return dictDatas 字典数据列表
     */
    public List<T> getDictCache(String keyPrefix, String key, Class<T> tClass)
    {
        JSONArray arrayCache = redisService.getCacheObject(keyPrefix + key);
        if (StringUtils.isNotNull(arrayCache))
        {
            return arrayCache.toList(tClass);
        }
        return null;
    }

    /**
     * 删除指定字典缓存
     *
     * @param key 字典键
     */
    public void removeDictCache(String keyPrefix, String key)
    {
        redisService.deleteObject(keyPrefix  + key);
    }

    /**
     * 清空字典缓存
     */
    public void clearDictCache(String keyPrefix)
    {
        Collection<String> keys = redisService.keys(keyPrefix + "*");
        redisService.deleteObject(keys);
    }
}

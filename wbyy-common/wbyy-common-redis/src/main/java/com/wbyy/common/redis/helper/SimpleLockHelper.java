package com.wbyy.common.redis.helper;

import com.wbyy.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * @author: 王金都
 * @date: 2025/5/19 14:42
 */
@Component
@AllArgsConstructor
@ConditionalOnProperty(name = "redisson.enabled", havingValue = "true")
public class SimpleLockHelper {

    private final RedissonClient redissonClient;

    /**
     * @Description: 执行加锁逻辑
     * @param lockKey 锁的key
     * @param waitTime 加锁等待时间
     * @param leaseTime 持有锁时间
     * @param unit 时间单位
     * @param action 业务逻辑
     * @return: T
     * @author: 王金都
     * @Date: 2025/5/19 14:57
     */
    public <T> T execute(String lockKey, long waitTime, long leaseTime, TimeUnit unit, Supplier<T> action) {
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        T result;
        try {
            locked = lock.tryLock(waitTime, leaseTime, unit);
            if (!locked) throw new ServiceException("获取锁失败");
            result = (T) action.get();
            return result;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ServiceException("操作中断", e);
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * @Description: 执行加锁逻辑
     * @param lockKey 锁的key
     * @param action 业务逻辑
     * @return: T
     * @author: 王金都
     * @Date: 2025/5/19 14:57
     */
    public <T> T execute(String lockKey, Supplier<T> action){
        return execute(lockKey,10L,30L,TimeUnit.SECONDS,action);
    }
}

package com.wbyy.common.redis.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * @author: 王金都
 * @date: 2025/5/19 15:00
 */
@Target({ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RedisLock {
    /**
     * 锁的前缀
     */
    String prefix();

    /**
     * 锁的 key（支持 SPEL 表达式）
     */
    String key() default "";

    /**
     * 最多等待时间，默认5
     */
    long waitTime() default 5;

    /**
     * 锁的持有时间，默认20
     */
    long leaseTime() default 20;

    /**
     * 时间单位，默认秒
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;
}

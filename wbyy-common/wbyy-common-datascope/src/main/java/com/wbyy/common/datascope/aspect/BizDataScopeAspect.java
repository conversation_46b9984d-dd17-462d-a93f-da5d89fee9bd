package com.wbyy.common.datascope.aspect;

import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.context.SecurityContextHolder;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.ServletUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.datascope.annotation.BizDataScope;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.system.api.PermissionApi;
import com.wbyy.system.api.domain.ApiPermissionSql;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.api.model.ApiBaseEntity;
import com.wbyy.system.api.model.LoginUser;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.wbyy.common.core.constant.ScopeConst.DATA_SCOPE;


/**
 * 数据过滤处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class BizDataScopeAspect {

    @Autowired
    private PermissionApi permissionApi;

    @Before("@annotation(bizDataScope)")
    public void doBefore(JoinPoint point, BizDataScope bizDataScope) throws Throwable {
        clearDataScope(point);
        handleDataScope(point, bizDataScope);
    }

    protected void handleDataScope(final JoinPoint joinPoint, BizDataScope bizDataScope) {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNotNull(loginUser)) {
            ApiUser currentUser = loginUser.getSysUser();
            // 如果是超级管理员，则不过滤数据
            if (StringUtils.isNotNull(currentUser)
                    && !currentUser.isCheckAdmin()) {
                String permission = StringUtils.defaultIfEmpty(bizDataScope.permission(), SecurityContextHolder.getPermission());
                dataScopeFilter(joinPoint,
                        bizDataScope.deptAlias(),
                        bizDataScope.userAlias(),
                        permission);
            }
        }
    }

    /**
     * 数据范围过滤
     *
     * @param joinPoint  切点
     * @param deptAlias  部门别名
     * @param userAlias  用户别名
     * @param permission 权限字符
     */
    public void dataScopeFilter(JoinPoint joinPoint,
                                String deptAlias,
                                String userAlias,
                                String permission) {
        HttpServletRequest httpServletRequest = ServletUtils.getRequest();
        if (StringUtils.isNotNull(httpServletRequest)) {
            R<String> permissionSqlResData = permissionApi.getPermissionSql(
                    ApiPermissionSql.builder()
                            .deptAlias(deptAlias)
                            .userAlias(userAlias)
                            .permission(permission)
                            .build(), SecurityConstants.INNER);
            if (permissionSqlResData.getCode() != 200){
                throw new ServiceException("获取权限失败，请稍后再试或联系管理员");
            }
            if (StrUtil.isNotEmpty(permissionSqlResData.getMsg())){
                throw new ServiceException(permissionSqlResData.getMsg());
            }
            String sqlString = permissionSqlResData.getData();
            if (StringUtils.isNotBlank(sqlString)) {
                Object params = joinPoint.getArgs()[0];
                if (StringUtils.isNotNull(params) && params instanceof ApiBaseEntity baseEntity) {
                    log.info("【权限SQL】 - {}", sqlString);
                    baseEntity.getParams().put(DATA_SCOPE, sqlString);
                }
            }
        } else {
            throw new ServiceException("权限有误，请联系管理员");
        }
    }

    /**
     * 拼接权限sql前先清空params.dataScope参数防止注入
     */
    private void clearDataScope(final JoinPoint joinPoint) {
        Object params = joinPoint.getArgs()[0];
        if (StringUtils.isNotNull(params) && params instanceof ApiBaseEntity) {
            ApiBaseEntity baseEntity = (ApiBaseEntity) params;
            baseEntity.getParams().put(DATA_SCOPE, "");
        }
    }
}

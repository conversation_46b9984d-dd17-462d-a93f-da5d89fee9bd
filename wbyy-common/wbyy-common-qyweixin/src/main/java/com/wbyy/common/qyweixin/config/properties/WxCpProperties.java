package com.wbyy.common.qyweixin.config.properties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Component
@ConfigurationProperties(prefix = "wechat.cp")
public class WxCpProperties {
    /**
     * 设置微信企业号的corpId
     */
    private String corpId;
    /**
     * 发消息用的 AgentId
     */
    private Integer sendMessageAgentId;

    /**
     * 各个应用单独的配置
     */
    private List<AppConfig> appConfigs;
}

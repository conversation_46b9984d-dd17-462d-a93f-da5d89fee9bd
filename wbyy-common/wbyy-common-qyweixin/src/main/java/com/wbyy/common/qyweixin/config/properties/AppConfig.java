package com.wbyy.common.qyweixin.config.properties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppConfig {
    /**
     * 设置微信企业应用的AgentId
     */
    private Integer agentId;

    /**
     * 设置微信企业应用的Secret
     */
    private String secret;

    /**
     * 设置微信企业号的token
     */
    private String token;


    private String aesKey;

    /**
     * 设置微信企业号的EncodingAESKey
     */
    private String encodingAesKey;

    /**
     * 应用编码pms-app、pms-web、message
     */
    private String agentCode;
}

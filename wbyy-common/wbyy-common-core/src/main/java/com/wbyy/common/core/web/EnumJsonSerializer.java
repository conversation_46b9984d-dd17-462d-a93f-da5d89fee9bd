package com.wbyy.common.core.web;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public class EnumJsonSerializer extends JsonSerializer<Enum<?>> {

    @Override
    public void serialize(Enum<?> value, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        // 直接输出枚举的名称作为字符串
        gen.writeString(value.name().toLowerCase());
    }
}

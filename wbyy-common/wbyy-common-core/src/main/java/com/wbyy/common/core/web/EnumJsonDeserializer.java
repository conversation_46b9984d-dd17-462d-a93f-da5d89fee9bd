package com.wbyy.common.core.web;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public class EnumJsonDeserializer extends JsonDeserializer<Enum<?>> {
    private final Class<? extends Enum<?>> enumType;

    public EnumJsonDeserializer(Class<? extends Enum<?>> enumType) {
        this.enumType = enumType;
    }

    @Override
    public Enum<?> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        // 从JSON读取字符串并转换为大写，然后查找枚举值
        String value = p.getText();
        // 使用Enum类的valueOf方法获取枚举实例
        for (Enum<?> e : enumType.getEnumConstants()) {
            if (e.name().equalsIgnoreCase(value)) {
                return e;
            }
        }
        throw ctxt.weirdStringException(value, enumType,
                "Invalid enum value: " + value);
    }
}

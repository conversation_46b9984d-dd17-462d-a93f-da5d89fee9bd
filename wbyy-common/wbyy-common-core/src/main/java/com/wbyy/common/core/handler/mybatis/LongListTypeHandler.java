package com.wbyy.common.core.handler.mybatis;


import com.alibaba.fastjson2.TypeReference;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/31 09:50
 */
@MappedTypes(List.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class LongListTypeHandler extends ListTypeHandler<Long>{
    @Override
    protected TypeReference<List<Long>> specificType() {
        return new TypeReference<>() {
        };
    }
}

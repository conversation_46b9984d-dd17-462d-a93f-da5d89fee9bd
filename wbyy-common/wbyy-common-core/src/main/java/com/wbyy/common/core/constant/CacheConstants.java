package com.wbyy.common.core.constant;

/**
 * 缓存常量信息
 * 
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 缓存有效期，默认一周
     */
    public final static long EXPIRATION = 7*24*60;

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;

    /**
     * 密码最大错误次数
     */
    public final static int PASSWORD_MAX_RETRY_COUNT = 5;

    /**
     * 密码锁定时间，默认10（分钟）
     */
    public final static long PASSWORD_LOCK_TIME = 10;

    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";


    /**
     * PMS 参数管理 cache key
     */
    public static final String PMS_SYS_CONFIG_KEY = "pms:sys_config:";

    /**
     * 系统管理 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";
    /**
     * 项目管理系统 字典管理 cache key
     */
    public static final String PMS_DICT_KEY = "pms_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 登录IP黑名单 cache key
     */
    public static final String SYS_LOGIN_BLACKIPLIST = SYS_CONFIG_KEY + "sys.login.blackIPList";

    /**
     * 聚合型用户token缓存，userid:<uuid : appcode,3434392043>
     */
    public final static String LOGIN_TOKEN_AGGREGATION_KEY = "login_tokens_aggregation:";

    /**
     * rsa私钥key
     */
    public static final String RSA_PRIVATE_KEY="RSA:PRIVATE:KEY:%s";

    /**
     * 获取公钥限流key
     */
    public static final String RATE_LIMIT_PUBLIC_KEY = "RATE:LIMIT:PUBLIC:KEY:%s";
}

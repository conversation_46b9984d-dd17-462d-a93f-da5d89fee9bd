package com.wbyy.common.core.web.page;

import com.wbyy.common.core.constant.HttpStatus;
import lombok.Data;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 表格分页数据对象
 * 
 * <AUTHOR>
 */
@Data
public class TableDataInfo<T> implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 总记录数 */
    private int total;

    /** 列表数据 */
    private List<T> rows;

    /** 消息状态码 */
    private int code;

    /** 消息内容 */
    private String msg;

    /**
     * 表格数据对象
     */
    public TableDataInfo()
    {
    }

    /**
     * 分页
     *
     * @param list 列表数据
     * @param total 总记录数
     */
    public TableDataInfo(List<T> list, int total)
    {

        this.rows = list;
        this.total = total;
    }

    public long getTotal()
    {
        return total;
    }

    /**
     * 降级默认构造方法
     * @param errorMsg 错误信息
     */
    public TableDataInfo(String errorMsg)
    {
        this.msg = errorMsg;
        this.code = HttpStatus.ERROR;
    }
}
package com.wbyy.common.core.utils;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 制作属性数据工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class TreeUtil {

    public static JSONArray buildTree(JSONArray array) {
        return buildTree(array, "id", "parentId", "children");
    }

    public static <T> List<T> buildTree(JSONArray array, Class<T> clazz) {
        return buildTree(array, "id", "parentId", "children", clazz);
    }

    /**
     * @param array        需要构造的 源数据
     * @param idKeyName    主键字段名
     * @param pidKeyName   父ID的字段名
     * @param childKeyName 子节点的集合名称
     * @return
     */
    public static JSONArray buildTree(JSONArray array, String idKeyName, String pidKeyName, String childKeyName) {
        //新建一个JSONArray来接收组装成树形结构的返回值
        JSONArray jsonArray = new JSONArray();
        //新建一个JSONObject对象
        JSONObject hash = new JSONObject();
        //将数组转换为object格式
        for (int i = 0; i < array.size(); i++) {
            //获取当前的JSON对象
            JSONObject json = (JSONObject) array.get(i);
            //把当前id作为键，当前JSON对象作为值 put回hash这个Object对象中
            //这里的put方法类似于map的put方法
            hash.put(json.getString(idKeyName), json);
        }
        //遍历结果集
        for (int j = 0; j < array.size(); j++) {
            //单条记录
            JSONObject aVal = (JSONObject) array.get(j);
            //在hash中取出key为单条记录中pid的值
            String pidStr = "";
            //如果父级id不等于null
            if (aVal.get(pidKeyName) != null) {
                pidStr = aVal.get(pidKeyName).toString();
            }
            //从hash这个对象中获取父级对象  parent
            JSONObject hashParent = (JSONObject) hash.get(pidStr);
            //如果记录的pid存在，则说明它有父节点，将她添加到孩子节点的集合中
            if (hashParent != null) {
                //检查是否有child属性
                if (hashParent.get(childKeyName) != null) {
                    //有子节点 则先将子节点取出
                    JSONArray children = (JSONArray) hashParent.get(childKeyName);
                    //然后把当前这个对象放进子节点之中
                    children.add(aVal);
                    //最后把子节点在放回父节点之中
                    hashParent.put(childKeyName, children);
                } else {
                    //无子节点 则新建一个子节点
                    JSONArray children = new JSONArray();
                    //然后再把当前对象放进去
                    children.add(aVal);
                    //最后在放回父节点之中
                    hashParent.put(childKeyName, children);
                }
            } else {
                jsonArray.add(aVal);
            }
        }
        return jsonArray;

    }

    /**
     * 构造 树形结构数据
     *
     * @param array        需要构造的 源数据
     * @param idKeyName    主键字段名
     * @param pidKeyName   父ID的字段名
     * @param childKeyName 子节点的集合名称
     * @return
     */
    public static <T> List<T> buildTree(JSONArray array, String idKeyName, String pidKeyName, String childKeyName, Class<T> clazz) {
        JSONArray jsonArray = buildTree(array, idKeyName, pidKeyName, childKeyName);
        return jsonArray.toJavaList(clazz);
    }

}


package com.wbyy.common.core.utils.security;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.OAEPParameterSpec;
import javax.crypto.spec.PSource;
import java.security.*;
import java.security.spec.MGF1ParameterSpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2025/7/15 13:56
 */
@Slf4j
public class RSAUtils {

    // 算法和密钥配置
    private static final String ALGORITHM = "RSA/ECB/OAEPWithSHA-256AndMGF1Padding";
    private static final int KEY_SIZE = 2048;

    // 单例 KeyPairGenerator
    private static final KeyPairGenerator KEY_PAIR_GENERATOR;

    // ThreadLocal Cipher 实例（每个线程独立）
    private static final ThreadLocal<Cipher> CIPHER_THREAD_LOCAL = ThreadLocal.withInitial(() -> {
        try {
            return Cipher.getInstance(ALGORITHM);
        } catch (Exception e) {
            throw new RuntimeException("初始化Cipher失败", e);
        }
    });

    static {
        try {
            // 初始化密钥生成器，使用SecureRandom增强随机性
            KEY_PAIR_GENERATOR = KeyPairGenerator.getInstance("RSA");
            KEY_PAIR_GENERATOR.initialize(KEY_SIZE, new SecureRandom());

            log.debug("RSAUtils 初始化成功");
        } catch (Exception e) {
            throw new RuntimeException("初始化RSA工具失败", e);
        }
    }

    /**
     * 获取秘钥对
     *
     * @return KeyPair
     */
    public static KeyPair generateKeyPair() {
        return KEY_PAIR_GENERATOR.generateKeyPair();
    }

    /**
     * 公钥转Base64字符串
     *
     * @param publicKey
     * @return String
     * <AUTHOR>
     * @date 2025/7/15 14:38
     */
    public static String publicKeyToBase64(PublicKey publicKey) {
        return Base64.getEncoder().encodeToString(publicKey.getEncoded());
    }

    /**
     * 私钥转Base64字符串
     *
     * @param privateKey
     * @return String
     * <AUTHOR>
     * @date 2025/7/15 14:39
     */
    public static String privateKeyToBase64(PrivateKey privateKey) {
        return Base64.getEncoder().encodeToString(privateKey.getEncoded());
    }

    /**
     * Base64字符串转公钥对象
     *
     * @param base64PublicKey
     * @return PublicKey
     * <AUTHOR>
     * @date 2025/7/15 14:39
     */
    public static PublicKey base64ToPublicKey(String base64PublicKey) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(base64PublicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * Base64字符串转私钥对象
     *
     * @param base64PrivateKey
     * @return PrivateKey
     * <AUTHOR>
     * @date 2025/7/15 14:42
     */
    public static PrivateKey base64ToPrivateKey(String base64PrivateKey) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(base64PrivateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * RSA加密
     *
     * @param plainText
     * @param publicKey
     * @return String
     */
    public static String encrypt(String plainText, PublicKey publicKey) throws Exception {
        Cipher cipher = CIPHER_THREAD_LOCAL.get();
        OAEPParameterSpec oaepParams = new OAEPParameterSpec(
                "SHA-256",
                "MGF1",
                MGF1ParameterSpec.SHA256,
                PSource.PSpecified.DEFAULT
        );
        cipher.init(Cipher.ENCRYPT_MODE, publicKey, oaepParams);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * RSA解密
     *
     * @param encryptedText
     * @param privateKey
     * @return String
     */
    public static String decrypt(String encryptedText, PrivateKey privateKey) throws Exception {
        Cipher cipher = CIPHER_THREAD_LOCAL.get();
        OAEPParameterSpec oaepParams = new OAEPParameterSpec(
                "SHA-256",
                "MGF1",
                MGF1ParameterSpec.SHA256,
                PSource.PSpecified.DEFAULT
        );
        cipher.init(Cipher.DECRYPT_MODE, privateKey, oaepParams);
        byte[] encryptedBytes = Base64.getDecoder().decode(encryptedText);
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes);
    }

    /**
     * 清理当前线程的Cipher资源
     */
    public static void clearThreadResources() {
        CIPHER_THREAD_LOCAL.remove();
    }

    public static KeyPairInfo generateKeyPairInfoAndSafeGet() {
        try {
            return generateBase64KeyPairInfo();
        } finally {
            clearThreadResources();
        }
    }

    /**
     * 生成密钥对并返回Base64编码的公钥和私钥
     *
     * @param
     * @return KeyPairInfo
     * <AUTHOR>
     * @date 2025/7/15 14:43
     */
    public static KeyPairInfo generateBase64KeyPairInfo() {
        KeyPair keyPair = generateKeyPair();
        return new KeyPairInfo(
                publicKeyToBase64(keyPair.getPublic()),
                privateKeyToBase64(keyPair.getPrivate())
        );
    }

    /**
     * 密钥对信息封装类
     */
    @Getter
    public static class KeyPairInfo {
        private final String publicKey;
        private final String privateKey;

        public KeyPairInfo(String publicKey, String privateKey) {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

    }
}
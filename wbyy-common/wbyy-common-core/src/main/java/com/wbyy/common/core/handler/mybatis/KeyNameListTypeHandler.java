package com.wbyy.common.core.handler.mybatis;


import com.alibaba.fastjson2.TypeReference;
import com.wbyy.common.core.handler.mybatis.bo.KeyNameBO;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * <AUTHOR>
 */
@MappedTypes(List.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class KeyNameListTypeHandler extends ListTypeHandler<KeyNameBO>{

    @Override
    protected TypeReference<List<KeyNameBO>> specificType() {
        return new TypeReference<List<KeyNameBO>>() {
        };
    }
}

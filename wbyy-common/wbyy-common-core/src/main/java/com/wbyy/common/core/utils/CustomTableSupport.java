package com.wbyy.common.core.utils;

import com.wbyy.common.core.text.Convert;
import com.wbyy.common.core.web.page.PageDomain;
import com.wbyy.common.core.web.page.TableSupport;

/**
 * <AUTHOR>
 * @date 2025/6/17  13:50
 * @description 扩展表格处理对象
 */
public class CustomTableSupport extends TableSupport {

    /**
     * 支持默认排序
     * @param orderByColumn 排序字段
     * @param isAsc 排序方式
     * @return PageDomain
     */
    public static PageDomain buildPageRequest(String orderByColumn,String isAsc) {
        PageDomain pageDomain = new PageDomain();
        pageDomain.setPageNum(Convert.toInt(ServletUtils.getParameter(PAGE_NUM), 1));
        pageDomain.setPageSize(Convert.toInt(ServletUtils.getParameter(PAGE_SIZE), 10));
        pageDomain.setOrderByColumn(StringUtils.isBlank(ServletUtils.getParameter(ORDER_BY_COLUMN)) ? orderByColumn : ServletUtils.getParameter(ORDER_BY_COLUMN));
        pageDomain.setIsAsc(StringUtils.isBlank(ServletUtils.getParameter(IS_ASC)) ? isAsc : ServletUtils.getParameter(IS_ASC));
        pageDomain.setReasonable(ServletUtils.getParameterToBool(REASONABLE));
        return pageDomain;
    }
}

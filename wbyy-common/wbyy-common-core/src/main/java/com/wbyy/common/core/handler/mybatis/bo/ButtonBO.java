package com.wbyy.common.core.handler.mybatis.bo;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ButtonBO implements Serializable {
    @Serial
    private static final long serialVersionUID = 3606173434128824579L;

    /**
     * 按钮文案，建议不超过10个字
     */
    @Size(max = 10, message = "按钮文案，建议不超过10个字")
    private String text;

    /**
     * 按钮样式，目前可填1~4，不填或错填默认1
     */
    @Size(min = 1, max = 4, message = "按钮样式，目前可填1~4，不填或错填默认1")
    private Integer style;

    /**
     * 按钮key值，用户点击后，会产生回调事件将本参数作为EventKey返回，
     * 回调事件会带上该key值，最长支持1024字节，不可重复
     */
    @Size(max = 1024, message = "按钮key值，用户点击后，会产生回调事件将本参数作为EventKey返回，回调事件会带上该key值，最长支持1024字节，不可重复")
    private String key;

    /**
     * 按钮点击事件类型，0 或不填代表回调点击事件，1 代表跳转url
     */
    @Size(max = 1, message = "按钮点击事件类型，0 或不填代表回调点击事件，1 代表跳转url")
    private Integer type;

    /**
     * 跳转事件的url，button_list.type是1时必填
     */
    private String url;

}

package com.wbyy.common.core.handler.mybatis;

import com.alibaba.fastjson2.TypeReference;
import com.wbyy.common.core.handler.mybatis.bo.ButtonBO;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * <AUTHOR>
 */
@MappedTypes(List.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class ButtonListTypeHandler extends ListTypeHandler<ButtonBO>{
    @Override
    protected TypeReference<List<ButtonBO>> specificType() {
        return new TypeReference<List<ButtonBO>>() {};
    }
}

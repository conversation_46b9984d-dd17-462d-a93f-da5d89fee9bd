package com.wbyy.common.core.utils.pool;

import com.wbyy.common.core.utils.SpringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Future;
import java.util.function.Supplier;

/**
 * 线程池工具类
 *
 * @date  2025-06-07
 * <AUTHOR>
 */
public class FutureUtils {

    /**
     * 异步任务
     *
     * @param supplier  线程任务
     * @return Future
     * @param <T>   返回值类型
     */
    public static <T> Future<T> taskAsync(String poolName, Supplier<T> supplier) {
        return getThreadPoolTaskExecutor(poolName).submit(supplier::get);
    }

    /**
     * 获取线程池
     * @return ThreadPoolTaskExecutor
     */
    private static ThreadPoolTaskExecutor getThreadPoolTaskExecutor(String poolName){
        return SpringUtils.getBean(poolName);
    }
}

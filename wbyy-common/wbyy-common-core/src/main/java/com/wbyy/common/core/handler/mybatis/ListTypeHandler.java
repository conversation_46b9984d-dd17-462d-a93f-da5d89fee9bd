package com.wbyy.common.core.handler.mybatis;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;
import java.util.List;

/**
 * List泛型处理器
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class ListTypeHandler<T> extends BaseTypeHandler<List<T>> {
    /**
     * 具体类型,由子类实现
     *
     * @return 具体类型
     */
//    protected abstract Class<T> getType();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<T> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter == null) {
            ps.setNull(i, Types.VARCHAR);
            return;
        }
        String json = JSONArray.toJSONString(parameter);
        ps.setString(i, json);
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getResult(rs.getString(columnName));
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getResult(rs.getString(columnIndex));
    }

    @Override
    public List<T> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getResult(cs.getString(columnIndex));
    }

    /**
     * 根据json字符串格式化成List
     */
    private List<T> getResult(String context) {
        if (StrUtil.isEmpty(context)) return null;
        try {
            return JSON.parseObject(context, this.specificType());
        } catch (Exception e) {
            log.error("【-{}-】JSON转换失败，类型：{}，错误描述：{}", context, specificType(), e.getMessage());
//            String content = context.trim();
//            if (content.startsWith("[")) content = content.substring(1);
//            if (content.endsWith("]")) content = content.substring(0, content.length() - 1);
//            content = content.trim();
//            return (List<T>) Arrays.stream(content.split(","))
//                    .map(String::trim)
//                    .map(s -> s.replace("\"", ""))
//                    .toList();
            return null;
        }
    }

    /**
     * 具体类型，由子类提供
     *
     * @return 具体类型
     */
    protected abstract TypeReference<List<T>> specificType();
}

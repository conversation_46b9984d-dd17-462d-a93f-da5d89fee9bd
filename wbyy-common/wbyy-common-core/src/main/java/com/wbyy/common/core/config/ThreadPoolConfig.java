package com.wbyy.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/17  14:56
 * @description 线程池配置化文件
 */
@ConfigurationProperties
@Component
@Data
public class ThreadPoolConfig {

    private Map<String,Config> threadPool;

    @Data
    public static class Config{
        private Integer coreSize = Runtime.getRuntime().availableProcessors();
        private Integer maxSize = Runtime.getRuntime().availableProcessors() * 2;
        private Integer keepAliveTime = 60;
        private String threadPrefix = "thread-pool-default";
        private Integer queueCapacity = 1000;
        private Integer awaitTerminationSeconds = 30;

        public Config mergeWithDefault(Config defaultConfig) {
            if (this.coreSize == null) this.coreSize = defaultConfig.coreSize;
            if (this.maxSize == null) this.maxSize = defaultConfig.maxSize;
            if (this.keepAliveTime == null) this.keepAliveTime = defaultConfig.keepAliveTime;
            if (this.threadPrefix == null) this.threadPrefix = defaultConfig.threadPrefix;
            if (this.queueCapacity == null) this.queueCapacity = defaultConfig.queueCapacity;
            if (this.awaitTerminationSeconds == null) this.awaitTerminationSeconds = defaultConfig.awaitTerminationSeconds;
            return this;
        }
    }

}

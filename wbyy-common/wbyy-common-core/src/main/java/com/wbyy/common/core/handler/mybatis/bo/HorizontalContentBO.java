package com.wbyy.common.core.handler.mybatis.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HorizontalContentBO implements Serializable {
    @Serial
    private static final long serialVersionUID = 3606173434128824579L;

    /**
     * 描述
     */
    private String name;
    /**
     * 链接类型，0或不填代表不是链接，1 代表跳转url，2 代表下载附件
     */
    private String type;
    /**
     * 二级标题，建议不超过5个字
     */
    private String keyname;
    /**
     * 二级文本，如果horizontal_content_list.type是2，该字段代表文件名称（要包含文件类型），建议不超过30个字
     */
    private String value;
    /**
     * 链接跳转的url，horizontal_content_list.type是1时必填
     */
    private String url;
    /**
     * 附件的media_id，horizontal_content_list.type是2时必填
     */
    private String media_id;

    /**
     * 成员详情的userid，horizontal_content_list.type是3时必填
     */
    private String userid;

}

          ${AnsiColor.BRIGHT_YELLOW}=================================================
          ||                   ${AnsiColor.BRIGHT_RED}_ooOoo_                   ${AnsiColor.BRIGHT_YELLOW}||
          ||                  ${AnsiColor.BRIGHT_RED}o8888888o                  ${AnsiColor.BRIGHT_YELLOW}||
          ||                  ${AnsiColor.BRIGHT_RED}88${AnsiColor.BRIGHT_YELLOW}" . "${AnsiColor.BRIGHT_RED}88                  ${AnsiColor.BRIGHT_YELLOW}||
          ||                  (| -_- |)                  ||  ${AnsiColor.BRIGHT_YELLOW}
          ||                  O\  =  /O                  ||  ${AnsiColor.BRIGHT_YELLOW}
          ||               ____/`---'\____               ||  ${AnsiColor.BRIGHT_YELLOW}
          ||             .'  \\|     |//  `.             ||  ${AnsiColor.BRIGHT_YELLOW}
          ||            /  \\|||  :  |||//  \            ||  ${AnsiColor.BRIGHT_YELLOW}
          ||           /  _||||| -:- |||||-  \           ||  ${AnsiColor.BRIGHT_YELLOW}
          ||           |   | \\\  -  /// |   |           ||  ${AnsiColor.BRIGHT_YELLOW}
          ||           | \_|  ''\---/''  |   |           ||  ${AnsiColor.BRIGHT_YELLOW}
          ||           \  .-\__  `-`  ___/-. /           ||  ${AnsiColor.BRIGHT_YELLOW}
          ||         ___`. .'  /--.--\  `. . __          ||  ${AnsiColor.BRIGHT_YELLOW}
          ||      ."" '<  `.___\_<|>_/___.'  >'"".       ||  ${AnsiColor.BRIGHT_YELLOW}
          ||     | | :  `- \`.;`\ _ /`;.`/ - ` : | |     ||  ${AnsiColor.BRIGHT_YELLOW}
          ||     \  \ `-.   \_ __\ /__ _/   .-` /  /     ||  ${AnsiColor.BRIGHT_YELLOW}
          ||======'-.____`-.___\_____/___.-`____.-'======||  ${AnsiColor.BRIGHT_YELLOW}
          ||                   '=---='                   ||  ${AnsiColor.BRIGHT_YELLOW}
          ||^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^||  ${AnsiColor.BRIGHT_YELLOW}
          ||^        佛祖保佑             永无BUG        ^||  ${AnsiColor.BRIGHT_YELLOW}
          ||^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^||  ${AnsiColor.BRIGHT_YELLOW}
          =================================================
          ${AnsiColor.BRIGHT_RED}          SpringBoot Version ${spring-boot.formatted-version} ${AnsiColor.BRIGHT_YELLOW}
          -------------------------------------------------
          ${AnsiColor.DEFAULT}
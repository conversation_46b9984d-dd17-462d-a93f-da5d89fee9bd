package com.wbyy.common.security.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.TokenConstants;
import com.wbyy.common.core.context.SecurityContextHolder;
import com.wbyy.common.core.utils.ServletUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.system.api.domain.ApiRole;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.api.model.LoginUser;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.wbyy.common.core.constant.ScopeConst.DATA_SCOPE_DEPT_AND_CHILD;

/**
 * 权限获取工具类
 * 
 * <AUTHOR>
 */
public class SecurityUtils
{

    public static final String ADMIN_ROLE = "admin";

    /**
     * 获取用户ID
     */
    public static Long getUserId()
    {
        return SecurityContextHolder.getUserId();
    }

    /**
     * 获取用户名称
     */
    public static String getUsername()
    {
        return SecurityContextHolder.getUserName();
    }

    /**
     * 获取用户key
     */
    public static String getUserKey()
    {
        return SecurityContextHolder.getUserKey();
    }

    /**
     * 获取登录用户信息
     */
    public static LoginUser getLoginUser()
    {
        return SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
    }

    public static void setLoginUser(LoginUser loginUser)
    {
        SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
    }

    /**
     * 获取请求token
     */
    public static String getToken()
    {
        return getToken(ServletUtils.getRequest());
    }

    /**
     * 根据request获取请求token
     */
    public static String getToken(HttpServletRequest request)
    {
        // 从header获取token标识
        String token = request.getHeader(TokenConstants.AUTHENTICATION);
        return replaceTokenPrefix(token);
    }

    /**
     * 裁剪token前缀
     */
    public static String replaceTokenPrefix(String token)
    {
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX))
        {
            token = token.replaceFirst(TokenConstants.PREFIX, "");
        }
        return token;
    }

    /**
     * 是否为管理员
     *
     * @return 结果
     */
    public static boolean isAdmin()
    {
        LoginUser loginUser = getLoginUser();
        if (null==loginUser) return false;
        ApiUser sysUser = loginUser.getSysUser();
        if (null==sysUser) return false;
        List<ApiRole> roles = sysUser.getRoles();
        if (CollectionUtil.isEmpty(roles)) return false;
        return roles.stream().anyMatch(item->ADMIN_ROLE.equals(item.getRoleKey()));
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword 真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * @Description: 获取用户管理部门权限范围-所在部门及子部门
     * @param
     * @return: Set<Long>
     * @author: 王金都
     * @Date: 2025/5/21 15:28
     */
    public static Set<Long> getUserManageDepts(){
        LoginUser loginUser = getLoginUser();
        List<ApiRole> roles = loginUser.getSysUser().getRoles();
        if (CollectionUtil.isEmpty(roles)) return new HashSet<>();
        // 角色具有部门领导的数据权限
        if (roles.stream().anyMatch(item->DATA_SCOPE_DEPT_AND_CHILD == item.getDataScope())){
            return loginUser.getSonDepts();
        }
        return new HashSet<>();
    }

    /**
     * @Description: 获取用户部门id
     * @param
     * @return: Long
     * @author: 王金都
     * @Date: 2025/6/11 17:19
     */
    public static Long getDeptId()
    {
        LoginUser loginUser = getLoginUser();
        return getDeptId(loginUser);
    }

    /**
     * @Description: 获取用户部门id
     * @param
     * @return: Long
     * @author: 王金都
     * @Date: 2025/6/11 17:19
     */
    public static Long getDeptId(LoginUser loginUser)
    {
        if (null==loginUser) return null;
        Set<Long> userDepts = loginUser.getDepts();
        if (CollectionUtil.isEmpty(userDepts)) return null;
        return userDepts.iterator().next();
    }
}

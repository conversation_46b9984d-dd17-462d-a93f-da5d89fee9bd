package com.wbyy.common.security.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.context.SecurityContextHolder;
import com.wbyy.common.core.utils.ServletUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.security.auth.AuthUtil;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.system.api.model.LoginUser;
import org.slf4j.MDC;

/**
 * 自定义请求头拦截器，将Header数据封装到线程变量中方便获取
 * 注意：此拦截器会同时验证当前用户有效期自动刷新有效期
 *
 * <AUTHOR>
 */
public class HeaderInterceptor implements AsyncHandlerInterceptor
{
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
    {
        if (!(handler instanceof HandlerMethod))
        {
            return true;
        }

        SecurityContextHolder.setUserId(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USER_ID));
        SecurityContextHolder.setUserName(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USERNAME));
        SecurityContextHolder.setRealName(ServletUtils.getHeader(request, SecurityConstants.DETAILS_REALNAME));
        SecurityContextHolder.setUserKey(ServletUtils.getHeader(request, SecurityConstants.USER_KEY));
        // traceId
        String traceId = ServletUtils.getHeader(request, SecurityConstants.TRACE_ID);
        SecurityContextHolder.set(SecurityConstants.TRACE_ID, traceId);
        if (StringUtils.isNotEmpty(traceId)) {
            MDC.put("traceId", traceId);
        }

        String token = SecurityUtils.getToken();
        if (StringUtils.isNotEmpty(token))
        {
            LoginUser loginUser = AuthUtil.getLoginUser(token);
            if (StringUtils.isNotNull(loginUser))
            {
                AuthUtil.verifyLoginUserExpire(loginUser);
                SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception
    {
        SecurityContextHolder.remove();
        MDC.remove("traceId");
    }
}

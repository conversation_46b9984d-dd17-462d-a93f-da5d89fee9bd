package com.wbyy.common.rabbitmq.service;


import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.wbyy.common.rabbitmq.domain.RabbitmqErrorMsg;
import com.wbyy.common.rabbitmq.mapper.RabbitmqErrorMsgMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RabbitService implements ApplicationContextAware {

    private RabbitTemplate rabbitTemplate;

    private RabbitmqErrorMsgMapper rabbitmqErrorMsgMapper;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        rabbitTemplate = applicationContext.getBean(RabbitTemplate.class);
        rabbitmqErrorMsgMapper = applicationContext.getBean(RabbitmqErrorMsgMapper.class);
    }

    public void sendMessage(String exchange, String routingKey, Object obj) {
        sendMessage(exchange, routingKey, obj, IdUtil.simpleUUID());
    }

    public void sendMessage(Object obj, MessageProperties messageProperties) {
        try {
            rabbitTemplate.convertAndSend(obj, message -> new Message(message.getBody(), messageProperties));
        } catch (AmqpException e) {
            saveErrorLog(messageProperties, obj, e);
        }
    }

    public void sendMessage(String exchange, String routingKey, Object obj, String messageId) {
        AtomicReference<MessageProperties> messagePropertiesAtomicReference = new AtomicReference<>();
        try {
            rabbitTemplate.convertAndSend(exchange, routingKey, obj, message -> {
                MessageProperties messageProperties = message.getMessageProperties();
                messageProperties.setMessageId(messageId);
                if (StringUtils.isBlank(messageProperties.getReceivedExchange())) {
                    messageProperties.setReceivedExchange(exchange);
                    messageProperties.setReceivedRoutingKey(routingKey);
                }
                messagePropertiesAtomicReference.set(messageProperties);
                return message;
            });
        } catch (AmqpException e) {
            saveErrorLog(messagePropertiesAtomicReference.get(), obj, e);
        }
    }

    /**
     * 发送延时消息
     *
     * @param exchange   交换机
     * @param routingKey 路由
     * @param obj        消息主体
     * @param delay      延迟时间 单位：ms
     */
    public void sendMessage(String exchange, String routingKey, Object obj, Long delay) {
        AtomicReference<MessageProperties> messageProperties = new AtomicReference<>();
        try {
            rabbitTemplate.convertAndSend(exchange, routingKey, obj, message -> {
                MessageProperties properties = message.getMessageProperties();
                properties.setMessageId(IdUtil.simpleUUID());
                properties.setDelayLong(delay);
                messageProperties.set(properties);
                return message;
            });
        } catch (AmqpException e) {
            saveErrorLog(exchange, Optional.ofNullable(messageProperties.get()).map(MessageProperties::getConsumerQueue).orElse(null), routingKey, obj, e);
        }
    }

    public void saveErrorLog(MessageProperties messageProperties, Object data, Throwable e) {
        messageProperties = Optional.ofNullable(messageProperties).orElseGet(MessageProperties::new);
        saveErrorLog(messageProperties.getReceivedExchange(), messageProperties.getConsumerQueue(), messageProperties.getReceivedRoutingKey(), data, e);
    }

    public void saveErrorLog(String exchange, String queue, String routingKey, Object data, Throwable e) {
        log.error("rabbitmq消息推送失败，交换机：{}，队列：{}，消息：{}", exchange, queue, e.getMessage());
        String errorMsg = "";
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream(); PrintStream ps = new PrintStream(baos)) {
            e.printStackTrace(ps);
            errorMsg = baos.toString(StandardCharsets.UTF_8);
        } catch (Exception ex) {
            log.error("获取报错信息失败", e);
        }

        rabbitmqErrorMsgMapper.insert(RabbitmqErrorMsg.builder()
                .className(data.getClass().getName())
                .data(JSON.toJSONString(data))
                .exchange(exchange)
                .queue(queue)
                .routingKey(routingKey)
                .errorMsg(errorMsg)
                .status(0)
                .type(0)
                .build());
    }
}

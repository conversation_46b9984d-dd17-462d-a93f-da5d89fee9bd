package com.wbyy.common.rabbitmq.aspect;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.rabbitmq.client.Channel;
import com.wbyy.common.rabbitmq.domain.ConsumerResend;
import com.wbyy.common.rabbitmq.domain.RabbitmqErrorMsg;
import com.wbyy.common.rabbitmq.mapper.RabbitmqErrorMsgMapper;
import com.wbyy.common.rabbitmq.service.RabbitService;
import com.wbyy.common.redis.service.RedisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

import static com.wbyy.common.rabbitmq.configure.RabbitmqStatementConfig.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class ConsumerAspect {
    // 重投消息延迟时间（单位ms）
    private final static Long DELAY_TIME = 1000 * 60L;

    private final RabbitmqErrorMsgMapper rabbitmqErrorMsgMapper;
    private final RedisService redisService;
    private final RabbitService rabbitService;

    /**
     * 监听
     *
     * @param joinPoint
     * @param rabbitListener
     */
    @Around("@annotation(rabbitListener)")
    public void autoAck(ProceedingJoinPoint joinPoint, RabbitListener rabbitListener) {

        Object[] args = joinPoint.getArgs();
        Message message = (Message) args[0];
        Object obj = args[1];
        Channel channel = (Channel) args[2];
        MessageProperties messageProperties = message.getMessageProperties();
        try {
            joinPoint.proceed();
        } catch (Throwable e) {
            log.error("rabbitmq消息消费失败，MessageId:{}，原因：", e.getMessage(), e);
            if (!MESSAGE_RESEND_QUEUE.equals(messageProperties.getConsumerQueue()))
                this.resendMessage(messageProperties, obj, e);
        } finally {
            // ack
            try {
                channel.basicAck(messageProperties.getDeliveryTag(), false);
            } catch (IOException e) {
                log.error("rabbitmq消息ack失败：{}", e.getMessage(), e);
            }
        }

    }

    /**
     * 重投消息
     */
    private void resendMessage(MessageProperties messageProperties, Object obj, Throwable e) {
        String incrementKey = "rabbitmq:incrementKey:key:" + messageProperties.getMessageId();
        Object o = redisService.getCacheObject(incrementKey);
        // 重投超过3次入库
        String errorMessage = "";
        if (Objects.nonNull(o) && Long.parseLong(o.toString()) >= 3L) {
            log.error("投递次数超过3次，请求体:{}", JSONObject.toJSONString(obj));

            try (ByteArrayOutputStream baos = new ByteArrayOutputStream(); PrintStream printStream = new PrintStream(baos)) {
                e.printStackTrace(printStream);
                errorMessage = new String(baos.toByteArray(), StandardCharsets.UTF_8);
            } catch (Exception ex) {
                log.error("入库失败：{}", ex.getMessage());
            }

            RabbitmqErrorMsg errorMsg = RabbitmqErrorMsg.builder()
                    .className(obj.getClass().getName())
                    .data(JSON.toJSONString(obj))
                    .errorMsg(errorMessage)
                    .exchange(messageProperties.getReceivedExchange())
                    .queue(messageProperties.getConsumerQueue())
                    .routingKey(messageProperties.getReceivedRoutingKey())
                    .status(0)
                    .type(1)
                    .build();
            rabbitmqErrorMsgMapper.insert(errorMsg);
            redisService.deleteObject(incrementKey);
            return;
        }
        // 消息重投
        long resendNum = redisService.increment(incrementKey, 1L);

        ConsumerResend resend = ConsumerResend.builder()
                .exchange(messageProperties.getReceivedExchange())
                .routingKey(messageProperties.getReceivedRoutingKey())
                .data(obj)
                .messageId(messageProperties.getMessageId())
                .build();
        long delay = DELAY_TIME * resendNum << 2;
        log.info("消息重投，MessageId:{}，第{}次，延迟时间：{}ms", messageProperties.getMessageId(), resendNum, delay);
        rabbitService.sendMessage(DELAY_EXCHANGE_NAME, MESSAGE_RESEND_ROUTING_KEY, resend, delay);
    }

}


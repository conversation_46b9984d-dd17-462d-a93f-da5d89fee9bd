package com.wbyy.common.rabbitmq.domain;


import com.alibaba.fastjson2.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConsumerResend implements Serializable {
    @Serial
    private static final long serialVersionUID = -5085083080413297427L;

    private String exchange;

    private String routingKey;

    private Object data;

    private String messageId;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

package com.wbyy.common.rabbitmq.listener;

import com.rabbitmq.client.Channel;
import com.wbyy.common.rabbitmq.domain.ConsumerResend;
import com.wbyy.common.rabbitmq.service.RabbitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import static com.wbyy.common.rabbitmq.configure.RabbitmqStatementConfig.MESSAGE_RESEND_QUEUE;

/**
 * 发送邮件消息监听
 *
 * <AUTHOR>
 * @date 2025/7/22 09:12
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MessageResendConsumer {

    private final RabbitService rabbitService;
    @RabbitListener(queues = MESSAGE_RESEND_QUEUE)
    public void consume(Message message, ConsumerResend consumerResend, Channel channel) {
        log.info("队列：{}, 消息: {} -- {}", MESSAGE_RESEND_QUEUE, message.toString(), consumerResend);
        rabbitService.sendMessage(
                consumerResend.getExchange(),
                consumerResend.getRoutingKey(),
                consumerResend.getData());

    }
}
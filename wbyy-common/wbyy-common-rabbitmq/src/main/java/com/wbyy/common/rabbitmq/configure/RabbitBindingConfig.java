package com.wbyy.common.rabbitmq.configure;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/23  9:48
 * @description rabbitMQ绑定配置
 */
@Configuration
@ConfigurationProperties(prefix = "rabbitmq")
@Data
@Slf4j
public class RabbitBindingConfig {

    @Autowired
    private RabbitAdmin rabbitAdmin;

    private List<BindingConfig> bindings;

    @Data
    private static class BindingConfig {
        /**
         * 名称
         */
        private String exchange;
        /**
         * 类型
         */
        private String type;
        private boolean durable;
        private boolean autoDelete;
        /**
         * exchange的arguments,存json
         */
        private String arguments;
        private List<QueueConfig> queues;
        /**
         * customExchange的类型
         */
        private String customExchangeType;
    }

    @Data
    private static class QueueConfig {
        private String name;
        private String routingKey;
        private boolean durable;
    }

    @DependsOn("rabbitAdmin")
    @PostConstruct
    public void initBindings() {
        log.info("【Rabbit-MQ】初始化开始.");
        rabbitAdmin.initialize();  // 主动触发声明

        if (bindings == null || bindings.isEmpty()) return;

        bindings.forEach(binding -> {
            Map<String, Object> arguments = JSON.parseObject(binding.getArguments(), new TypeReference<>() {
            });
            log.info("【Rabbit-MQ】初始化交换机. 名称: [{}], 是否持久化: [{}]，是否自动清除: [{}]，参数: [{}]",
                    binding.getExchange(), binding.isDurable(), binding.isAutoDelete(), arguments);
            Exchange exchange = switch (binding.getType()) {
                case "direct" ->
                        new DirectExchange(binding.getExchange(), binding.isDurable(), binding.isAutoDelete(), arguments);
                case "topic" ->
                        new TopicExchange(binding.getExchange(), binding.isDurable(), binding.isAutoDelete(), arguments);
                case "fanout" ->
                        new FanoutExchange(binding.getExchange(), binding.isDurable(), binding.isAutoDelete(), arguments);
                case "headers" ->
                        new HeadersExchange(binding.getExchange(), binding.isDurable(), binding.isAutoDelete(), arguments);
                case "custom" ->
                        new CustomExchange(binding.getExchange(), binding.getCustomExchangeType(), binding.isDurable(),
                                binding.isAutoDelete(), arguments);
                default -> throw new IllegalArgumentException("Unsupported exchange type");
            };

            try {
                rabbitAdmin.declareExchange(exchange);
            } catch (AmqpException e) {
                log.error("【Rabbit-MQ】Exchange初始化失败: {}", exchange.getName(), e);
            }

            binding.getQueues().forEach(queue -> {
                // FanoutExchange配置RoutingKey的警告
                if (exchange instanceof FanoutExchange && queue.getRoutingKey() != null) {
                    log.warn("【Rabbit-MQ】RoutingKey '{}' will be ignored by FanoutExchange", queue.getRoutingKey());
                }

                Queue q = new Queue(queue.getName(), queue.isDurable());
                rabbitAdmin.declareQueue(q);

                log.info("【Rabbit-MQ】队列初始化. 队列 -> 交换机: [{}] -> [{}], routingKey: [{}]",
                        queue.getName(), exchange.getName(), queue.getRoutingKey());

                rabbitAdmin.declareBinding(createBinding(q, exchange, queue.routingKey, arguments));
            });
        });
    }

    /**
     * 创建绑定关系
     *
     * @param queue      队列
     * @param exchange   交换机
     * @param routingKey key
     * @return binding
     */
    private static Binding createBinding(Queue queue, Exchange exchange, String routingKey, Map<String, Object> arguments) {
        if (exchange instanceof HeadersExchange) {
            return BindingBuilder.bind(queue)
                    .to((HeadersExchange) exchange)
                    .where("x-match").matches("any");  // 必须为首个条件
        } else if (exchange instanceof TopicExchange) {
            return BindingBuilder.bind(queue)
                    .to((TopicExchange) exchange)
                    .with(routingKey);
        } else if (exchange instanceof DirectExchange) {
            return BindingBuilder.bind(queue)
                    .to((DirectExchange) exchange)
                    .with(routingKey);
        } else if (exchange instanceof FanoutExchange) {
            return BindingBuilder.bind(queue)
                    .to((FanoutExchange) exchange);
        } else if (exchange instanceof CustomExchange) {
            return BindingBuilder.bind(queue)
                    .to(exchange)
                    .with(routingKey).and(arguments);
        }
        throw new IllegalArgumentException("Unsupported exchange type");
    }
}

package com.wbyy.common.log.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * API参数日志信息
 * 
 * <AUTHOR>
 */
@Data
public class ApiParamLogInfo {
    
    /**
     * 请求ID（用于关联请求和响应）
     */
    private String requestId;
    
    /**
     * 操作描述
     */
    private String operation;
    
    /**
     * 请求URL
     */
    private String requestUrl;
    
    /**
     * 请求方法
     */
    private String requestMethod;
    
    /**
     * 请求IP
     */
    private String requestIp;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 类名
     */
    private String className;
    
    /**
     * 方法名
     */
    private String methodName;
    
    /**
     * 请求参数
     */
    private String requestParams;
    
    /**
     * 请求头信息
     */
    private String requestHeaders;
    
    /**
     * 响应参数
     */
    private String responseParams;
    
    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 异常信息
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 线程ID
     */
    private String threadId;
    
    /**
     * 追踪ID
     */
    private String traceId;
}

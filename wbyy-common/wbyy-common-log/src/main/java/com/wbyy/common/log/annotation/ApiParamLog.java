package com.wbyy.common.log.annotation;

import java.lang.annotation.*;

/**
 * API参数拦截注解
 * 用于标记需要拦截入参和出参的接口方法
 * 
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiParamLog {
    
    /**
     * 操作描述
     */
    String value() default "";
    
    /**
     * 是否记录请求参数
     */
    boolean logRequest() default true;
    
    /**
     * 是否记录响应参数
     */
    boolean logResponse() default true;
    
    /**
     * 排除的参数名称（敏感信息）
     */
    String[] excludeParams() default {"password", "token", "secret"};
    
    /**
     * 是否记录请求头信息
     */
    boolean logHeaders() default false;
    
    /**
     * 最大参数长度（超过则截断）
     */
    int maxParamLength() default 2000;
}

package com.wbyy.common.log.service;

import com.wbyy.system.api.domain.ApiOperLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.system.api.SystemLogApi;

/**
 * 异步调用日志服务
 * 
 * <AUTHOR>
 */
@Service
public class AsyncLogService
{
    @Autowired
    private SystemLogApi systemLogApi;

    /**
     * 保存系统日志记录
     */
    @Async
    public void saveSysLog(ApiOperLog sysOperLog) throws Exception
    {
        systemLogApi.saveLog(sysOperLog, SecurityConstants.INNER);
    }
}

package com.wbyy.common.log.aspect;

import com.alibaba.fastjson2.JSON;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.context.SecurityContextHolder;
import com.wbyy.common.core.utils.ServletUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.core.utils.ip.IpUtils;
import com.wbyy.common.log.annotation.ApiParamLog;
import com.wbyy.common.log.domain.ApiParamLogInfo;
import com.wbyy.common.log.filter.PropertyPreExcludeFilter;
import com.wbyy.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.core.NamedThreadLocal;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.UUID;

/**
 * API参数拦截切面
 * 用于拦截和记录API接口的入参和出参
 * 
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class ApiParamLogAspect {
    
    /** 计算操作消耗时间 */
    private static final ThreadLocal<Long> TIME_THREADLOCAL = new NamedThreadLocal<>("API Param Log Cost Time");
    
    /** 请求ID线程变量 */
    private static final ThreadLocal<String> REQUEST_ID_THREADLOCAL = new NamedThreadLocal<>("API Request ID");
    
    /**
     * 配置织入点
     */
    @Pointcut("@annotation(com.wbyy.common.log.annotation.ApiParamLog)")
    public void apiParamLogPointCut() {
    }
    
    /**
     * 环绕通知
     */
    @Around("apiParamLogPointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String requestId = UUID.randomUUID().toString().replace("-", "");
        TIME_THREADLOCAL.set(startTime);
        REQUEST_ID_THREADLOCAL.set(requestId);
        
        ApiParamLogInfo logInfo = new ApiParamLogInfo();
        Object result = null;
        Exception exception = null;
        
        try {
            // 获取注解信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            ApiParamLog apiParamLog = method.getAnnotation(ApiParamLog.class);
            
            // 设置基本信息
            setBasicInfo(joinPoint, logInfo, apiParamLog, requestId);
            
            // 记录请求参数
            if (apiParamLog.logRequest()) {
                setRequestInfo(joinPoint, logInfo, apiParamLog);
            }
            
            // 执行原方法
            result = joinPoint.proceed();
            
            // 记录响应参数
            if (apiParamLog.logResponse() && result != null) {
                setResponseInfo(result, logInfo, apiParamLog);
            }
            
            logInfo.setSuccess(true);
            
        } catch (Exception e) {
            exception = e;
            logInfo.setSuccess(false);
            logInfo.setErrorMessage(StringUtils.substring(e.getMessage(), 0, 500));
            throw e;
        } finally {
            // 设置执行时间
            long endTime = System.currentTimeMillis();
            logInfo.setExecutionTime(endTime - startTime);
            logInfo.setCreateTime(LocalDateTime.now());
            
            // 输出日志
            outputLog(logInfo);
            
            // 清理线程变量
            TIME_THREADLOCAL.remove();
            REQUEST_ID_THREADLOCAL.remove();
        }
        
        return result;
    }
    
    /**
     * 设置基本信息
     */
    private void setBasicInfo(JoinPoint joinPoint, ApiParamLogInfo logInfo, ApiParamLog apiParamLog, String requestId) {
        try {
            HttpServletRequest request = ServletUtils.getRequest();
            
            logInfo.setRequestId(requestId);
            logInfo.setOperation(apiParamLog.value());
            logInfo.setRequestUrl(request.getRequestURI());
            logInfo.setRequestMethod(request.getMethod());
            logInfo.setRequestIp(IpUtils.getIpAddr(request));
            logInfo.setClassName(joinPoint.getTarget().getClass().getName());
            logInfo.setMethodName(joinPoint.getSignature().getName());
            logInfo.setThreadId(String.valueOf(Thread.currentThread().getId()));
            
            // 获取用户名
            String username = SecurityUtils.getUsername();
            if (StringUtils.isNotBlank(username)) {
                logInfo.setUsername(username);
            }
            
            // 获取traceId
            String traceId = MDC.get("traceId");
            if (StringUtils.isBlank(traceId)) {
                traceId = SecurityContextHolder.get(SecurityConstants.TRACE_ID);
            }
            logInfo.setTraceId(traceId);
            
        } catch (Exception e) {
            log.warn("设置基本信息失败: {}", e.getMessage());
        }
    }
    
    /**
     * 设置请求信息
     */
    private void setRequestInfo(JoinPoint joinPoint, ApiParamLogInfo logInfo, ApiParamLog apiParamLog) {
        try {
            HttpServletRequest request = ServletUtils.getRequest();
            
            // 记录请求头
            if (apiParamLog.logHeaders()) {
                Map<String, String> headers = ServletUtils.getHeaders(request);
                logInfo.setRequestHeaders(StringUtils.substring(JSON.toJSONString(headers), 0, apiParamLog.maxParamLength()));
            }
            
            // 记录请求参数
            String requestParams = getRequestParams(joinPoint, request, apiParamLog);
            logInfo.setRequestParams(StringUtils.substring(requestParams, 0, apiParamLog.maxParamLength()));
            
        } catch (Exception e) {
            log.warn("设置请求信息失败: {}", e.getMessage());
        }
    }
    
    /**
     * 设置响应信息
     */
    private void setResponseInfo(Object result, ApiParamLogInfo logInfo, ApiParamLog apiParamLog) {
        try {
            String responseParams = JSON.toJSONString(result);
            logInfo.setResponseParams(StringUtils.substring(responseParams, 0, apiParamLog.maxParamLength()));
        } catch (Exception e) {
            log.warn("设置响应信息失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取请求参数
     */
    private String getRequestParams(JoinPoint joinPoint, HttpServletRequest request, ApiParamLog apiParamLog) {
        String requestMethod = request.getMethod();
        Map<?, ?> paramsMap = ServletUtils.getParamMap(request);
        
        if (StringUtils.isEmpty(paramsMap) && 
            (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod))) {
            // POST/PUT请求，从方法参数获取
            return argsArrayToString(joinPoint.getArgs(), apiParamLog.excludeParams());
        } else {
            // GET请求或有URL参数的请求
            return JSON.toJSONString(paramsMap, excludePropertyPreFilter(apiParamLog.excludeParams()));
        }
    }
    
    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray, String[] excludeParamNames) {
        StringBuilder params = new StringBuilder();
        if (paramsArray != null && paramsArray.length > 0) {
            for (Object o : paramsArray) {
                if (StringUtils.isNotNull(o) && !isFilterObject(o)) {
                    try {
                        String jsonObj = JSON.toJSONString(o, excludePropertyPreFilter(excludeParamNames));
                        params.append(jsonObj).append(" ");
                    } catch (Exception e) {
                        // 序列化失败时使用toString
                        params.append(o.toString()).append(" ");
                    }
                }
            }
        }
        return params.toString().trim();
    }
    
    /**
     * 忽略敏感属性
     */
    private PropertyPreExcludeFilter excludePropertyPreFilter(String[] excludeParamNames) {
        return new PropertyPreExcludeFilter().addExcludes(excludeParamNames);
    }
    
    /**
     * 判断是否需要过滤的对象
     */
    private boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (java.util.Collection.class.isAssignableFrom(clazz)) {
            java.util.Collection collection = (java.util.Collection) o;
            for (Object value : collection) {
                return value instanceof MultipartFile;
            }
        } else if (java.util.Map.class.isAssignableFrom(clazz)) {
            java.util.Map map = (java.util.Map) o;
            for (Object value : map.entrySet()) {
                java.util.Map.Entry entry = (java.util.Map.Entry) value;
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse;
    }
    
    /**
     * 输出日志
     */
    private void outputLog(ApiParamLogInfo logInfo) {
        try {
            if (log.isInfoEnabled()) {
                log.info("API参数拦截日志: {}", JSON.toJSONString(logInfo));
            }
        } catch (Exception e) {
            log.error("输出API参数日志失败: {}", e.getMessage());
        }
    }
}

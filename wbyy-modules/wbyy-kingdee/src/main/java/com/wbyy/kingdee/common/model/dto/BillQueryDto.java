package com.wbyy.kingdee.common.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.wbyy.common.core.exception.ServiceException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import static com.wbyy.kingdee.common.config.jackson.StaticMapper.OBJECT_MAPPER;

@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class BillQueryDto extends AbstractBaseDto{

    @JsonProperty("FieldKeys")
    private String fieldKeys;

    @JsonProperty("FilterString")
    private String[] filterString;

    @JsonProperty("OrderString")
    private String orderString;

    @JsonProperty("TopRowCount")
    private Integer topRowCount;

    @JsonProperty("StartRow")
    private Integer startRow;

    @JsonProperty("Limit")
    private Integer limit;

    @JsonProperty("SubSystemId")
    private String subSystemId;

    public String toParamString(){
        try {
            return OBJECT_MAPPER.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            log.error("【ViewDto】转参数失败",e);
            throw new ServiceException("【ViewDto】转参数失败");
        }
    }
}

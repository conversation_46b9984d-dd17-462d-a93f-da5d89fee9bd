package com.wbyy.kingdee;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.wbyy.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * Kingdee
 * 
 * <AUTHOR>
 */
@Slf4j
@EnableRyFeignClients
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
public class WbyyKingdeeApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(WbyyKingdeeApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  Kingdee模块启动成功   ლ(´ڡ`ლ)ﾞ  \n");
        log.info("(♥◠‿◠)ﾉﾞ  Kingdee模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}

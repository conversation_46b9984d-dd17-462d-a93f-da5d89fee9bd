package com.wbyy.kingdee.modules.k3api.controller;

import com.wbyy.common.core.web.domain.AjaxResult;
import com.wbyy.kingdee.common.config.k3.K3CloudService;
import com.wbyy.kingdee.common.model.dto.AddDto;
import com.wbyy.kingdee.common.model.dto.BillQueryDto;
import com.wbyy.kingdee.common.model.dto.ViewDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("k3api")
@Slf4j
public class K3ApiController {

    @Autowired
    private K3CloudService k3CloudService;

    @PostMapping("view")
    public AjaxResult view(@RequestBody ViewDto dto){
        return AjaxResult.success(k3CloudService.view(dto));
    }

    @PostMapping("bill-query")
    public AjaxResult billQuery(@RequestBody BillQueryDto dto){
        return AjaxResult.success(k3CloudService.billQuery(dto));
    }

    @PostMapping("add")
    public AjaxResult add(@RequestBody AddDto dto){
        return AjaxResult.success(k3CloudService.add(dto));
    }
}

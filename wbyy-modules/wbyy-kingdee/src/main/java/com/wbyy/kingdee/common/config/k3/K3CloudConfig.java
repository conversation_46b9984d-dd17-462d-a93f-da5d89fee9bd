package com.wbyy.kingdee.common.config.k3;

import com.kingdee.bos.webapi.entity.IdentifyInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class K3CloudConfig {
    @Value("${X-KDApi-AcctID}")
    private String acctId;
    @Value("${X-KDApi-AppID}")
    private String appId;
    @Value("${X-KDApi-AppSec}")
    private String appSec;
    @Value("${X-KDApi-UserName}")
    private String userName;
    @Value("${X-KDApi-LCID}")
    private Integer lcid;
    @Value("${X-KDApi-ServerUrl}")
    private String serverUrl;

    @Bean(value = "myK3CloudApi")
    public MyK3CloudApi myK3CloudApi(){
        IdentifyInfo iden = new IdentifyInfo();
        iden.setUserName(userName);
        iden.setAppId(appId);
        iden.setdCID(acctId);
        iden.setAppSecret(appSec);
        iden.setlCID(lcid);
        iden.setServerUrl(serverUrl);
        return new MyK3CloudApi(iden);
    }
}

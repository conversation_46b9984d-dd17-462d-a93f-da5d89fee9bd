package com.wbyy.kingdee.common.config.k3;

import com.kingdee.bos.webapi.entity.IdentifyInfo;
import com.kingdee.bos.webapi.sdk.K3CloudApi;
import com.wbyy.common.core.exception.ServiceException;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@NoArgsConstructor
@Slf4j
public class MyK3CloudApi extends K3CloudApi {
    private IdentifyInfo iden;
    public MyK3CloudApi(IdentifyInfo iden) {
        super(iden);
        this.iden = iden;
    }

    public void login(){
        Object[] parameters = new Object[] {
                iden.getdCID(),//数据中心
               iden.getUserName(),//用户
                iden.getAppId(),//appid
                iden.getAppSecret(),//app密钥
                iden.getlCID()};
        try {
            String execute = super.execute("Kingdee.BOS.WebApi.ServicesStub.AuthService.LoginByAppSecret", parameters);
            log.info("金蝶登录返回报文：\n{}",execute);
        } catch (Exception e) {
            log.error("金蝶登录失败",e);
            throw new ServiceException("金蝶登录失败");
        }
    }
}

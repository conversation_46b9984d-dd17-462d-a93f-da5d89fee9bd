package com.wbyy.kingdee.common.config.k3;

import com.alibaba.fastjson2.JSONObject;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.kingdee.common.model.dto.AddDto;
import com.wbyy.kingdee.common.model.dto.BillQueryDto;
import com.wbyy.kingdee.common.model.dto.ViewDto;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Component
@Slf4j
public class K3CloudService {
    // 定义倒计时时间（单位：毫秒）
    private static final long COUNTDOWN_DURATION = 10 * 60 * 1000; // 10 分钟
    private static final long ONE_MINUTE = 60 * 1000; // 1 分钟

    // 倒计时开始时间
    private volatile long startTime;

    // 读写锁
//    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    @Autowired
    private MyK3CloudApi myK3CloudApi;

    @PostConstruct
    public void init(){
        // 初始化倒计时开始时间
//        resetTimer();
    }

    /**
     * 重置倒计时
     */
    private void resetTimer() {
//        lock.writeLock().lock();
        try {
            this.startTime = System.currentTimeMillis(); // 设置当前时间为倒计时开始时间
            myK3CloudApi.login();
        } finally {
//            lock.writeLock().unlock();
        }
    }

    /**
     * 检查倒计时状态并执行相应方法
     */
    private void check() {
//        lock.readLock().lock();
        try {
            long elapsedTime = System.currentTimeMillis() - startTime; // 计算已经过去的时间
            long remainingTime = COUNTDOWN_DURATION - elapsedTime; // 计算剩余时间

            if (remainingTime <= ONE_MINUTE) {
                resetTimer(); // 倒计时结束或剩余时间不足 1 分钟
            }
        } finally {
//            lock.readLock().unlock();
        }
    }

    public JSONObject view(ViewDto dto){
//        check();
        try {
            log.info("进入【k3api/view】");
            JSONObject parse = JSONObject.parse(myK3CloudApi.view(dto.getFormId(), dto.toParamString()));
            log.info("退出【k3api/view】");
            return parse;
        } catch (Exception e) {
            log.error("kingdee查询调用失败",e);
            throw new ServiceException("kingdee查询调用失败");
        }
    }

    public List<List<Object>> billQuery(BillQueryDto dto){
//        check();
        try {
            log.info("进入【k3api/bill-query】");
            List<List<Object>> list = myK3CloudApi.executeBillQuery(dto.toParamString());
            log.info("退出【k3api/bill-query】");
            return list;
        }catch (Exception e) {
            log.error("kingdee查询调用失败",e);
            throw new ServiceException("kingdee查询调用失败");
        }
    }

    public JSONObject add(AddDto dto){
        try {
            log.info("进入【k3api/add】");
            String save = myK3CloudApi.save(dto.getFormId(), dto.toParamString());
            log.info("退出【k3api/add】");
            return JSONObject.parse(save);
        }catch (Exception e){
            log.error("kingdee查询调用失败",e);
            throw new ServiceException("kingdee新增调用失败");
        }
    }

}

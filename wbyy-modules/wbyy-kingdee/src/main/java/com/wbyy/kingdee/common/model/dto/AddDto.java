package com.wbyy.kingdee.common.model.dto;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.wbyy.common.core.exception.ServiceException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import static com.wbyy.kingdee.common.config.jackson.StaticMapper.OBJECT_MAPPER;

@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class AddDto extends AbstractBaseDto {

    @JsonProperty("NeedUpDateFields")
    private String[] needUpDateFields;

    @JsonProperty("NeedReturnFields")
    private String[] needReturnFields;

    @JsonProperty("Model")
    private JSONObject model;


    public String toParamString(){
        try {
            return OBJECT_MAPPER.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            log.error("【ViewDto】转参数失败",e);
            throw new ServiceException("【ViewDto】转参数失败");
        }
    }
}

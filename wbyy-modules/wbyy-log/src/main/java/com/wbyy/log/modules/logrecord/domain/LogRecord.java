package com.wbyy.log.modules.logrecord.domain;

import java.io.Serializable;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 日志记录表(log_record)实体类
 *
 * <AUTHOR>
 * @since 2025-04-15 09:39:00
 * @description 
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName("log_record")
public class LogRecord extends Model<LogRecord> {

    /**
     * 主键
     */
    @TableId
	private Long id;
    /**
     * 用户Id
     */
    private String userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户工号
     */
    private String workNumber;
    /**
     * 部门Id
     */
    private String deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 访问IP
     */
    private String ip;
    /**
     * 操作时间
     */
    private Date operationTime;

    @TableField(exist = false)
    private Date operationStartTime;

    @TableField(exist = false)
    private Date operationEndTime;
    /**
     * 集成系统标识
     */
    private String integrateCode;
    /**
     * 操作模块，业务系统中的一类业务模块的标识，如项目、物料、客户、登录

     */
    private String module;
    /**
     * 数据模块，模块中对不同业务模块类型中有数据表现的操作的分类，如项目中的团队、计划；知识库中的文档模版、经验分享

     */
    private String dataModule;
    /**
     * 数据模块中具体数据Id
     */
    private String dataModuleId;
    /**
     * 接入应用中这条日志数据关联的核心业务Id
     */
    private String bizId;
    /**
     * 接入应用中这条日志数据关联的核心业务名称
     */
    private String bizName;
    /**
     * 接入应用中这条日志数据关联的核心业务表示
     */
    private String bizCode;
    /**
     * 操作类型，修改、新增、删除或触发业务数据发生改变的动作的定义，如进展反馈、升降级、任务反馈
     */
    private String operationType;
    /**
     * 日志内容，主要记录对持久化数据造成变动的数据，通过拼接提高可读性，敏感信息脱敏
     */
    private String content;

    @TableField(exist = false)
    private List<FieldChange> fieldChanges;

    @TableField(exist = false)
    private String searchValue;

    public void buildSearchValue(){
        if (StrUtil.isBlank(this.searchValue)){
            return;
        }
        this.operationType = this.searchValue;
        this.content = this.searchValue;
        this.bizName = this.searchValue;
        this.bizCode = this.searchValue;
        this.dataModule = this.searchValue;
        this.module = this.searchValue;
        this.userName = this.searchValue;
        this.deptName = this.searchValue;
        this.workNumber = this.searchValue;
    }

}
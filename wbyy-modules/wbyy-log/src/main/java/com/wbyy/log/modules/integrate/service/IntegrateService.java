package com.wbyy.log.modules.integrate.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.log.modules.integrate.domain.Integrate;

/**
 * 应用集成表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-15 09:46:33
 * @description 
 */
public interface IntegrateService extends IService<Integrate> {
    void addNew(Integrate dto);

    void update(Integrate dto);

    Integrate getByCodeByToken(String code,String token);
}

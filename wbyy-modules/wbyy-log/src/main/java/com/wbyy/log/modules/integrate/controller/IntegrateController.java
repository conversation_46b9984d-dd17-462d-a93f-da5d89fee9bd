package com.wbyy.log.modules.integrate.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.domain.AjaxResult;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.log.modules.integrate.domain.Integrate;
import com.wbyy.log.modules.integrate.service.IntegrateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

@RestController
@RequestMapping("integrate")
public class IntegrateController extends BaseController {
    @Autowired
    private IntegrateService integrateService;


    @RequiresPermissions("log-server:integrate:list")
    @GetMapping("list")
    public TableDataInfo list(Integrate dto){
        startPage();
        return getDataTable(integrateService.list(new LambdaQueryWrapper<Integrate>()
                .like(StrUtil.isNotBlank(dto.getName()),Integrate::getName,dto.getName())));
    }

    @RequiresPermissions("log-server:integrate:add")
    @PostMapping("add")
    public AjaxResult add(@Validated @RequestBody Integrate dto){
        integrateService.addNew(dto);
        return AjaxResult.success();
    }

    @RequiresPermissions("log-server:integrate:delete")
    @DeleteMapping("/{ids}")
    public AjaxResult delete(@PathVariable Long[] ids){
        integrateService.removeBatchByIds(Arrays.stream(ids).toList());
        return AjaxResult.success();
    }

    @RequiresPermissions("log-server:integrate:detail")
    @GetMapping("/{id}")
    public AjaxResult detail(@PathVariable Long id){
        return AjaxResult.success(integrateService.getById(id));
    }

    @RequiresPermissions("log-server:integrate:update")
    @PostMapping("update")
    public AjaxResult update(@Validated @RequestBody Integrate dto){
        integrateService.update(dto);
        return AjaxResult.success();
    }
}

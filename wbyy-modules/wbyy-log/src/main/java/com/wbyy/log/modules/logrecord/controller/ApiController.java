package com.wbyy.log.modules.logrecord.controller;

import com.wbyy.common.core.web.domain.AjaxResult;
import com.wbyy.log.common.annotation.IntegrateCheck;
import com.wbyy.log.modules.logrecord.domain.LogRecord;
import com.wbyy.log.modules.logrecord.service.LogRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("log-api")
public class ApiController {
    @Autowired
    private LogRecordService logRecordService;

    @PostMapping("process")
    @IntegrateCheck
    public AjaxResult process(@RequestBody LogRecord model){
        logRecordService.process(model);
        return AjaxResult.success();
    }
}

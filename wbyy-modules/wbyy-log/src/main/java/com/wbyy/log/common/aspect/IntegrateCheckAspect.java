package com.wbyy.log.common.aspect;

import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.ServletUtils;
import com.wbyy.log.common.constant.ConfigConst;
import com.wbyy.log.modules.integrate.domain.Integrate;
import com.wbyy.log.modules.integrate.service.IntegrateService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class IntegrateCheckAspect {

    @Autowired
    private IntegrateService integrateService;

    @Pointcut("@annotation(com.wbyy.log.common.annotation.IntegrateCheck)")
    public void pointcut(){}

    @Before("pointcut()")
    public void before(){
        HttpServletRequest request = ServletUtils.getRequest();
        if (null==request) throw new ServiceException("获取请求失败");
        String authToken = ServletUtils.getHeader(request, ConfigConst.AUTH_HEADER);
        if (StrUtil.isBlank(authToken)) throw new ServiceException("身份令牌为空");
        String integrateCode = ServletUtils.getHeader(request, ConfigConst.CODE_HEADER);
        if (StrUtil.isBlank(integrateCode)) throw new ServiceException("集成标识为空");
        Integrate byCodeByToken = integrateService.getByCodeByToken(integrateCode, authToken);
        if (null==byCodeByToken) throw new ServiceException("身份验证失败");
    }
}

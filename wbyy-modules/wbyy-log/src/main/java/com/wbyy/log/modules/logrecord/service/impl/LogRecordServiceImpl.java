package com.wbyy.log.modules.logrecord.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.redis.helper.SimpleLockHelper;
import com.wbyy.log.modules.logrecord.domain.LogRecord;
import com.wbyy.log.modules.logrecord.mapper.LogRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.log.modules.logrecord.service.LogRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

/**
 * 日志记录表服务接口实现
 *
 * <AUTHOR>
 * @since 2025-04-15 09:39:00
 * @description 
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogRecordServiceImpl extends ServiceImpl<LogRecordMapper, LogRecord> implements LogRecordService {

    private static final String LOCK_KEY = "LOG:RECORD:PROCESS:%s:%s:%s:%s";
    private final SimpleLockHelper simpleLockHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void process(LogRecord model) {
        String lock = String.format(LOCK_KEY, model.getWorkNumber(), model.getOperationTime(), model.getBizId(), model.getDataModuleId());
        simpleLockHelper.execute(lock,()->{
            LogRecord exist = this.getExist(model);
            if (null!=exist) return null;
            this.save(model);
            return null;
        });
    }

    private LogRecord getExist(LogRecord model){
        LambdaQueryWrapper<LogRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogRecord::getWorkNumber,model.getWorkNumber());
        queryWrapper.eq(LogRecord::getOperationTime,model.getOperationTime());
        queryWrapper.eq(LogRecord::getBizId,model.getBizId());
        queryWrapper.eq(LogRecord::getDataModuleId,model.getDataModuleId());
        return this.getOne(queryWrapper,false);
    }


}
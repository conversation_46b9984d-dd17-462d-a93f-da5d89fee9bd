package com.wbyy.log.modules.integrate.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;

import com.wbyy.orm.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 应用集成表(integrate)实体类
 *
 * <AUTHOR>
 * @since 2025-04-15 09:46:33
 * @description 
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("integrate")
public class Integrate extends BaseEntity {

    /**
     * id
     */
    @TableId
	private Long id;
    /**
     * 集成应用名称
     */
    @NotBlank(message = "应用名称不得为空")
    private String name;
    /**
     * 集成应用标识
     */
    @NotBlank(message = "应用标识不得为空")
    private String code;
    /**
     * 集成应用访问令牌
     */
    @NotBlank(message = "访问令牌不得为空")
    private String token;
    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Integer delFlag;

}
-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(), 0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(@parentId, '消息模板', '1925779955281735681', '1', 'template', 'message/template/index', 1, 0, 'C', '0', '0', 'message:template:list', '#', 1, sysdate(), null, null, '消息模板菜单', 'sys');


-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '消息模板查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'message:template:list',        '#', 1, sysdate(), null, null, '', 'sys');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '消息模板新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'message:template:add',          '#', 1, sysdate(), null, null, '', 'sys');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '消息模板修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'message:template:edit',         '#', 1, sysdate(), null, null, '', 'sys');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '消息模板删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'message:template:remove',       '#', 1, sysdate(), null, null, '', 'sys');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '消息模板导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'message:template:export',       '#', 1, sysdate(), null, null, '', 'sys');
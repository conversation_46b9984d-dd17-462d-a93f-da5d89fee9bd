package com.wbyy.message.modules.messagesendinfo.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;

/**
 * 消息发送记录对象 message_send_info
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "message_send_info", autoResultMap = true)
@Schema(description = "消息发送记录实体类")
@EqualsAndHashCode(callSuper = true)
public class MessageSendInfo extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /**
     * 任务ID
     */
    @Excel(name = "任务ID")
    @Schema(description = "任务ID")
    @Size(max = 36, message = "任务ID不能超过 36 个字符")
    private String taskId;

    @Excel(name = "消息服务任务ID")
    @Schema(description = "消息服务任务ID")
    private String msgServerTaskId;

    /**
     * 渠道编码
     */
    @Schema(description = "渠道编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelCode;

    /**
     * 消息类型
     */
    @Schema(description = "消息类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String type;

    /**
     * 应用编码
     */
    @Excel(name = "应用编码")
    @Schema(description = "应用编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "应用编码不能为空")
    @Size(max = 64, message = "应用编码不能超过 64 个字符")
    private String applicationCode;

    /**
     * 消息模板ID
     */
    @Excel(name = "消息模板ID")
    @Schema(description = "消息模板ID")
    private Long messageTemplateId;

    @TableField(exist = false)
    private String messageTemplateName;

    /**
     * 模板参数配置
     */
    @Excel(name = "模板参数配置")
    @Schema(description = "模板参数配置")
    private String messageTemplateParams;

    /**
     * 消息体
     */
    @Excel(name = "消息体")
    @Schema(description = "消息体", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "消息体不能为空")
    private String message;

    /**
     * 接收者
     */
    @Excel(name = "接收者")
    @Schema(description = "接收者", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "接收者不能为空")
    private Long userId;

    /**
     * 接收人工号
     */
    @Excel(name = "接收人工号")
    @Schema(description = "接收人工号")
    @Size(max = 16, message = "接收人工号不能超过 16 个字符")
    private String workNumber;

    /**
     * 接收人姓名
     */
    @Excel(name = "接收人姓名")
    @Schema(description = "接收人姓名")
    @Size(max = 30, message = "接收人姓名不能超过 30 个字符")
    private String realName;

    /**
     * 重试次数
     */
    @Excel(name = "重试次数")
    @Schema(description = "重试次数")
    private Long retryCount;

    /**
     * 发送结果
     */
    @Excel(name = "发送结果")
    @Schema(description = "发送结果", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "发送结果不能为空")
    @Size(max = 32, message = "发送结果不能超过 32 个字符")
    private String sendStatus;

    /**
     * 失败原因
     */
    @Excel(name = "失败原因")
    @Schema(description = "失败原因")
    @Size(max = 128, message = "失败原因不能超过 128 个字符")
    private String failureReason;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("taskId", getTaskId())
                .append("channelCode", getChannelCode())
                .append("type", getType())
                .append("applicationCode", getApplicationCode())
                .append("messageTemplateId", getMessageTemplateId())
                .append("messageTemplateParams", getMessageTemplateParams())
                .append("message", getMessage())
                .append("userId", getUserId())
                .append("workNumber", getWorkNumber())
                .append("realName", getRealName())
                .append("retryCount", getRetryCount())
                .append("sendStatus", getSendStatus())
                .append("failureReason", getFailureReason())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createNameBy", getCreateNameBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateNameBy", getUpdateNameBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

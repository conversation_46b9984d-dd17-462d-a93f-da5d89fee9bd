package com.wbyy.message.modules.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.rabbitmq.client.Channel;
import com.wbyy.message.common.constant.ChannelCodeConst;
import com.wbyy.message.modules.listener.dto.SendQwMessageDTO;
import com.wbyy.message.modules.messagesendinfo.domain.MessageSendInfo;
import com.wbyy.message.modules.messagesendinfo.service.IMessageSendInfoService;
import com.wbyy.message.utils.WxSendUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import static com.wbyy.message.common.constant.RabbitmqConst.SEND_MESSAGE_QW_QUEUE;

/**
 * 发送企微消息监听
 *
 * <AUTHOR>
 * @date 2025/7/22 09:12
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SendQwMessageConsumer {
    private final IMessageSendInfoService messageSendInfoService;

    @RabbitListener(queues = SEND_MESSAGE_QW_QUEUE)
    public void consume(Message message, String msg, Channel channel) {
        log.info("队列：{}, 消息: {} -- {}", SEND_MESSAGE_QW_QUEUE, message.toString(), msg);
        SendQwMessageDTO sendQwMessageDTO = JSON.parseObject(msg, SendQwMessageDTO.class);
        log.info("发送企微消息: {}", sendQwMessageDTO);

        String messageBody = "";
        String failureReason = "";
        boolean isOk = true;

        String msgServerTaskId = sendQwMessageDTO.getMsgServerTaskId();
        MessageSendInfo messageSendInfo = messageSendInfoService.getByMsgServerTaskId(msgServerTaskId);

        try {
            if (messageSendInfo == null || "1".equals(messageSendInfo.getSendStatus()))
                messageBody = WxSendUtils.sendTemplateCardMsg(
                        sendQwMessageDTO.getMsgServerTaskId(),
                        sendQwMessageDTO.getQwUserId(),
                        sendQwMessageDTO.getTemplate(),
                        sendQwMessageDTO.getParams(),
                        sendQwMessageDTO.getHorizontalContentParams(),
                        sendQwMessageDTO.getApplicationCode()
                );
        } catch (Exception e) {
            failureReason = e.getMessage();
            isOk = false;
            log.error("发送企微消息失败: {}", failureReason, e);
            throw e;
        } finally {
            // 新增或修改
            if (messageSendInfo != null) {
                messageSendInfo.setMessage(messageBody);
                messageSendInfo.setFailureReason(failureReason);
                messageSendInfo.setRetryCount(messageSendInfo.getRetryCount() + 1);
                messageSendInfo.setSendStatus(isOk ? "0" : "1");
                messageSendInfoService.updateById(messageSendInfo);
            } else {
                messageSendInfoService.save(MessageSendInfo.builder()
                        .msgServerTaskId(sendQwMessageDTO.getMsgServerTaskId())
                        .taskId(sendQwMessageDTO.getTaskId())
                        .type(sendQwMessageDTO.getType())
                        .message(messageBody)
                        .channelCode(ChannelCodeConst.QW)
                        .userId(sendQwMessageDTO.getUserId())
                        .workNumber(sendQwMessageDTO.getWorkNumber())
                        .realName(sendQwMessageDTO.getRealName())
                        .applicationCode(sendQwMessageDTO.getApplicationCode())
                        .failureReason(failureReason)
                        .messageTemplateId(sendQwMessageDTO.getTemplate().getId())
                        .messageTemplateParams(CollUtil.isNotEmpty(sendQwMessageDTO.getParams()) ? JSON.toJSONString(sendQwMessageDTO.getParams()) : null)
                        .retryCount(1L)
                        .sendStatus(isOk ? "0" : "1")
                        .build());
            }
        }
    }
}
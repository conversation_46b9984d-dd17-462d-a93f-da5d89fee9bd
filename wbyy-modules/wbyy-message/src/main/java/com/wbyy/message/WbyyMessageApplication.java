package com.wbyy.message;

import com.wbyy.common.security.annotation.EnableCustomConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.wbyy.common.security.annotation.EnableRyFeignClients;

/**
 * log
 * 
 * <AUTHOR>
 */
@Slf4j
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
public class WbyyMessageApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(WbyyMessageApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  MESSAGE模块启动成功   ლ(´ڡ`ლ)ﾞ  \n");
        log.info("(♥◠‿◠)ﾉﾞ  MESSAGE模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}

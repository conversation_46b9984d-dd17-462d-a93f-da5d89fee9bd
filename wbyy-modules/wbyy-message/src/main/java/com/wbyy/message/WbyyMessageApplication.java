package com.wbyy.message;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.security.annotation.EnableCustomConfig;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.wbyy.common.security.annotation.EnableRyFeignClients;

/**
 * log
 * 
 * <AUTHOR>
 */
@Slf4j
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
public class WbyyMessageApplication
{
    public static void main(String[] args)
    {
        MDC.put(SecurityConstants.TRACE_ID, "message");
        SpringApplication.run(WbyyMessageApplication.class, args);
        log.info("(♥◠‿◠)ﾉﾞ  MESSAGE模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}

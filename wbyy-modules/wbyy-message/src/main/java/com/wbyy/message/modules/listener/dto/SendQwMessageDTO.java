package com.wbyy.message.modules.listener.dto;

import com.alibaba.fastjson2.JSON;
import com.wbyy.message.modules.messagetemplate.domain.MessageTemplate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * 发送企微消息 DTO
 *
 * <AUTHOR>
 * @date 2025/7/22 10:00
 */

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SendQwMessageDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -2713918164096910946L;

    /**
     * 消息服务任务ID
     */
    private String msgServerTaskId;
    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 消息类型
     *
     * @see com.wbyy.message.common.constant.MessageTypeConst
     */
    private String type;

    /**
     * 接收者ID - 用户ID
     */
    private Long userId;

    /**
     * 企微用户ID
     */
    private String qwUserId;

    /**
     * 工号
     */
    private String workNumber;

    /**
     * 真实姓名
     */
    private String realName;


    /**
     * 应用编码
     */
    private String applicationCode;

    /**
     * 企微消息模板
     */
    private MessageTemplate template;

    /**
     * 模板参数
     */
    private Map<String, String> params;

    /**
     * 横向内容参数
     */
    private Map<String, String> horizontalContentParams;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
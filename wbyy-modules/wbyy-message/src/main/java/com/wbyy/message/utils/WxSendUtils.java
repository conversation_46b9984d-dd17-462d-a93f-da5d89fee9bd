package com.wbyy.message.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.wbyy.common.core.constant.AppCodeConst;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.handler.mybatis.bo.ButtonBO;
import com.wbyy.common.core.handler.mybatis.bo.HorizontalContentBO;
import com.wbyy.common.core.utils.SpringUtils;
import com.wbyy.common.qyweixin.config.WxCpConfiguration;
import com.wbyy.common.qyweixin.config.properties.WxCpProperties;
import com.wbyy.message.common.config.properties.MessageProperties;
import com.wbyy.message.modules.messagetemplate.domain.MessageTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.message.WxCpMessageSendResult;
import me.chanjar.weixin.cp.bean.templatecard.HorizontalContent;
import me.chanjar.weixin.cp.bean.templatecard.TemplateCardButton;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 企业微信发送消息工具类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class WxSendUtils implements InitializingBean {

    private static final String PARAM_KEY_PREFIX = "{";
    private static final String PARAM_KEY_SUFFIX = "}";

    @Autowired
    private WxCpProperties wxCpProperties;
    private static MessageProperties messageProperties;

    @Override
    public void afterPropertiesSet() {
        messageProperties = SpringUtils.getBean(MessageProperties.class);
    }


    /**
     * 发送消息
     *
     * @param msgServerTaskId         消息服务 任务ID
     * @param userId                  发送人ID
     * @param template                模板
     * @param params                  参数
     * @param horizontalContentParams 横向标题列表参数
     * @return 消息体
     */
    public static String sendTemplateCardMsg(String msgServerTaskId, String userId,
                                             MessageTemplate template,
                                             Map<String, String> params,
                                             Map<String, String> horizontalContentParams,
                                             String applicationCode) throws ServiceException {
        if (params == null){
            params = new HashMap<>();
        }
        if (horizontalContentParams == null){
            horizontalContentParams = new HashMap<>();
        }
        WxCpService cpService = WxCpConfiguration.getCpService(AppCodeConst.MESSAGE);
        // 拿到所有应用的名称和agentId
        WxCpMessageService messageService = cpService.getMessageService();
        if (messageService == null) {
            throw new ServiceException("企业微信接口异常");
        }

        WxCpMessage message = new WxCpMessage();
        // 来源
        MessageProperties.MsgSourceItem source = getSourceByApplicationCode(applicationCode);
        message.setSourceIconUrl(source.getIconUrl());
        message.setSourceDesc(source.getDesc());
        message.setSourceDescColor(source.getDescColor());

        if (CollUtil.isNotEmpty(messageProperties.getDefaultUserId())) {
            message.setToUser(String.join("|", messageProperties.getDefaultUserId()));
        } else {
            message.setToUser(userId);
        }
        message.setMsgType("template_card");
        message.setAgentId(WxCpConfiguration.getAppConfig(AppCodeConst.MESSAGE).getAgentId());
        // 模板卡片类型，按钮交互型卡片填写"button_interaction"
        message.setCardType("button_interaction");

        // 一级标题
        String title = template.getTitle();
        if (StrUtil.isNotEmpty(title))
            for (Map.Entry<String, String> entry : params.entrySet()) {
                title = title.replaceAll(Pattern.quote(PARAM_KEY_PREFIX + entry.getKey() + PARAM_KEY_SUFFIX),
                        entry.getValue());
            }
        message.setMainTitleTitle(title);

        String titleOverview = template.getTitleOverview();
        if (StrUtil.isNotEmpty(titleOverview)) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                titleOverview = titleOverview.replaceAll(Pattern.quote(PARAM_KEY_PREFIX + entry.getKey() + PARAM_KEY_SUFFIX),
                        entry.getValue());
            }
        }
        message.setMainTitleDesc(titleOverview);

        // 二级普通文本（消息内容）
        String content = template.getContent();
        if (StrUtil.isNotEmpty(content)) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                content = content.replaceAll(Pattern.quote(PARAM_KEY_PREFIX + entry.getKey() + PARAM_KEY_SUFFIX),
                        entry.getValue());
            }
        }
        message.setSubTitleText(content);

        // 模板 横向标题列表配置
        List<HorizontalContentBO> templateHorizontalContentList =
                template.getHorizontalContentList();
        if (CollUtil.isNotEmpty(templateHorizontalContentList)) {

            JSONArray array = new JSONArray();
            JSONObject jsonObject;
            for (HorizontalContentBO item : templateHorizontalContentList) {
                jsonObject = new JSONObject();
                if (StrUtil.isNotEmpty(item.getKeyname())) {
                    jsonObject.put("keyname", horizontalContentParams.get(item.getKeyname()));
                }
                if (StrUtil.isNotEmpty(item.getType())) {
                    jsonObject.put("type", item.getType());
                }
                if (StrUtil.isNotEmpty(item.getValue())) {
                    jsonObject.put("value", horizontalContentParams.get(item.getValue()));
                }
                if (StrUtil.isNotEmpty(item.getUserid())) {
                    jsonObject.put("userid", horizontalContentParams.get(item.getUserid()));
                }
                if (StrUtil.isNotEmpty(item.getUrl())) {
                    for (Map.Entry<String, String> entry : params.entrySet()) {
                        String userValue = horizontalContentParams.get(item.getUrl());
                        if (StrUtil.isEmpty(jsonObject.getString("url"))) {
                            jsonObject.put("url", userValue.replaceAll(Pattern.quote(PARAM_KEY_PREFIX + entry.getKey() + PARAM_KEY_SUFFIX),
                                    entry.getValue()));
                        } else {
                            jsonObject.put("url", jsonObject.getString("url").replaceAll(Pattern.quote(PARAM_KEY_PREFIX + entry.getKey() + PARAM_KEY_SUFFIX),
                                    entry.getValue()));
                        }
                    }
                }
                array.add(jsonObject);
            }

            /*
             * 二级标题+文本列表，该字段可为空数组，但有数据的话需确认对应字段是否必填，列表长度不超过6
             */
            if (CollUtil.isNotEmpty(array)) {
                message.setHorizontalContents(array.toJavaList(HorizontalContent.class));
            }
        }
        /*
            整体卡片的点击跳转事件，text_notice 必填本字段 跳转事件类型，
                1 代表跳转url，
                2 代表打开小程序。text_notice 卡片模版中该字段取值范围为[1,2]

            // 不实现该功能
            message.setCardActionUrl("https://pms-test.wbcro.com/");
            message.setCardActionType(1);
         */

        message.setTaskId(msgServerTaskId);
        message.setEnableIdTrans(true);

        // 表示是否重复消息检查的时间间隔，默认1800s，最大不超过4小时
        if (source.getDuplicateCheckInterval() != null)
            message.setDuplicateCheckInterval(source.getDuplicateCheckInterval());

        // 如果模板设置了按钮信息
        if (CollUtil.isNotEmpty(template.getButtonList())) {
            List<TemplateCardButton> buttons = new ArrayList<>();
            for (ButtonBO buttonBO : template.getButtonList()) {
                TemplateCardButton button = new TemplateCardButton();
                button.setText(buttonBO.getText());
                button.setStyle(buttonBO.getStyle());
                button.setKey(buttonBO.getKey());
                button.setType(buttonBO.getType());
                button.setUrl(buttonBO.getUrl());
                if (StrUtil.isNotEmpty(buttonBO.getUrl())) {
                    for (Map.Entry<String, String> entry : params.entrySet()) {
                        if (StrUtil.isEmpty(button.getUrl())){
                            button.setUrl(buttonBO.getUrl().replaceAll(Pattern.quote(PARAM_KEY_PREFIX + entry.getKey() + PARAM_KEY_SUFFIX),
                                    entry.getValue()));
                        } else {
                            button.setUrl(button.getUrl().replaceAll(Pattern.quote(PARAM_KEY_PREFIX + entry.getKey() + PARAM_KEY_SUFFIX),
                                    entry.getValue()));
                        }
                    }
                }
                buttons.add(button);
            }
            message.setButtons(buttons);
        }

        try {

            log.info("【企微】发送消息 taskId：{}，请求参数：{}", msgServerTaskId, JSON.toJSONString(message));
            WxCpMessageSendResult result = messageService.send(message);
            log.info("【企微】发送消息 taskId：{}，返回结果：{}", msgServerTaskId, JSON.toJSONString(result));

            Integer errCode = result.getErrCode();
            if (errCode != 0) {
                throw new ServiceException("【企业微信】：" + result.getErrMsg());
            }
        } catch (WxErrorException e) {
            log.error("【企微】发送消息 taskId：{}，请求参数：{}", msgServerTaskId, JSON.toJSONString(message));
            log.error("【企微】接口异常 taskId：{}，异常描述: {}", msgServerTaskId, e.getMessage(), e);
            throw new ServiceException("【企业微信】：" + e.getMessage());
        }
        return JSON.toJSONString(message);
    }

    private static MessageProperties.MsgSourceItem getSourceByApplicationCode(String applicationCode) {
        if (messageProperties.getAuthApp() == null) throw new ServiceException("应用ID不存在");
        if (!messageProperties.getAuthApp().contains(applicationCode))
            throw new ServiceException("该服务未授权，请联系管理员");

        return messageProperties.getMsgSource().get(applicationCode);

    }
}

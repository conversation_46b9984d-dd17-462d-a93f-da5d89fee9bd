package com.wbyy.message.modules.messagesendinfo.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.message.modules.messagesendinfo.domain.MessageSendInfo;
import com.wbyy.message.modules.messagesendinfo.mapper.MessageSendInfoMapper;
import com.wbyy.message.modules.messagesendinfo.service.IMessageSendInfoService;
import com.wbyy.message.modules.messagetemplate.service.IMessageTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 消息发送记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageSendInfoServiceImpl extends ServiceImpl<MessageSendInfoMapper, MessageSendInfo> implements IMessageSendInfoService
{
    private final MessageSendInfoMapper messageSendInfoMapper;
    private final IMessageTemplateService messageTemplateService;

    /**
     * 查询消息发送记录
     *
     * @param id 消息发送记录主键
     * @return 消息发送记录
     */
    @Override
    public MessageSendInfo selectById(Long id)
    {
        return this.getById(id);
    }

    /**
     * 查询消息发送记录列表
     *
     * @param messageSendInfo 消息发送记录
     * @return 消息发送记录
     */
    @Override
    public List<MessageSendInfo> selectList(MessageSendInfo messageSendInfo)
    {
        LambdaQueryWrapper<MessageSendInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotEmpty(messageSendInfo.getTaskId()),
                MessageSendInfo::getTaskId, messageSendInfo.getTaskId());

        wrapper.eq(StrUtil.isNotEmpty(messageSendInfo.getChannelCode()),
                MessageSendInfo::getChannelCode, messageSendInfo.getChannelCode());

        wrapper.eq(messageSendInfo.getType() != null,
                MessageSendInfo::getType, messageSendInfo.getType());

        wrapper.eq(messageSendInfo.getMessageTemplateId() !=null,
                MessageSendInfo::getMessageTemplateId, messageSendInfo.getMessageTemplateId());
        wrapper.eq(StrUtil.isNotEmpty(messageSendInfo.getWorkNumber()),
                MessageSendInfo::getWorkNumber, messageSendInfo.getWorkNumber());

        wrapper.like(StrUtil.isNotEmpty(messageSendInfo.getRealName()),
                MessageSendInfo::getRealName, messageSendInfo.getRealName());

        wrapper.eq(StrUtil.isNotEmpty(messageSendInfo.getSendStatus()),
                MessageSendInfo::getSendStatus, messageSendInfo.getSendStatus());

        wrapper.eq(StrUtil.isNotEmpty(messageSendInfo.getApplicationCode()),
                MessageSendInfo::getApplicationCode, messageSendInfo.getApplicationCode());
        wrapper.orderByDesc(MessageSendInfo::getCreateTime);

        Map<Long, String> map = messageTemplateService.findMap();
        List<MessageSendInfo> list = this.list(wrapper);
        if (CollUtil.isNotEmpty(list)){
            list.forEach(item -> item.setMessageTemplateName(map.get(item.getMessageTemplateId())));
        }
        return list;
    }

    @Override
    public MessageSendInfo getByMsgServerTaskId(String msgServerTaskId) {
        if (StrUtil.isEmpty(msgServerTaskId)){
            return null;
        }

        LambdaQueryWrapper<MessageSendInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MessageSendInfo::getMsgServerTaskId, msgServerTaskId);
        return this.getOne(wrapper);
    }

    /**
     * 新增消息发送记录
     * 
     * @param messageSendInfo 消息发送记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(MessageSendInfo messageSendInfo)
    {
        return this.save(messageSendInfo);
    }

    /**
     * 修改消息发送记录
     * 
     * @param messageSendInfo 消息发送记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MessageSendInfo messageSendInfo)
    {
        return this.updateById(messageSendInfo);
    }

    /**
     * 批量删除消息发送记录
     * 
     * @param ids 需要删除的消息发送记录主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids)
    {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

}

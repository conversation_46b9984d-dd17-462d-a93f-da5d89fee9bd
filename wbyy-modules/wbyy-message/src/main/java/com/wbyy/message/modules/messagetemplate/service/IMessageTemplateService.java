package com.wbyy.message.modules.messagetemplate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.message.modules.messagetemplate.domain.MessageTemplate;

import java.util.List;
import java.util.Map;

/**
 * 消息模板Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IMessageTemplateService  extends IService<MessageTemplate> {
    /**
     * 查询消息模板
     *
     * @param id 消息模板主键
     * @return 消息模板
     */
    MessageTemplate selectById(Long id);

    /**
     * 查询消息模板列表
     *
     * @param messageTemplate 消息模板
     * @return 消息模板集合
     */
    List<MessageTemplate> selectList(MessageTemplate messageTemplate);

    /**
     * 新增消息模板
     *
     * @param messageTemplate 消息模板
     * @return 结果
     */
    boolean insert(MessageTemplate messageTemplate);

    /**
     * 修改消息模板
     *
     * @param messageTemplate 消息模板
     * @return 结果
     */
    boolean update(MessageTemplate messageTemplate);

    /**
     * 批量删除消息模板
     *
     * @param ids 需要删除的消息模板主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 查询模板id name 的 map
     * @return
     */
    Map<Long, String> findMap();

    /**
     * 查询所有模板
     * @return
     */
    List<MessageTemplate> findAll();


    /**
     * 根据模板Code 查询模板
     * @return  模板
     */
    MessageTemplate getByCode(String messageTemplateCode);
}

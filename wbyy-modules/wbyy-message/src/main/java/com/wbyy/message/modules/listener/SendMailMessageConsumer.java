package com.wbyy.message.modules.listener;

import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import static com.wbyy.message.common.constant.RabbitmqConst.SEND_MESSAGE_MAIL_QUEUE;

/**
 * 发送邮件消息监听
 *
 * <AUTHOR>
 * @date 2025/7/22 09:12
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SendMailMessageConsumer {

    @RabbitListener(queues = SEND_MESSAGE_MAIL_QUEUE)
    public void consume(Message message, String msg, Channel channel) {
        log.info("队列：{}, 消息: {} -- {}", SEND_MESSAGE_MAIL_QUEUE, message.toString(), msg);

    }
}
package com.wbyy.message.modules.send.service.impl;

import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.message.api.domain.ApiSenMessage;
import com.wbyy.message.api.domain.SendVO;
import com.wbyy.message.modules.send.service.ISendService;
import org.springframework.stereotype.Service;

import static com.wbyy.message.common.constant.ChannelCodeConst.EMAIL;

/**
 * <AUTHOR>
 */
@Service
public class MailServiceImpl implements ISendService {
    @Override
    public String getChannelCode() {
        return EMAIL;
    }

    @Override
    public SendVO send(ApiSenMessage dto) {
        throw new ServiceException("暂未实现");
    }
}

package com.wbyy.message.modules.messagesendinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.message.modules.messagesendinfo.domain.MessageSendInfo;

import java.util.List;

/**
 * 消息发送记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IMessageSendInfoService  extends IService<MessageSendInfo> {
    /**
     * 查询消息发送记录
     *
     * @param id 消息发送记录主键
     * @return 消息发送记录
     */
    MessageSendInfo selectById(Long id);

    /**
     * 查询消息发送记录列表
     *
     * @param messageSendInfo 消息发送记录
     * @return 消息发送记录集合
     */
    List<MessageSendInfo> selectList(MessageSendInfo messageSendInfo);

    /**
     * 新增消息发送记录
     *
     * @param messageSendInfo 消息发送记录
     * @return 结果
     */
    boolean insert(MessageSendInfo messageSendInfo);

    /**
     * 修改消息发送记录
     *
     * @param messageSendInfo 消息发送记录
     * @return 结果
     */
    boolean update(MessageSendInfo messageSendInfo);

    /**
     * 批量删除消息发送记录
     *
     * @param ids 需要删除的消息发送记录主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 根据消息服务任务ID查询消息发送记录
     *
     * @param msgServerTaskId 消息服务任务ID
     * @return 消息发送记录
     */
    MessageSendInfo getByMsgServerTaskId(String msgServerTaskId);

}

package com.wbyy.message.modules.test.controller;

import com.wbyy.common.core.domain.R;
import com.wbyy.common.log.annotation.ApiParamLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * API参数拦截测试控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test/api-param")
@Tag(name = "API参数拦截测试")
@Slf4j
public class ApiParamTestController {

    @GetMapping("/simple")
    @Operation(summary = "简单GET测试")
    @ApiParamLog(value = "简单GET测试", logRequest = true, logResponse = true)
    public R<String> simpleGet(@RequestParam(required = false) String name, 
                               @RequestParam(required = false) Integer age) {
        log.info("执行简单GET测试，参数：name={}, age={}", name, age);
        return R.ok("Hello " + (name != null ? name : "World") + ", age: " + (age != null ? age : "unknown"));
    }

    @PostMapping("/simple")
    @Operation(summary = "简单POST测试")
    @ApiParamLog(value = "简单POST测试", logRequest = true, logResponse = true, logHeaders = true)
    public R<Map<String, Object>> simplePost(@RequestBody TestRequest request) {
        log.info("执行简单POST测试，参数：{}", request);
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "处理成功");
        response.put("requestData", request);
        response.put("timestamp", LocalDateTime.now());
        response.put("processedBy", "ApiParamTestController");
        
        return R.ok(response);
    }

    @PostMapping("/with-sensitive")
    @Operation(summary = "包含敏感信息的POST测试")
    @ApiParamLog(value = "敏感信息POST测试", 
                 logRequest = true, 
                 logResponse = true, 
                 excludeParams = {"password", "secret", "token"})
    public R<Map<String, Object>> postWithSensitive(@RequestBody SensitiveRequest request) {
        log.info("执行包含敏感信息的POST测试");
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "敏感信息已处理");
        response.put("username", request.getUsername());
        response.put("timestamp", LocalDateTime.now());
        // 不返回敏感信息
        
        return R.ok(response);
    }

    @PostMapping("/error-test")
    @Operation(summary = "异常测试")
    @ApiParamLog(value = "异常测试", logRequest = true, logResponse = true)
    public R<String> errorTest(@RequestBody TestRequest request) {
        log.info("执行异常测试");
        
        if ("error".equals(request.getName())) {
            throw new RuntimeException("这是一个测试异常");
        }
        
        return R.ok("正常处理完成");
    }

    @Data
    public static class TestRequest {
        private String name;
        private Integer age;
        private String email;
        private String description;
    }

    @Data
    public static class SensitiveRequest {
        private String username;
        private String password;
        private String email;
        private String secret;
        private String token;
    }
}

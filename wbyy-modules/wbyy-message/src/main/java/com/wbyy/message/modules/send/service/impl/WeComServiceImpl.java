package com.wbyy.message.modules.send.service.impl;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.ServletUtils;
import com.wbyy.common.rabbitmq.service.RabbitService;
import com.wbyy.message.api.domain.ApiSenMessage;
import com.wbyy.message.api.domain.SendVO;
import com.wbyy.message.modules.listener.dto.SendQwMessageDTO;
import com.wbyy.message.modules.messagesendinfo.service.IMessageSendInfoService;
import com.wbyy.message.modules.messagetemplate.domain.MessageTemplate;
import com.wbyy.message.modules.messagetemplate.service.IMessageTemplateService;
import com.wbyy.message.modules.send.service.ISendService;
import com.wbyy.system.api.UserApi;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.api.utils.WbyyApiUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.wbyy.message.common.constant.ChannelCodeConst.QW;
import static com.wbyy.message.common.constant.RabbitmqConst.SEND_MESSAGE_EXCHANGE;
import static com.wbyy.message.common.constant.RabbitmqConst.SEND_QW_MESSAGE_ROUTING_KEY;


/**
 * 企微消息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WeComServiceImpl implements ISendService {

    private final IMessageTemplateService messageTemplateService;
    private final IMessageSendInfoService messageSendInfoService;
    private final UserApi userApi;
    private final RabbitService rabbitService;


    @Override
    public String getChannelCode() {
        return QW;
    }

    @Override
    public SendVO send(ApiSenMessage dto) {
        // 消息接收人ID
        Long userId = dto.getUserId();

        // 发送消息的参数
        Map<String, String> params = dto.getParams();
        // 横向内容列表 参数
        Map<String, String> horizontalContentParams = dto.getHorizontalContentParams();
        // 客户端 任务ID
        String taskId = dto.getTaskId();

        // 服务端 任务ID
        String msgServerTaskId = IdUtil.getSnowflakeNextId()
                + "-" + UUID.fastUUID();
        // 模板ID
        String messageTemplateCode = dto.getMessageTemplateCode();
        MessageTemplate template = messageTemplateService.getByCode(messageTemplateCode);
        // 检查模板是否存在 及 状态
        if (template == null) throw new ServiceException("模板不存在");
        if (!"0".equals(template.getStatus())) throw new ServiceException("模板状态有误，请联系管理员");

        List<ApiUser> listData = WbyyApiUtils.getListData(userApi.getUserInfoByUserIds(List.of(userId), SecurityConstants.INNER));
        Map<Long, String> userIdRealNameMap = WbyyApiUtils.userRealNameMap(listData);
        Map<Long, String> userIdWorkNumberMap = WbyyApiUtils.userWorkNumberMap(listData);
        Map<Long, String> userQwUserIdMap = WbyyApiUtils.userQwUserIdMap(listData);

        // 生产者
        rabbitService.sendMessage(SEND_MESSAGE_EXCHANGE, SEND_QW_MESSAGE_ROUTING_KEY,
                SendQwMessageDTO.builder()
                        .msgServerTaskId(msgServerTaskId)
                        .taskId(taskId)
                        .type(dto.getType())
                        .userId(userId)
                        .qwUserId(userQwUserIdMap.get(userId))
                        .workNumber(userIdWorkNumberMap.get(userId))
                        .realName(userIdRealNameMap.get(userId))
                        .applicationCode(ServletUtils.getHeader(Objects.requireNonNull(ServletUtils.getRequest()), SecurityConstants.APPLICATION_CODE))
                        .template(template)
                        .params(params)
                        .horizontalContentParams(horizontalContentParams)
                        .build().toString()
        );

        return SendVO.builder()
                .msgServerTaskId(msgServerTaskId)
                .isOk(true)
                .build();
    }
}

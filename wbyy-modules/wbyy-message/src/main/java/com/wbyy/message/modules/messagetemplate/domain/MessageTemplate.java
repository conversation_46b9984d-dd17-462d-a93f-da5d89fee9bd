package com.wbyy.message.modules.messagetemplate.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.common.core.handler.mybatis.ButtonListTypeHandler;
import com.wbyy.common.core.handler.mybatis.HorizontalContentListTypeHandler;
import com.wbyy.common.core.handler.mybatis.KeyNameListTypeHandler;
import com.wbyy.common.core.handler.mybatis.bo.ButtonBO;
import com.wbyy.common.core.handler.mybatis.bo.HorizontalContentBO;
import com.wbyy.common.core.handler.mybatis.bo.KeyNameBO;
import com.wbyy.orm.core.domain.BaseAuditEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.util.List;

/**
 * 消息模板对象 message_template
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "message_template", autoResultMap = true)
@Schema(description = "消息模板实体类")
@EqualsAndHashCode(callSuper = true)
public class MessageTemplate extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /**
     * 应用编码
     */
    @Excel(name = "应用编码")
    @Schema(description = "应用编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "应用编码不能为空")
    @Size(max = 64, message = "应用编码不能超过 64 个字符")
    private String applicationCode;

    /**
     * 模板名称
     */
    @Excel(name = "模板名称")
    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模板名称不能为空")
    @Size(max = 64, message = "模板名称不能超过 64 个字符")
    private String name;

    /**
     * 模板编码
     */
    @Excel(name = "模板编码")
    @Schema(description = "模板编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模板编码不能为空")
    @Size(max = 36, message = "模板编码不能超过 36 个字符")
    private String code;

    /**
     * 标题
     */
    @Excel(name = "标题")
    @Schema(description = "标题")
    @Size(max = 36, message = "标题不能超过 36 个字符")
    private String title;

    /**
     * 标题概述
     */
    @Excel(name = "标题概述")
    @Schema(description = "标题概述")
    @Size(max = 100, message = "标题概述不能超过 100 个字符")
    private String titleOverview;

    /**
     * 标题概述参数
     */
    @Excel(name = "标题概述参数")
    @Schema(description = "标题概述参数")
    @TableField(typeHandler = KeyNameListTypeHandler.class)
    private List<KeyNameBO> titleOverviewParams;

    /**
     * 模板责任人
     */
    @Excel(name = "模板责任人")
    @Schema(description = "模板责任人", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模板责任人不能为空")
    @Size(max = 64, message = "模板责任人不能超过 64 个字符")
    private String dutyUserName;

    /**
     * 模板状态 （0启用 1禁用）
     */
    @Excel(name = "模板状态")
    @Schema(description = "模板状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模板状态不能为空")
    @Size(max = 4, message = "模板状态不能超过 4 个字符")
    private String status;

    /**
     * 消息内容
     */
    @Excel(name = "消息内容")
    @Schema(description = "消息内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "消息内容不能为空")
    private String content;


    /**
     * 消息内容参数
     */
    @Excel(name = "消息内容参数")
    @Schema(description = "消息内容参数")
    @TableField(typeHandler = KeyNameListTypeHandler.class)
    private List<KeyNameBO> contentParams;

    /**
     * 横向标题列表
     */
    @Excel(name = "横向标题列表")
    @Schema(description = "横向标题列表")
    @TableField(typeHandler = HorizontalContentListTypeHandler.class)
    private List<HorizontalContentBO> horizontalContentList;

    /**
     * 按钮配置
     */
    @Excel(name = "按钮配置")
    @Schema(description = "按钮配置")
    @TableField(typeHandler = ButtonListTypeHandler.class)
    private List<ButtonBO> buttonList;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("applicationCode", getApplicationCode())
                .append("name", getName())
                .append("code", getCode())
                .append("title", getTitle())
//                .append("subTitle", getSubTitle())
                .append("dutyUserName", getDutyUserName())
                .append("status", getStatus())
                .append("content", getContent())
                .append("contentParams", getContentParams())
                .append("params", getParams())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createNameBy", getCreateNameBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateNameBy", getUpdateNameBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

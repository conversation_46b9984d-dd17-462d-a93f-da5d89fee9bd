package com.wbyy.message.modules.messagesendinfo.controller;

import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.message.modules.messagesendinfo.domain.MessageSendInfo;
import com.wbyy.message.modules.messagesendinfo.service.IMessageSendInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息发送记录Controller
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "消息发送记录")
@RequestMapping("/message/send/info")
public class MessageSendInfoController extends BaseController {

    private final IMessageSendInfoService messageSendInfoService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("message:send:list")
    @GetMapping("/page")
    public TableDataInfo<MessageSendInfo> page(@ParameterObject MessageSendInfo messageSendInfo) {
        startPage();
        List<MessageSendInfo> list = messageSendInfoService.selectList(messageSendInfo);
        return getDataTable(list);
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("message:send:query")
    @GetMapping(value = "/{id}")
    public R<MessageSendInfo> getInfo(@PathVariable("id") Long id) {
        return R.ok(messageSendInfoService.selectById(id));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("message:send:remove")
    @Log(title = "消息发送记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        return R.ok(messageSendInfoService.deleteByIds(ids));
    }
}

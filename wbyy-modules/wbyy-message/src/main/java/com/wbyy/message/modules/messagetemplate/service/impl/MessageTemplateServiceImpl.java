package com.wbyy.message.modules.messagetemplate.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.message.modules.messagetemplate.domain.MessageTemplate;
import com.wbyy.message.modules.messagetemplate.mapper.MessageTemplateMapper;
import com.wbyy.message.modules.messagetemplate.service.IMessageTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消息模板Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageTemplateServiceImpl extends ServiceImpl<MessageTemplateMapper, MessageTemplate> implements IMessageTemplateService
{
    private final MessageTemplateMapper messageTemplateMapper;

    /**
     * 查询消息模板
     * 
     * @param id 消息模板主键
     * @return 消息模板
     */
    @Override
    public MessageTemplate selectById(Long id)
    {
        return this.getById(id);
    }

    @Override
    public MessageTemplate getByCode(String messageTemplateCode) {
        LambdaQueryWrapper<MessageTemplate> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MessageTemplate::getCode, messageTemplateCode)
                .eq( MessageTemplate::getStatus, "0");
        MessageTemplate one = this.getOne(wrapper, false);
        if  (one == null) {
            throw new ServiceException("模板不存在或未启用");
        }
        return one;
    }

    /**
     * 查询消息模板列表
     * 
     * @param messageTemplate 消息模板
     * @return 消息模板
     */
    @Override
    public List<MessageTemplate> selectList(MessageTemplate messageTemplate)
    {
        LambdaQueryWrapper<MessageTemplate> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotEmpty(messageTemplate.getName()),MessageTemplate::getName, messageTemplate.getName());
        wrapper.like(StrUtil.isNotEmpty(messageTemplate.getCode()),MessageTemplate::getCode, messageTemplate.getCode());
        wrapper.like(StrUtil.isNotEmpty(messageTemplate.getTitle()),MessageTemplate::getTitle, messageTemplate.getTitle());
        wrapper.like(StrUtil.isNotEmpty(messageTemplate.getDutyUserName()),MessageTemplate::getDutyUserName, messageTemplate.getDutyUserName());
        wrapper.eq(StrUtil.isNotEmpty(messageTemplate.getStatus()),MessageTemplate::getStatus, messageTemplate.getStatus());
        wrapper.eq(StrUtil.isNotEmpty(messageTemplate.getApplicationCode()),MessageTemplate::getApplicationCode, messageTemplate.getApplicationCode());
        wrapper.orderByDesc(MessageTemplate::getCreateTime);
        return this.list(wrapper);
    }

    @Override
    public Map<Long, String> findMap() {
        LambdaQueryWrapper<MessageTemplate> wrapper = Wrappers.lambdaQuery();
        wrapper.select(MessageTemplate::getId, MessageTemplate::getName);
        wrapper.orderByDesc(MessageTemplate::getCreateTime);
        return this.list(wrapper).stream().collect(Collectors.toMap(MessageTemplate::getId, MessageTemplate::getName));
    }

    @Override
    public List<MessageTemplate> findAll() {
        LambdaQueryWrapper<MessageTemplate> wrapper = Wrappers.lambdaQuery();
        wrapper.select(MessageTemplate::getId, MessageTemplate::getName);
        wrapper.orderByDesc(MessageTemplate::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 新增消息模板
     * 
     * @param messageTemplate 消息模板
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(MessageTemplate messageTemplate)
    {
        return this.save(messageTemplate);
    }

    /**
     * 修改消息模板
     * 
     * @param messageTemplate 消息模板
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MessageTemplate messageTemplate)
    {
        return this.updateById(messageTemplate);
    }

    /**
     * 批量删除消息模板
     * 
     * @param ids 需要删除的消息模板主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids)
    {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

}

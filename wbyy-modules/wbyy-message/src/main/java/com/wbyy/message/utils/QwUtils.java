package com.wbyy.message.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 *
 */
@Data
@Slf4j
@Component
public class QwUtils {

    /**
     * 修正乱码问题
     *
     * @return
     */
    public static Map<String, String> amendCodeMap(Map<String, String[]> requestParams) {

        Map<String, String> params = new HashMap<String, String>();
        for (Iterator iter = requestParams.keySet()
                .iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            //如果出现乱码则生成这个
            //            try {
            //                valueStr = new String(valueStr.getBytes("ISO-8859-1"), "UTF-8");
            //            } catch (UnsupportedEncodingException e) {
            //                e.printStackTrace();
            //            }
            params.put(name, valueStr);
        }
        return params;
    }
}

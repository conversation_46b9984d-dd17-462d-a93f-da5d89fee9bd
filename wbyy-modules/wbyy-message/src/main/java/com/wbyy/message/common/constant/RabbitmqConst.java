package com.wbyy.message.common.constant;

/**
 * rabbit 相关常量
 *
 * <AUTHOR>
 * @date 2025/7/22 09:25
 */
public interface RabbitmqConst {

    /**
     * 交换机定义
     */
    // message - 发布消息 交换机
    public static final String SEND_MESSAGE_EXCHANGE = "wbyy.message.exchange";


    /**
     * 队列定义
     */
    // message - 发送 企微 消息队列
    public static final String SEND_MESSAGE_QW_QUEUE = "send-message-qw-queue";
    // message - 发送 短信 消息队列
    public static final String SEND_MESSAGE_TEXTMESSAGE_QUEUE = "send-message-textMessage-queue";
    // message - 发送 邮件 消息队列
    public static final String SEND_MESSAGE_MAIL_QUEUE = "send-message-mail-queue";


    /**
     * 路由key定义
     */
    // message - 发送 企微 消息key
    public static final String SEND_QW_MESSAGE_ROUTING_KEY = "send.message.qw";
    // message - 发送 短信 消息key
    public static final String SEND_TEXTMESSAGE_MESSAGE_ROUTING_KEY = "send.message.textMessage";
    // message - 发送 邮件 消息key
    public static final String SEND_MAIL_MESSAGE_ROUTING_KEY = "send.message.mail";

}
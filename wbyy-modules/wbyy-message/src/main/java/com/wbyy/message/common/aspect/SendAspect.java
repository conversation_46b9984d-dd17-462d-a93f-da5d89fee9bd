package com.wbyy.message.common.aspect;

import com.alibaba.fastjson2.JSON;
import com.wbyy.message.api.domain.ApiSenMessage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;


/**
 * 发送方法切面
 *
 * <AUTHOR>
 * @date 2025/6/30 09:21
 */
@Component
@Aspect
@Slf4j
@AllArgsConstructor
public class SendAspect {

    /**
     * 发送消息切面
     *
     * @param joinPoint 切点
     * @return Object
     * <AUTHOR>
     * @date 2025/6/30 09:21
     */
    @Around("execution(* com.wbyy.message.modules.send.service.impl.*.send(..))")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        log.debug("[发送消息]切面] 类名：{}.send(). 开始执行...", className);
        long startTime = System.currentTimeMillis();

        Object[] args = joinPoint.getArgs();
        ApiSenMessage sendDTO = null;

        if (args != null && args.length > 0) {
            sendDTO = JSON.parseObject(JSON.toJSONString(args[0]), ApiSenMessage.class);
            log.debug("[发送消息]切面] 类名：{}.send(). 入参：{}", className, JSON.toJSONString(args));
        }
        Object result = null;
        String errorMsg = null;
        try {
            result = joinPoint.proceed();
            log.debug("[发送消息]切面] 类名：{}.send(). 执行完毕...", className);
        } catch (Throwable e) {
            errorMsg = e.getMessage();
            log.error("[发送消息]切面] 类名：{}.send(). 执行异常... \n 入参：{}", className, sendDTO != null ? JSON.toJSONString(sendDTO) : "没有参数", e);
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            log.debug("[发送消息]切面] 类名：{}.send(). 执行完毕，执行结果：{}, 耗时：{}ms", className, (result != null ? result : "没有结果返回"), endTime - startTime);
        }
        return result;
    }

}

package com.wbyy.message.modules.send.controller;

import com.alibaba.fastjson2.JSON;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.message.api.SendMessageApi;
import com.wbyy.message.api.domain.ApiSenMessage;
import com.wbyy.message.api.domain.SendVO;
import com.wbyy.message.modules.send.service.ISendService;
import com.wbyy.message.utils.WxSendUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "消息发送")
@RequestMapping("/send")
@Slf4j
public class SendController implements SendMessageApi {

    private final List<ISendService> sendServices;
    private final WxSendUtils wxSendUtils;

    @PostMapping
    @Operation(summary = "发送消息")
    @RequiresPermissions("message:message:send")
    @InnerAuth
    @Log(title = "发送消息", businessType = BusinessType.SEND)
    public R<SendVO> send(@RequestBody @Validated ApiSenMessage dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        log.debug("【发送消息】消息体：{}", JSON.toJSONString(dto));
        ISendService sendService = sendServices
                .stream().filter(item -> item.getChannelCode().equals(dto.getChannelCode()))
                .findFirst().orElse(null);
        if (sendService != null) {

            return R.ok(sendService.send(dto));
        } else {
            throw new ServiceException("不支持的渠道");
        }

    }
}

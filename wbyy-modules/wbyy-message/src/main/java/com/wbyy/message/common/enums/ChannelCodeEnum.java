package com.wbyy.message.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChannelCodeEnum implements Serializable {
    // 企微、邮箱、短信
    QW("qw", "企业微信"),
    EMAIL("email", "邮件"),
    TEXT_MESSAGE("text_message", "短信"),
    ;

    @JsonValue
    @EnumValue
    private final String code;

    private final String label;

    @JsonCreator
    public static ChannelCodeEnum forValue(String value) {
        return ChannelCodeEnum.fromCode(value);
    }

    public static ChannelCodeEnum fromCode(String code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equalsIgnoreCase(code))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的渠道编码"));
    }

}

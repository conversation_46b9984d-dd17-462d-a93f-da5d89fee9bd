package com.wbyy.message.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MessageTypeEnum implements Serializable {
    // 单发、群发
    SINGLE("single", "单发"),
    MASS_SEND("mass_send", "邮件"),

    ;

    @JsonValue
    @EnumValue
    private final String code;

    private final String label;


    @JsonCreator
    public static MessageTypeEnum forValue(String value) {
        return MessageTypeEnum.fromCode(value);
    }

    public static MessageTypeEnum fromCode(String code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equalsIgnoreCase(code))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的消息类型"));
    }
}

package com.wbyy.gen;

import lombok.extern.slf4j.Slf4j;

import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.wbyy.common.security.annotation.EnableCustomConfig;
import com.wbyy.common.security.annotation.EnableRyFeignClients;

/**
 * 代码生成
 * 
 * <AUTHOR>
 */
@Slf4j
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
public class WbyyGenApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(WbyyGenApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  代码生成模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
        log.info("(♥◠‿◠)ﾉﾞ  代码生成模块启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}

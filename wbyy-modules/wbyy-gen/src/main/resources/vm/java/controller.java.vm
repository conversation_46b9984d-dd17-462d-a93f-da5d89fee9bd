package ${packageName}.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import ${packageName}.domain.${ClassName}PO;
import ${packageName}.service.I${ClassName}Service;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.utils.poi.ExcelUtil;
#if($table.crud || $table.sub)
import com.wbyy.common.core.web.page.TableDataInfo;
#elseif($table.tree)
#end

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "${functionName}")
@RequestMapping("/${controlPath}")
public class ${ClassName}Controller extends BaseController {

    private final I${ClassName}Service ${className}Service;

    @Operation(summary = "分页列表")
    @RequiresPermissions("${permissionPrefix}:list")
    @GetMapping("/page")
    #if($table.crud || $table.sub)
    public TableDataInfo<${ClassName}PO> page(@ParameterObject ${ClassName}PO po) {
        startPage();
        List<${ClassName}PO> list = ${className}Service.selectList(po);
        return getDataTable(list);
    }
    #elseif($table.tree)
    public R<List<${ClassName}PO>> list(${ClassName}PO po) {
        List<${ClassName}PO> list = ${className}Service.selectList(po);
        return R.ok(list);
    }
    #end

    @Operation(summary = "导出列表")
    @RequiresPermissions("${permissionPrefix}:export")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ${ClassName}PO po) {
        List<${ClassName}PO> list = ${className}Service.selectList(po);
        ExcelUtil<${ClassName}PO> util = new ExcelUtil<${ClassName}PO>(${ClassName}PO.class);
        util.exportExcel(response, list, "${functionName}数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("${permissionPrefix}:query")
    @GetMapping(value = "/{${pkColumn.javaField}}")
    public R<${ClassName}PO> getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return R.ok(${className}Service.selectBy${pkColumn.capJavaField}(${pkColumn.javaField}));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("${permissionPrefix}:add")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated ${ClassName}PO po) {
        return R.ok(${className}Service.insert(po));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("${permissionPrefix}:edit")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated ${ClassName}PO po) {
        return R.ok(${className}Service.update(po));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("${permissionPrefix}:remove")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @DeleteMapping("/{${pkColumn.javaField}s}")
    public R<Boolean> remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return R.ok(${className}Service.deleteBy${pkColumn.capJavaField}s(${pkColumn.javaField}s));
    }
}

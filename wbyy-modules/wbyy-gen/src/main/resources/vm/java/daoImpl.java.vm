package ${packageName}.dao.impl;

import cn.hutool.core.util.StrUtil;
import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
#foreach ($column in $columns)
#if($column.javaField == 'createTime' || $column.javaField == 'updateTime')
#break
#end
#end
import org.springframework.stereotype.Service;
#if($table.sub)
import java.util.ArrayList;

import com.wbyy.common.core.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import ${packageName}.domain.${subClassName};
#end
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.dao.I${ClassName}Dao;
import ${packageName}.domain.${ClassName}PO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

/**
 * ${functionName}Dao实现层
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ${ClassName}DaoImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}PO> implements I${ClassName}Dao {

    @Override
    public ${ClassName}PO selectBy${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField}) {
        return this.getById(${pkColumn.javaField});
    }

    @Override
    public List<${ClassName}PO> selectList(${ClassName}PO po) {
        return this.findByPO(po);
    }

    @Override
    public List<${ClassName}PO> findByPO(${ClassName}PO po) {
        if (po == null) {
            return List.of();
        }
        LambdaQueryWrapper<${ClassName}PO> wrapper = Wrappers.lambdaQuery();
#foreach ($column in $columns)
    #if(!$table.isSuperColumn($column.javaFieldFirstUpperCase))
        #if($column.list)
            #if($column.javaType == 'String')
        wrapper.eq(StrUtil.isNotEmpty(po.get${column.javaFieldFirstUpperCase}()), ${ClassName}PO::get${column.javaFieldFirstUpperCase}, po.get${column.javaFieldFirstUpperCase}());
            #else
        wrapper.eq(po.get${column.javaFieldFirstUpperCase}() != null, ${ClassName}PO::get${column.javaFieldFirstUpperCase}, po.get${column.javaFieldFirstUpperCase}());
            #end
        #end
    #end
#end
        wrapper.orderByDesc(${ClassName}PO::getId);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(${ClassName}PO po) {
        #if($table.sub)
        // int rows = baseMapper.insert${ClassName}(po);
        // insert${subClassName}(po);
        return rows;
        #else
        return this.save(po);
        #end
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(${ClassName}PO po) {
        #if($table.sub)
        // baseMapper.delete${subClassName}By${subTableFkClassName}(${className}.get${pkColumn.capJavaField}());
        // insert${subClassName}(po);
        #end
        return this.updateById(po);
    }

    /**
     * 批量删除${functionName}
     *
     * @param ${pkColumn.javaField}s 需要删除的${functionName}主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBy${pkColumn.capJavaField}s(${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        #if($table.sub)
        baseMapper.delete${subClassName}By${subTableFkClassName}s(${pkColumn.javaField}s);
        #end
        return this.removeBatchByIds(Arrays.asList(${pkColumn.javaField}s));
    }

    #if($table.sub)
    /**
     * 新增${subTable.functionName}信息
     *
     * @param ${className} ${functionName}对象
     */
    /**
    @Transactional(rollbackFor = Exception.class)
    public boolean insert${subClassName}(${ClassName}PO po) {
        List<${subClassName}> ${subclassName}List = ${className}.get${subClassName}List();
        ${pkColumn.javaType} ${pkColumn.javaField} = ${className}.get${pkColumn.capJavaField}();
        if (StringUtils.isNotNull(${subclassName}List)) {
            List<${subClassName}> list = new ArrayList<${subClassName}>();
            for (${subClassName} ${subclassName} :${subclassName}List) {
                ${subclassName}.set${subTableFkClassName}(${pkColumn.javaField});
                list.add(${subclassName});
            }
            if (list.size() > 0) {
                baseMapper.batch${subClassName}(list);
            }
        }
    }
    */
    #end
}

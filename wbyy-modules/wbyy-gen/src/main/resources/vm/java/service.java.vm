package ${packageName}.service;

import java.util.List;

import ${packageName}.domain.${ClassName}PO;

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service {
    /**
     * 查询${functionName}
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return ${functionName}
     */
    ${ClassName}PO selectBy${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField});

    /**
     * 查询${functionName}列表
     *
     * @param po ${functionName}
     * @return ${functionName}集合
     */
    List<${ClassName}PO> selectList(${ClassName}PO po);

    /**
     * 新增${functionName}
     *
     * @param po ${functionName}
     * @return 结果
     */
    boolean insert(${ClassName}PO po);

    /**
     * 修改${functionName}
     *
     * @param po ${functionName}
     * @return 结果
     */
    boolean update(${ClassName}PO po);

    /**
     * 批量删除${functionName}
     *
     * @param ${pkColumn.javaField}s 需要删除的${functionName}主键集合
     * @return 结果
     */
    boolean deleteBy${pkColumn.capJavaField}s(${pkColumn.javaType}[] ${pkColumn.javaField}s);

}

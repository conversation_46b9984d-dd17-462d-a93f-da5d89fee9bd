-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(), 0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(@parentId, '${functionName}', '${parentMenuId}', '1', '${businessName}', '${moduleName}/${businessName}/index', 1, 0, 'C', '0', '0', '${permissionPrefix}:list', '#', 1, sysdate(), null, null, '${functionName}菜单', '${applicationCode}');


-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '${functionName}查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:list',        '#', 1, sysdate(), null, null, '', '${applicationCode}');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '${functionName}新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:add',          '#', 1, sysdate(), null, null, '', '${applicationCode}');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '${functionName}修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:edit',         '#', 1, sysdate(), null, null, '', '${applicationCode}');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '${functionName}删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:remove',       '#', 1, sysdate(), null, null, '', '${applicationCode}');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '${functionName}导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:export',       '#', 1, sysdate(), null, null, '', '${applicationCode}');
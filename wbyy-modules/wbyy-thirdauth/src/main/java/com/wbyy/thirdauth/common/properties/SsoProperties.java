package com.wbyy.thirdauth.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/18 9:37
 */
@Configuration
@ConfigurationProperties(prefix = "sso")
@Data
public class SsoProperties {
    private String pmsUrl;

    private String pmsSsoUrl;

    private String loginUrl;
    private String serviceUrl;
    private String appKey;
    private String authUrl;
    private Map<String,String> toUrl;
}

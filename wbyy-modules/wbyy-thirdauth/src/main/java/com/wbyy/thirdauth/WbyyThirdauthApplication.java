package com.wbyy.thirdauth;

import com.wbyy.common.core.constant.SecurityConstants;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.wbyy.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * thirdauth
 * 
 * <AUTHOR>
 */
@Slf4j
@EnableRyFeignClients
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class WbyyThirdauthApplication
{
    public static void main(String[] args)
    {
        MDC.put(SecurityConstants.TRACE_ID, "thirdauth");
        SpringApplication.run(WbyyThirdauthApplication.class, args);
        log.info("(♥◠‿◠)ﾉﾞ  Thirdauth模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}

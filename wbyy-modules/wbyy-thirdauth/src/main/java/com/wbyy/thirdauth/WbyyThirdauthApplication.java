package com.wbyy.thirdauth;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.wbyy.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * thirdauth
 * 
 * <AUTHOR>
 */
@Slf4j
@EnableRyFeignClients
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class WbyyThirdauthApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(WbyyThirdauthApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  Thirdauth模块启动成功   ლ(´ڡ`ლ)ﾞ  \n");
        log.info("(♥◠‿◠)ﾉﾞ  Thirdauth模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}

package com.wbyy.thirdauth.modules.auth.service.impl;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.redis.constant.RedisConst;
import com.wbyy.common.redis.service.RedisService;
import com.wbyy.system.api.UserApi;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.thirdauth.common.properties.SsoProperties;
import com.wbyy.thirdauth.modules.auth.service.ISsoService;
import com.wbyy.weaver.api.AuthApi;
import com.wbyy.weaver.api.model.vo.UserInfoVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/18 9:19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SsoServiceImpl implements ISsoService {

    private final AuthApi authApi;
    private final UserApi userApi;
    private final RedisService redisService;
    private final SsoProperties ssoProperties;

    @Override
    public String getToUrl(String eteamsToken,String appCode) {
        String ssoUrl = Optional.ofNullable(ssoProperties.getToUrl())
                .orElseThrow(() -> new ServiceException("未维护sso页面地址"))
                .get(appCode);
        if (StrUtil.isBlank(ssoUrl)){
            throw new ServiceException("未获取到sso页面地址");
        }
        R<UserInfoVo> userInfoR = authApi.getUserInfo(eteamsToken, SecurityConstants.INNER);
        if (R.FAIL==userInfoR.getCode()) throw new ServiceException("获取用户信息失败");
        String jobNum = userInfoR.getData().getJobNum();
        if (StrUtil.isBlank(jobNum))throw new ServiceException("工号为空");
        R<ApiUser> userR = userApi.getByWorkNumber(jobNum, SecurityConstants.INNER);
        if (R.isError(userR)) throw new ServiceException("根据工号获取用户信息失败");
        String code = UUID.fastUUID().toString();
        String cacheKey = String.format(RedisConst.SSO_INFO_CODE,code);
        redisService.setCacheObject(cacheKey,jobNum,5L, TimeUnit.SECONDS);
        return UriComponentsBuilder.fromHttpUrl(ssoUrl)
                .queryParam("code", code)
                .build()
                .toUriString();
    }

    @Override
    public String getSsoUrl(String appCode) {
        String service = UriComponentsBuilder.fromHttpUrl(ssoProperties.getServiceUrl())
                .queryParam("app_key", ssoProperties.getAppKey())
                .queryParam("redirect_uri",ssoProperties.getAuthUrl()+"?appCode="+appCode)
                .build()
                .toUriString();
        return UriComponentsBuilder.fromHttpUrl(ssoProperties.getLoginUrl())
                .queryParam("service",URLEncodeUtil.encodeAll(service))
                .build()
                .toUriString();
    }
}

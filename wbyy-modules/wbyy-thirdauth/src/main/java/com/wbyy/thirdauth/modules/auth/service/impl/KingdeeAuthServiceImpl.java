package com.wbyy.thirdauth.modules.auth.service.impl;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.weaver.api.AuthApi;
import com.wbyy.weaver.api.model.vo.UserInfoVo;
import com.wbyy.thirdauth.common.properties.KingdeeProperties;
import com.wbyy.thirdauth.modules.auth.service.IKingdeeAuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;

/**
 * @author: 王金都
 * @date: 2025/5/28 14:42
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KingdeeAuthServiceImpl implements IKingdeeAuthService {

    private final AuthApi authApi;
    private final KingdeeProperties kingdeeProperties;

    @Override
    public String getAuthUrl(String eteamsToken) {
        R<UserInfoVo> userInfoR = authApi.getUserInfo(eteamsToken, SecurityConstants.INNER);
        if (R.FAIL==userInfoR.getCode()) throw new ServiceException("获取用户信息失败");
        // 用户名
        String userName = userInfoR.getData().getUserName();
        // 时间戳，当前时间（秒）
        long currentTime=System.currentTimeMillis()/1000;
        String timestamp=Long.toString(currentTime);
        String dbid = kingdeeProperties.getDbid();
        String appid = kingdeeProperties.getAppid();
        String[] strArray ={dbid, userName, appid, kingdeeProperties.getAppSecret(),timestamp,"1"};
        //签名字符串数组需要排序，后生成签名
        Arrays.sort(strArray);
        String combStr = String.join("", strArray);
        byte[] strByte = combStr.getBytes(StandardCharsets.UTF_8);
        byte[] strSign = DigestUtils.sha256(strByte);
        String sign = bytesToHexString(strSign);
        String urlPara = String.format("{\"dbid\":\"%s\",\"username\":\"%s\",\"appid\":\"%s\",\"signeddata\":\"%s\",\"timestamp\":\"%s\",\"lcid\":\"%s\",\"origintype\":\"SimPas\",\"entryrole\":\"\",\"formid\":\"\",\"formtype\":\"\",\"otherargs\":\"|{'permitcount':'1'}\",\"pkid\":\"\",\"loginAgain\":\"\"}",
                dbid,userName,appid,sign, timestamp, "2052");
        urlPara =  Base64.getEncoder().encodeToString(urlPara.getBytes(StandardCharsets.UTF_8));
        return UriComponentsBuilder.fromHttpUrl(kingdeeProperties.getBaseUrl())
                .queryParam("udencoding", "utf-8")
                .queryParam("ud", urlPara)
                .build()
                .toUriString();
    }

    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder();
        if (src == null || src.length <= 0) {
            return null;
        }
        for (byte b : src) {
            int v = b & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

}

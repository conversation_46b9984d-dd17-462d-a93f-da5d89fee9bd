package com.wbyy.thirdauth.modules.auth.service.impl;

import com.wbyy.common.core.constant.Constants;
import com.wbyy.common.core.constant.HttpMagConstants;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.enums.UserStatus;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.DateUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.core.utils.ip.IpUtils;
import com.wbyy.common.qyweixin.config.WxCpConfiguration;
import com.wbyy.common.security.service.TokenService;
import com.wbyy.system.api.SystemLogApi;
import com.wbyy.system.api.UserApi;
import com.wbyy.system.api.domain.ApiLogininfor;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.api.model.LoginUser;
import com.wbyy.thirdauth.modules.auth.service.IQwAuthService;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpOAuth2Service;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/19  10:23
 * @description 企微登录服务
 */
@Service
@RequiredArgsConstructor
public class QwAuthServiceImpl implements IQwAuthService {
    private final UserApi userApi;

    private final SystemLogApi systemLogApi;

    private final TokenService tokenService;

    /**
     * 企微登录
     *
     * @param code            企微code
     * @param applicationCode 应用
     * @return token信息
     */
    @Override
    public Map<String, Object> qwLogin(String code, String applicationCode) {
        if (StringUtils.isBlank(code)) {
            throw new ServiceException(HttpMagConstants.PARAMS_NOT_NULL);
        }
        WxCpService cpService = WxCpConfiguration.getCpService(applicationCode);
        if (null == cpService) {
            throw new ServiceException("应用缺少企微配置:" + applicationCode);
        }

        WxCpOAuth2Service oauth2Service = cpService.getOauth2Service();
        try {
            WxCpOauth2UserInfo userInfo = oauth2Service.getUserInfo(code);
            LoginUser loginUser = this.getInfoByQw(userInfo.getUserId());
            return tokenService.createToken(loginUser);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据企微id查找用户信息，并校验用户状态
     */
    private LoginUser getInfoByQw(String qwUserId) {
        // 查询用户信息
        R<LoginUser> userResult = userApi.getUserInfoByQw(qwUserId, SecurityConstants.INNER);

        if (R.FAIL == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }

        LoginUser userInfo = userResult.getData();
        ApiUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            this.recordLogininfor(user.getUserName(), Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + user.getUserName() + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            this.recordLogininfor(user.getUserName(), Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
            throw new ServiceException("对不起，您的账号：" + user.getUserName() + " 已停用");
        }
        this.recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, "企微登录成功");
        recordLoginInfo(user.getUserId());
        return userInfo;
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        ApiUser sysUser = new ApiUser();
        sysUser.setUserId(userId);
        // 更新用户登录IP
        sysUser.setLoginIp(IpUtils.getIpAddr());
        // 更新用户登录时间
        sysUser.setLoginDate(DateUtils.getNowDate());
        userApi.recordUserLogin(sysUser, SecurityConstants.INNER);
    }

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status   状态
     * @param message  消息内容
     * @return
     */
    public void recordLogininfor(String username, String status, String message) {
        ApiLogininfor logininfor = new ApiLogininfor();
        logininfor.setUserName(username);
        logininfor.setIpaddr(IpUtils.getIpAddr());
        logininfor.setMsg(message);
        // 日志状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            logininfor.setStatus(Constants.LOGIN_SUCCESS_STATUS);
        } else if (Constants.LOGIN_FAIL.equals(status)) {
            logininfor.setStatus(Constants.LOGIN_FAIL_STATUS);
        }
        systemLogApi.saveLogininfor(logininfor, SecurityConstants.INNER);
    }
}

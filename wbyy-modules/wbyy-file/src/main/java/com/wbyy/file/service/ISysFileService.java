package com.wbyy.file.service;

import com.wbyy.system.api.domain.ApiDownloadFile;
import com.wbyy.system.api.domain.ApiSysFile;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.List;

/**
 * 文件上传接口
 *
 * <AUTHOR>
 */
public interface ISysFileService {
    /**
     * 文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    @Deprecated
    String uploadFile(MultipartFile file) throws Exception;

    /**
     * 上传文件
     *
     * @param path            存储路径
     * @param file            文件流
     * @param applicationCode 来源
     * @return 文件信息
     */
    default ApiSysFile uploadFile(String path, MultipartFile file, String applicationCode) {
        return null;
    }

    /**
     * 批量上传文件
     *
     * @param path            存储路径
     * @param files           文件流
     * @param applicationCode 来源
     * @return 文件信息
     */
    default List<ApiSysFile> uploadBatchFile(String path, List<MultipartFile> files, String applicationCode) {
        return null;
    }

    /**
     * 获取文件可访问路径
     *
     * @param fileId 文件id
     * @return 可访问路径
     */
    default String getFilePath(Long fileId) {
        return "";
    }

    /**
     * 下载文件
     *
     * @param fileId   文件id
     * @param response 返回
     */
    default ApiDownloadFile downloadFile(Long fileId, HttpServletResponse response) {
        return null;
    }

    /**
     * 多文件下载zip
     *
     * @param fileIds 多文件id
     */
    default ApiDownloadFile downloadZip(List<Long> fileIds, HttpServletResponse response) {
        return null;
    }

    /**
     * 根据文件id，获取文件信息
     *
     * @param fileId 文件id
     * @return 文件信息
     */
    default ApiSysFile getFileById(Long fileId) {
        return null;
    }

    /**
     * 根据文件id，获取文件信息
     *
     * @param fileIds 文件id
     * @return 文件信息
     */
    default List<ApiSysFile> getFilesByIdList(List<Long> fileIds) {
        return Collections.emptyList();
    }
}

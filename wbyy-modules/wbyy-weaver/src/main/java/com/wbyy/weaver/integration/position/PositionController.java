package com.wbyy.weaver.integration.position;

import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.weaver.common.config.WeaverConfigProperties;
import com.wbyy.weaver.common.constant.ConfigConst;
import com.wbyy.weaver.common.helper.OAuth2TokenHelper;
import com.weaver.openapi.pojo.NormalResult;
import com.weaver.openapi.pojo.dept.res.DeptInfo;
import com.weaver.openapi.pojo.position.PositionList;
import com.weaver.openapi.service.PositionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/5/20 10:04
 */
@RequestMapping("/position")
@RestController
public class PositionController {
    @Autowired
    private OAuth2TokenHelper oAuth2TokenHelper;
    @Autowired
    private WeaverConfigProperties weaverConfigProperties;

    @GetMapping("/list-v2")
    @InnerAuth
    public R<List<DeptInfo>> listPositionV2(){
        String accessToken = oAuth2TokenHelper.getAccessToken();
        PositionList positionList = PositionService.listPositionV2(accessToken, null, weaverConfigProperties.getBaseUrl(), null);
        NormalResult message = positionList.getMessage();
        if (null!=message&& !ConfigConst.SUCCESS_CODE.equals(message.getErrcode())){
            throw new ServiceException("岗位获取失败，"+message.getErrmsg());
        }
        return R.ok(positionList.getPosition());
    }
}

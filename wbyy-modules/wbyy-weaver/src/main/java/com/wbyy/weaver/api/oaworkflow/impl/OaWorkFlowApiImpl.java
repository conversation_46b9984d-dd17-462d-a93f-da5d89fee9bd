package com.wbyy.weaver.api.oaworkflow.impl;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.weaver.api.OaWorkFlowApi;
import com.wbyy.weaver.api.model.vo.OAProjectInitiationVO;
import com.wbyy.weaver.api.oaworkflow.service.IOaWorkFlowService;
import com.wbyy.weaver.common.config.ConfigProperties;
import com.wbyy.weaver.common.config.WeaverConfigProperties;
import com.wbyy.weaver.common.constant.ConfigConst;
import com.wbyy.weaver.common.helper.OAuth2TokenHelper;
import com.weaver.openapi.pojo.NormalResult;
import com.weaver.openapi.pojo.flow.params.FlowVo;
import com.weaver.openapi.pojo.flow.res.WorkFlowRequestInfoResultVo;
import com.weaver.openapi.pojo.flow.res.vo.WorkFlowRequestInfo;
import com.weaver.openapi.service.FlowService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 请求OA 流程相关的接口
 *
 * <AUTHOR>
 */
@RequestMapping("/oa/work/flow")
@RestController
@RequiredArgsConstructor
public class OaWorkFlowApiImpl implements OaWorkFlowApi {

    private final IOaWorkFlowService oaWorkFlowService;
    private final OAuth2TokenHelper oAuth2TokenHelper;
    private final WeaverConfigProperties weaverConfigProperties;
    private final ConfigProperties configProperties;

    @Operation(summary = "根据 oaWorkFlowId 查询项目立项表单信息")
    @GetMapping("/get-one")
    public R<OAProjectInitiationVO> getByOaFlowId(Long oaWorkFlowId){
        return R.ok(oaWorkFlowService.getByOaFlowId(oaWorkFlowId));
    }

    @Override
    @PostMapping("/get-workflow-request-v1")
    public R<WorkFlowRequestInfo> getWorkflowRequestV1(@RequestBody FlowVo vo,
                                                       @RequestHeader(SecurityConstants.FROM_SOURCE) String source){
        vo.setAccessToken(oAuth2TokenHelper.getAccessToken());
        vo.setUserid(configProperties.getSysAdminId());
        WorkFlowRequestInfoResultVo result = FlowService.getWorkflowRequestV1(vo, weaverConfigProperties.getBaseUrl(), null);
        NormalResult message = result.getMessage();
        if (null!=message&& !ConfigConst.SUCCESS_CODE.equals(message.getErrcode())){
            throw new ServiceException("表单数据获取失败，"+message.getErrmsg());
        }
        return R.ok(result.getData());
    }
}

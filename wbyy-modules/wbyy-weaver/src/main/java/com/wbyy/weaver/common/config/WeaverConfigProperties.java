package com.wbyy.weaver.common.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "weaver")
@AllArgsConstructor
@NoArgsConstructor
public class WeaverConfigProperties {
    private String baseUrl;
    private String corpId;
    private String appKey;
    private String appSecret;
    private Http http = new Http();

    /**
     * HTTP配置类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Http{
        private int connectTimeout;
        private int readTimeout;
        private int writeTimeout;
        private boolean openRetry;
        private int maxRetries;
    }
}

package com.wbyy.weaver.api.oaworkflow.service;

import com.wbyy.weaver.api.model.vo.OAProjectInitiationVO;

/**
 * OA工作流相关
 *
 * <AUTHOR>
 * @date 2025/6/26 19:38
 */
public interface IOaWorkFlowService {

    /**
     * 根据 oaWorkFlowId 查询项目立项表单信息
     *
     * @param oaWorkFlowId
     * @return OAProjectInitiationVO
     * <AUTHOR>
     * @date 2025/6/26 19:37
     */
    OAProjectInitiationVO getByOaFlowId(Long oaWorkFlowId);
}

package com.wbyy.weaver.integration.file;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.system.api.FileApi;
import com.wbyy.system.api.domain.ApiSysFile;
import com.wbyy.weaver.common.config.ConfigProperties;
import com.wbyy.weaver.common.config.WeaverConfigProperties;
import com.wbyy.weaver.common.helper.OAuth2TokenHelper;
import com.wbyy.weaver.common.model.CustomMultipartFile;
import com.wbyy.weaver.api.model.UploadFileParam;
import com.wbyy.weaver.integration.file.vo.FilePageInfo;
import com.wbyy.weaver.integration.file.vo.FilePageResultVo;
import com.weaver.openapi.pojo.file.params.FileVo;
import com.weaver.openapi.pojo.file.res.vo.FileData;
import com.weaver.openapi.service.FileService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/6/3 10:29
 */
@RestController
@RequestMapping("/file")
@Slf4j
@RequiredArgsConstructor
public class WeaverFileController {

    private final OAuth2TokenHelper oAuth2TokenHelper;
    private final WeaverConfigProperties weaverConfigProperties;
    private final ConfigProperties configProperties;
    private final FileApi fileApi;

    @GetMapping("/download-file-v2")
    @InnerAuth
    public R<FileData> downloadFileV2(@RequestParam("fileId") String fileId) throws IOException {
        FileVo fileVo = new FileVo();
        fileVo.setAccessToken(oAuth2TokenHelper.getAccessToken());
        fileVo.setFileId(fileId);
        fileVo.setUserid(configProperties.getSysAdminId());
        FileData fileData = FileService.downloadFileDataV2(fileVo, weaverConfigProperties.getBaseUrl(), null);
        return R.ok(fileData);
    }

    @PostMapping("/upload-file-2-system-file")
    @InnerAuth
    public R<ApiSysFile> uploadFile2SystemFile(@Valid @RequestBody UploadFileParam param) throws IOException {
        FileVo fileVo = new FileVo();
        fileVo.setAccessToken(oAuth2TokenHelper.getAccessToken());
        fileVo.setFileId(param.getFileId());
        fileVo.setFileIdList(List.of(Long.valueOf(param.getFileId())));
        fileVo.setUserid(configProperties.getSysAdminId());
        String baseUrl = weaverConfigProperties.getBaseUrl();
        FilePageResultVo fileResultVo = FileService.queryFileInfo(fileVo, baseUrl, new HashMap<>());
        List<FilePageInfo.FileInfo> result = fileResultVo.getData().getResult();
        if (result.isEmpty()) {
            return R.fail(String.format("文件%s不存在", param.getFileId()));
        }
        String fileName = result.get(0).getFileName();
        FileData fileData = FileService.downloadFileDataV2(fileVo, baseUrl, new HashMap<>());
        CustomMultipartFile file = CustomMultipartFile.builder()
                .content(fileData.getBytes())
                .name(fileName)
                .originalFilename(fileName)
                .contentType(fileData.getContentType())
                .build();
        R<ApiSysFile> apiSysFileR = fileApi.uploadFile(param.getSavePath(), file, SecurityConstants.INNER);
        return R.ok(apiSysFileR.getData());
    }
}

package com.wbyy.weaver.api.attend.impl;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.weaver.api.AttendApi;
import com.wbyy.weaver.common.config.WeaverConfigProperties;
import com.wbyy.weaver.common.constant.ConfigConst;
import com.wbyy.weaver.common.helper.OAuth2TokenHelper;
import com.weaver.openapi.pojo.NormalResult;
import com.weaver.openapi.pojo.attend.params.AttendVo;
import com.weaver.openapi.pojo.attend.res.AttendResult;
import com.weaver.openapi.pojo.attend.res.vo.AttendResultVo;
import com.weaver.openapi.service.AttendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/6/13 14:06
 */
@RestController
@RequestMapping("/attend")
public class AttendApiImpl implements AttendApi {


    @Autowired
    private OAuth2TokenHelper oAuth2TokenHelper;
    @Autowired
    private WeaverConfigProperties weaverConfigProperties;

    @PostMapping("get-leave-v2")
    @InnerAuth
    @Override
    public R<List<AttendResultVo>> getLeaveV2(@RequestBody AttendVo vo,
                                              @RequestHeader(SecurityConstants.FROM_SOURCE) String source){
        vo.setAccessToken(oAuth2TokenHelper.getAccessToken());
        AttendResult leaveV2 = AttendService.getLeaveV2(vo, weaverConfigProperties.getBaseUrl(), null);
        NormalResult message = leaveV2.getMessage();
        if (null!=message&& !ConfigConst.SUCCESS_CODE.equals(message.getErrcode())){
            throw new ServiceException("请假数据获取失败，"+message.getErrmsg());
        }
        if (null==leaveV2.getData()){
            return R.ok();
        }
        return R.ok(leaveV2.getData());
    }

    @PostMapping("/get-sign-in-data")
    @InnerAuth
    @Override
    public R<List<AttendResultVo>> getSignInData(@RequestBody AttendVo vo,
                                                 @RequestHeader(SecurityConstants.FROM_SOURCE) String source){
        vo.setAccessToken(oAuth2TokenHelper.getAccessToken());
        AttendResult result = AttendService.getSignInData(vo, weaverConfigProperties.getBaseUrl(), null);
        NormalResult message = result.getMessage();
        if (null!=message&& !ConfigConst.SUCCESS_CODE.equals(message.getErrcode())){
            throw new ServiceException("签到数据获取失败，"+message.getErrmsg());
        }
        return R.ok(result.getData());
    }

    @PostMapping("/get-sign-out-data")
    @InnerAuth
    @Override
    public R<List<AttendResultVo>> getSignOutData(@RequestBody AttendVo vo,
                                                  @RequestHeader(SecurityConstants.FROM_SOURCE) String source){
        vo.setAccessToken(oAuth2TokenHelper.getAccessToken());
        AttendResult result = AttendService.getSignOutData(vo, weaverConfigProperties.getBaseUrl(), null);
        NormalResult message = result.getMessage();
        if (null!=message&& !ConfigConst.SUCCESS_CODE.equals(message.getErrcode())){
            throw new ServiceException("签退数据获取失败,"+message.getErrmsg());
        }
        return R.ok(result.getData());
    }
}

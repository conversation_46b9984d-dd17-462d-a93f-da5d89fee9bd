package com.wbyy.weaver.provider.project;

import com.alibaba.fastjson2.JSONArray;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.pms.api.BusinessTypeApi;
import com.wbyy.pms.api.domain.ApiBusinessType;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/28 17:47
 */
@RestController
@RequestMapping("/provider/business/type")
@RequiredArgsConstructor
public class BusinessTypeController {

    private final BusinessTypeApi businessTypeApi;

    @GetMapping("/list")
    public List<ApiBusinessType> list(){
        return businessTypeApi.simpleTree(SecurityConstants.INNER).getData();
    }
}

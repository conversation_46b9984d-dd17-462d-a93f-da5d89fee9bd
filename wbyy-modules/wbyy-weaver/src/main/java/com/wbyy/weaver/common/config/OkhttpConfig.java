package com.wbyy.weaver.common.config;

import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Configuration
public class OkhttpConfig {
    @Autowired
    private WeaverConfigProperties weaverConfigProperties;
    @Bean
    public OkHttpClient okHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(weaverConfigProperties.getHttp().getConnectTimeout(), TimeUnit.SECONDS)
                .readTimeout(weaverConfigProperties.getHttp().getReadTimeout(), TimeUnit.SECONDS)
                .writeTimeout(weaverConfigProperties.getHttp().getWriteTimeout(), TimeUnit.SECONDS);
        if (weaverConfigProperties.getHttp().isOpenRetry()){
            builder.addInterceptor(new RetryInterceptor(weaverConfigProperties.getHttp().getMaxRetries()));
        }
        return builder.build();
    }

    // 重试拦截器
    private static class RetryInterceptor implements Interceptor {
        private final int maxRetries;

        RetryInterceptor(int maxRetries) {
            this.maxRetries = maxRetries;
        }

        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            Response response = null;
            IOException exception = null;

            for (int i = 0; i <= maxRetries; i++) {
                try {
                    response = chain.proceed(request);
                    if (response.isSuccessful()) return response;
                } catch (IOException e) {
                    exception = e;
                }
            }

            if (exception != null) throw exception;
            if (response != null) {
                return response;
            }throw new NullPointerException("response entity is null");
        }
    }
}

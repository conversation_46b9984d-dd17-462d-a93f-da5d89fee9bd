package com.wbyy.weaver.integration.file.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/6 15:11
 */
@Data
public class FilePageInfo {
    private int pageNo;
    private int pageSize;
    private List<FileInfo> result;
    private int totalCount;
    private boolean hasNext;
    private int nextPage;
    private boolean hasPre;
    private int prePage;
    private int totalPages;
    private int first;

    @Data
    public static class FileInfo {
        private String fileId;
        private String fileName;
        private String type;
        private String size;
        private long uploadTime;
        private String uploadUserName;
        private String uploadEmployeeId;
        private String tenantKey;
        private String docId;
        private String extName;
        private String module;
        private long refId;
    }
}
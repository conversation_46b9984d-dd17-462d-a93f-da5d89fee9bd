package com.wbyy.weaver;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.wbyy.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * Weaver
 * 
 * <AUTHOR>
 */
@Slf4j
@EnableRyFeignClients
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class WbyyWeaverApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(WbyyWeaverApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  Weaver模块启动成功   ლ(´ڡ`ლ)ﾞ  \n");
        log.info("(♥◠‿◠)ﾉﾞ  Weaver模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}

package com.wbyy.weaver.api.form.impl;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.weaver.api.WeaverFormApi;
import com.wbyy.weaver.common.config.ConfigProperties;
import com.wbyy.weaver.common.config.WeaverConfigProperties;
import com.wbyy.weaver.common.constant.ConfigConst;
import com.wbyy.weaver.common.helper.OAuth2TokenHelper;
import com.weaver.openapi.pojo.NormalResult;
import com.weaver.openapi.pojo.form.params.FormVo;
import com.weaver.openapi.pojo.form.res.DataReportResultV3;
import com.weaver.openapi.pojo.form.res.vo.FormDataV3;
import com.weaver.openapi.service.FormService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.wbyy.weaver.common.constant.ConfigConst.SYSADMIN_ID;

/**
 * <AUTHOR>
 * @date 2025/8/1 10:34
 */
@RestController
@RequestMapping("/form")
public class WeaverFormApiImpl implements WeaverFormApi {

    @Autowired
    private OAuth2TokenHelper oAuth2TokenHelper;
    @Autowired
    private WeaverConfigProperties weaverConfigProperties;
    @Autowired
    private ConfigProperties configProperties;

    @PostMapping("/query-data-report-by-id-v3")
    @InnerAuth
    public R<FormDataV3> queryDataReportByIdV3(@RequestBody FormVo vo,
                                               @RequestHeader(SecurityConstants.FROM_SOURCE) String source){
        vo.setAccessToken(oAuth2TokenHelper.getAccessToken());
        vo.setUserid(configProperties.getSysAdminId());
        DataReportResultV3 result = FormService.queryDataReportByIdV3(vo, weaverConfigProperties.getBaseUrl(), null);
        NormalResult message = result.getMessage();
        if (null!=message&& !ConfigConst.SUCCESS_CODE.equals(message.getErrcode())){
            throw new ServiceException("表单数据获取失败，"+message.getErrmsg());
        }
        return R.ok(result.getFormData());
    }
}

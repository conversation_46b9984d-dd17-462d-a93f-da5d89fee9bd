package com.wbyy.weaver.common.helper;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.wbyy.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class HttpSendHelper {
    @Autowired
    private OkHttpClient okHttpClient;
   /**
    * @Description: post请求
    * @param url
    * @param bodyString
    * @return: String
    * @author: 王金都
    * @Date: 2025/4/27 11:50
    */
    public <T> T simplePost(String url, String bodyString,Class<T> responseClass) {
        if (StrUtil.isBlank(url)) throw new ServiceException("URL不得为空");
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(bodyString, MediaType.parse("application/json; charset=utf-8")))
                .build();
        return JSON.parseObject(simpleReturn(request),responseClass);
    }

    /**
     * @Description: get请求
     * @param url
     * @param param
     * @param responseClass
     * @return: T
     * @author: 王金都
     * @Date: 2025/4/27 14:26
     */
    public <T> T simpleGet(String url, Map<String,Object> param,Class<T> responseClass){
        HttpUrl httpUrl = HttpUrl.parse(url);
        if (null==httpUrl) throw new ServiceException("URL为空");
        HttpUrl.Builder urlBuilder = httpUrl.newBuilder();
        if (CollectionUtil.isNotEmpty(param)){
            for (Map.Entry<String, Object> entry : param.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                // 忽略 null 或空值
                if (value != null && !value.toString().isEmpty()) {
                    urlBuilder.addQueryParameter(key, value.toString());
                }
            }
        }
        Request request = new Request.Builder()
                .url(urlBuilder.build())
                .get()
                .build();
        return JSON.parseObject(simpleReturn(request),responseClass);
    }

    private String simpleReturn(Request request){
        try (Response response = okHttpClient.newCall(request).execute()) {
            String body = response.body() != null ? response.body().string() : StrUtil.EMPTY_JSON;
            if (!response.isSuccessful()){
                throw new ServiceException(String.format("""
                    请求失败，响应码【%s】
                    响应体【%s】
                    """,response.code(),body));
            }
            return body;
        } catch (Exception e) {
            log.error("HTTP请求失败",e);
            throw new ServiceException("请求失败");
        }
    }
}

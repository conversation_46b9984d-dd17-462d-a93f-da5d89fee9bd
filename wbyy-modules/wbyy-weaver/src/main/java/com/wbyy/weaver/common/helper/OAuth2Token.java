package com.wbyy.weaver.common.helper;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @author: 王金都
 * @date: 2025/4/27 14:51
 */
@Data
public class OAuth2Token implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private String accessToken;
    private String refreshToken;
    private Long expiresIn;
    private Long createTime;

    // 检查token是否过期，提前5分钟认为过期，防止刚好在临界点
    public boolean isValid() {
        return System.currentTimeMillis() < (createTime + (expiresIn * 1000) - 180000);
    }
}

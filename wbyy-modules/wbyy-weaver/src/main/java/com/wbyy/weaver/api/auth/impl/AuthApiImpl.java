package com.wbyy.weaver.api.auth.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSONObject;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.weaver.api.AuthApi;
import com.wbyy.weaver.common.config.WeaverConfigProperties;
import com.weaver.openapi.util.HostUtil;
import com.wbyy.weaver.api.model.vo.UserInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: 王金都
 * @date: 2025/5/28 14:49
 */
@RestController
@RequestMapping("/auth")
@Slf4j
public class AuthApiImpl implements AuthApi {

    @Autowired
    private WeaverConfigProperties weaverConfigProperties;

    @GetMapping("/get-user-info")
    @InnerAuth
    public R<UserInfoVo> getUserInfo(@RequestParam("eteamsToken")String eteamsToken, @RequestHeader(SecurityConstants.FROM_SOURCE) String source){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("eteams_token",eteamsToken);
        String host = HostUtil.beforeRequestCheckHeaders(weaverConfigProperties.getBaseUrl(), null, "application/x-www-form-urlencoded");
        try(HttpResponse execute = HttpRequest.post(host + "/oauth2/getUserInfo").form(paramMap).headerMap(null, true).execute()) {
            String resJson = execute.body();
            return R.ok(JSONObject.parseObject(resJson,UserInfoVo.class));
        }catch (Exception e){
            log.error("获取泛微用户失败",e);
            throw new ServiceException("获取泛微用户信息失败");
        }
    }
}

package com.wbyy.weaver.integration.department.controller;

import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.weaver.common.config.WeaverConfigProperties;
import com.wbyy.weaver.common.constant.ConfigConst;
import com.wbyy.weaver.common.helper.OAuth2TokenHelper;
import com.weaver.openapi.pojo.NormalResult;
import com.weaver.openapi.pojo.dept.params.DeptListParam;
import com.weaver.openapi.pojo.dept.res.DeptInfo;
import com.weaver.openapi.pojo.dept.res.DeptList;
import com.weaver.openapi.service.DeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/4/27 15:23
 */
@RestController
@RequestMapping("department")
public class DepartmentController {
    @Autowired
    private OAuth2TokenHelper oAuth2TokenHelper;
    @Autowired
    private WeaverConfigProperties weaverConfigProperties;

    @GetMapping("list-v2")
    @InnerAuth
    public R<List<DeptInfo>> v2List(@RequestParam(name = "status",required = false) String status,
                                    @RequestParam(name = "isDelete",required = false) String isDelete,
                                    @RequestParam(name = "deptId",required = false) Long deptId){
        String accessToken = oAuth2TokenHelper.getAccessToken();
        DeptListParam deptListParam = new DeptListParam(accessToken, null, null, null);
        DeptList deptList = DeptService.listDeptV2(deptListParam, weaverConfigProperties.getBaseUrl(), null);
        NormalResult message = deptList.getMessage();
        if (null!=message&&!ConfigConst.SUCCESS_CODE.equals(message.getErrcode())){
            throw new ServiceException("获取部门列表失败，"+ message.getErrmsg());
        }
        return R.ok(deptList.getDepartment());
    }
}

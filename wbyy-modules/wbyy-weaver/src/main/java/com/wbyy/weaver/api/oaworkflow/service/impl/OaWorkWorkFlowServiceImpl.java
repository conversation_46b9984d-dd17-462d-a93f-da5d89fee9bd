package com.wbyy.weaver.api.oaworkflow.service.impl;

import com.wbyy.weaver.api.model.vo.OAProjectInitiationVO;
import com.wbyy.weaver.api.oaworkflow.service.IOaWorkFlowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OaWorkWorkFlowServiceImpl implements IOaWorkFlowService {

    @Override
    public OAProjectInitiationVO getByOaFlowId(Long oaWorkFlowId) {
        // TODO: 2023/9/5
        //   暂不实现
        return null;
    }
}

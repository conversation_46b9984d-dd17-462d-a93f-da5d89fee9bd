package com.wbyy.weaver.common.helper;

import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.weaver.common.config.WeaverConfigProperties;
import com.wbyy.weaver.common.constant.ConfigConst;
import com.weaver.openapi.pojo.auth.params.AccessTokenParam;
import com.weaver.openapi.pojo.auth.params.CodeParam;
import com.weaver.openapi.pojo.auth.res.AccessToken;
import com.weaver.openapi.pojo.auth.res.Code;
import com.weaver.openapi.service.AuthService;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @author: 王金都
 * @date: 2025/4/27 14:50
 */
@Component
public class OAuth2TokenHelper {
    private static final String TOKEN_LOCK_KEY = "weaver:oauth2:token:lock";
    private static final String TOKEN_CACHE_KEY = "weaver:oauth2:token";
    private static final long LOCK_WAIT_TIME = 10;
    private static final long LOCK_LEASE_TIME = 30;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private WeaverConfigProperties weaverConfigProperties;

    public String getAccessToken() {
        // 先从缓存获取token
        OAuth2Token token = getTokenFromCache();
        if (token != null && token.isValid()) {
            return token.getAccessToken();
        }

        // 获取分布式锁
        RLock lock = redisson.getLock(TOKEN_LOCK_KEY);
        try {
            // 尝试获取锁
            if (lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                try {
                    // 双重检查，防止其他线程已经刷新了token
                    token = getTokenFromCache();
                    if (token != null && token.isValid()) {
                        return token.getAccessToken();
                    }

                    // token过期或不存在，需要刷新
                    if (token != null && token.getRefreshToken() != null && token.isValid()) {
                        try {
                            // 尝试使用refreshToken刷新
                            token = refreshToken(token.getRefreshToken());
                        } catch (Exception e) {
                            // 刷新失败，重新获取新token
                            token = getNewToken();
                        }
                    } else {
                        // 重新获取新token
                        token = getNewToken();
                    }

                    // 保存到缓存
                    saveTokenToCache(token);
                    return token.getAccessToken();
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ServiceException("获取access token失败", e);
        }
        throw new ServiceException("获取access token超时");
    }

    private OAuth2Token getTokenFromCache() {
        RBucket<OAuth2Token> bucket = redisson.getBucket(TOKEN_CACHE_KEY);
        return bucket.get();
    }

    private void saveTokenToCache(OAuth2Token token) {
        RBucket<OAuth2Token> bucket = redisson.getBucket(TOKEN_CACHE_KEY);
        // 设置过期时间比token过期时间稍长一些
        bucket.set(token, token.getExpiresIn() + 60, TimeUnit.SECONDS);
    }

    private OAuth2Token getNewToken() {
        CodeParam param = new CodeParam(weaverConfigProperties.getCorpId(),"code",null);
        Code code = AuthService.getAuthCode(param, weaverConfigProperties.getBaseUrl(), null);
        AccessTokenParam tokenParam = new AccessTokenParam(weaverConfigProperties.getAppKey(),
                weaverConfigProperties.getAppSecret(), "authorization_code", code.getCode());
        AccessToken token = AuthService.getAuthAccessToken(tokenParam, weaverConfigProperties.getBaseUrl(), null);
        if (!ConfigConst.SUCCESS_CODE.equals(token.getErrcode())) throw new ServiceException("获取accessToken失败");
        OAuth2Token oAuth2Token = new OAuth2Token();
        oAuth2Token.setAccessToken(token.getAccessToken());
        oAuth2Token.setRefreshToken(token.getRefreshToken());
        oAuth2Token.setExpiresIn((long) token.getExpiresIn());
        oAuth2Token.setCreateTime(System.currentTimeMillis());
        return oAuth2Token;
    }



    private OAuth2Token refreshToken(String refreshToken) {
        AccessTokenParam tokenParam = new AccessTokenParam("refresh_token",refreshToken);
        AccessToken refreshAccessToken = AuthService.refreshAccessToken(tokenParam, weaverConfigProperties.getBaseUrl(), null);
        if (!ConfigConst.SUCCESS_CODE.equals(refreshAccessToken.getErrcode())) throw new ServiceException("accessToken续期失败");
        OAuth2Token oAuth2Token = new OAuth2Token();
        oAuth2Token.setAccessToken(refreshAccessToken.getAccessToken());
        oAuth2Token.setRefreshToken(refreshAccessToken.getRefreshToken());
        oAuth2Token.setExpiresIn((long) refreshAccessToken.getExpiresIn());
        oAuth2Token.setCreateTime(System.currentTimeMillis());
        return oAuth2Token;
    }
}

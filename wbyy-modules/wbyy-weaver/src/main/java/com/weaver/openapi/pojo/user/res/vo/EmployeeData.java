package com.weaver.openapi.pojo.user.res.vo;

import java.io.Serializable;

/**
 * @author: 王金都
 * @date: 2025/4/29 11:01
 */
public class EmployeeData implements Serializable {
    private UserParam education;
    private String accumfundaccount;
    private UserParam nation;
    private String residence_place;
    private String modifier;
    private String active_date;
    private String type;
    private String bankid;
    private String id_no;
    private String bank_accountname;
    private String id;
    private String bank_account;
    private String created;
    private UserParam degree;
    private String first_work_date;
    private Integer work_year;
    private String formdata;
    private String login_status;
    private String native_place;
    private String last_update_time;
    private String pinyin;
    private String user_id;
    private String child_status;
    private String birthday;
    private String tenant_key;
    private String family_contact;
    private Integer sec_level;
    private String update_time;
    private String job_num;
    private String department;
    private String logogram;
    private String personnel_status;
    private String im_uid;
    private UserParam household_type;
    private String graduate_school;
    private String marital_status;
    private UserParam politics_status;
    private Integer age;
    private String username;
    private String mobile;
    private String email;
    private String position;

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public UserParam getEducation() {
        return this.education;
    }

    public void setEducation(UserParam education) {
        this.education = education;
    }

    public String getAccumfundaccount() {
        return this.accumfundaccount;
    }

    public void setAccumfundaccount(String accumfundaccount) {
        this.accumfundaccount = accumfundaccount;
    }

    public UserParam getNation() {
        return this.nation;
    }

    public void setNation(UserParam nation) {
        this.nation = nation;
    }

    public String getResidence_place() {
        return this.residence_place;
    }

    public void setResidence_place(String residence_place) {
        this.residence_place = residence_place;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getActive_date() {
        return this.active_date;
    }

    public void setActive_date(String active_date) {
        this.active_date = active_date;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBankid() {
        return this.bankid;
    }

    public void setBankid(String bankid) {
        this.bankid = bankid;
    }

    public String getId_no() {
        return this.id_no;
    }

    public void setId_no(String id_no) {
        this.id_no = id_no;
    }

    public String getBank_accountname() {
        return this.bank_accountname;
    }

    public void setBank_accountname(String bank_accountname) {
        this.bank_accountname = bank_accountname;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBank_account() {
        return this.bank_account;
    }

    public void setBank_account(String bank_account) {
        this.bank_account = bank_account;
    }

    public String getCreated() {
        return this.created;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public UserParam getDegree() {
        return this.degree;
    }

    public void setDegree(UserParam degree) {
        this.degree = degree;
    }

    public String getFirst_work_date() {
        return this.first_work_date;
    }

    public void setFirst_work_date(String first_work_date) {
        this.first_work_date = first_work_date;
    }

    public Integer getWork_year() {
        return this.work_year;
    }

    public void setWork_year(Integer work_year) {
        this.work_year = work_year;
    }

    public String getFormdata() {
        return this.formdata;
    }

    public void setFormdata(String formdata) {
        this.formdata = formdata;
    }

    public String getLogin_status() {
        return this.login_status;
    }

    public void setLogin_status(String login_status) {
        this.login_status = login_status;
    }

    public String getNative_place() {
        return this.native_place;
    }

    public void setNative_place(String native_place) {
        this.native_place = native_place;
    }

    public String getLast_update_time() {
        return this.last_update_time;
    }

    public void setLast_update_time(String last_update_time) {
        this.last_update_time = last_update_time;
    }

    public String getPinyin() {
        return this.pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getUser_id() {
        return this.user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getChild_status() {
        return this.child_status;
    }

    public void setChild_status(String child_status) {
        this.child_status = child_status;
    }

    public String getBirthday() {
        return this.birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getTenant_key() {
        return this.tenant_key;
    }

    public void setTenant_key(String tenant_key) {
        this.tenant_key = tenant_key;
    }

    public String getFamily_contact() {
        return this.family_contact;
    }

    public void setFamily_contact(String family_contact) {
        this.family_contact = family_contact;
    }

    public Integer getSec_level() {
        return this.sec_level;
    }

    public void setSec_level(Integer sec_level) {
        this.sec_level = sec_level;
    }

    public String getUpdate_time() {
        return this.update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public String getJob_num() {
        return this.job_num;
    }

    public void setJob_num(String job_num) {
        this.job_num = job_num;
    }

    public String getDepartment() {
        return this.department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getLogogram() {
        return this.logogram;
    }

    public void setLogogram(String logogram) {
        this.logogram = logogram;
    }

    public String getPersonnel_status() {
        return this.personnel_status;
    }

    public void setPersonnel_status(String personnel_status) {
        this.personnel_status = personnel_status;
    }

    public String getIm_uid() {
        return this.im_uid;
    }

    public void setIm_uid(String im_uid) {
        this.im_uid = im_uid;
    }

    public UserParam getHousehold_type() {
        return this.household_type;
    }

    public void setHousehold_type(UserParam household_type) {
        this.household_type = household_type;
    }

    public String getGraduate_school() {
        return this.graduate_school;
    }

    public void setGraduate_school(String graduate_school) {
        this.graduate_school = graduate_school;
    }

    public String getMarital_status() {
        return this.marital_status;
    }

    public void setMarital_status(String marital_status) {
        this.marital_status = marital_status;
    }

    public UserParam getPolitics_status() {
        return this.politics_status;
    }

    public void setPolitics_status(UserParam politics_status) {
        this.politics_status = politics_status;
    }

    public Integer getAge() {
        return this.age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
}
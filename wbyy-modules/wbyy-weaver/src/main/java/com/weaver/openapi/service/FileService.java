package com.weaver.openapi.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wbyy.weaver.integration.file.vo.FilePageResultVo;
import com.weaver.openapi.pojo.MessageResult;
import com.weaver.openapi.pojo.file.params.DocVo;
import com.weaver.openapi.pojo.file.params.FileVo;
import com.weaver.openapi.pojo.file.res.*;
import com.weaver.openapi.pojo.file.res.vo.FileData;
import com.weaver.openapi.util.HostUtil;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * @author: 王金都
 * @date: 2025/6/3 11:16
 */
public class FileService {
    public static FileResultVo uploadFileV2(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/x-www-form-urlencoded");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("access_token", fileVo.getAccessToken());
        paramMap.put("name", fileVo.getName());
        paramMap.put("file", fileVo.getFile());
        paramMap.put("userid", fileVo.getUserid());
        paramMap.put("createDocument", fileVo.isCreateDocument());
        paramMap.put("folderId", fileVo.getFolderId());
        paramMap.put("module", fileVo.getModule());
        paramMap.put("size", fileVo.getSize());
        paramMap.put("lastModified", fileVo.getLastModified());
        paramMap.put("createDocContent", fileVo.isCreateDocContent());
        paramMap.put("MD5", fileVo.getMD5());
        paramMap.put("refId", fileVo.getRefId());
        String resJson = ((HttpRequest)HttpRequest.post(host + "/api/file/v2/common/upload").form(paramMap).headerMap(headers, true)).execute().body();
        return (FileResultVo) JSONObject.parseObject(resJson, FileResultVo.class);
    }

    public static InputStream downloadFileV2(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/x-www-form-urlencoded");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("access_token", fileVo.getAccessToken());
        paramMap.put("userid", fileVo.getUserid());
        paramMap.put("module", fileVo.getModule());
        InputStream inputStream = ((HttpRequest)HttpRequest.get(host + "/api/file/v2/common/download/" + fileVo.getFileId()).form(paramMap).headerMap(headers, true)).execute().bodyStream();
        return inputStream;
    }

    public static FileData downloadFileDataV2(FileVo fileVo, String host, Map<String, String> headers) throws IOException {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/x-www-form-urlencoded");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("access_token", fileVo.getAccessToken());
        paramMap.put("userid", fileVo.getUserid());
        paramMap.put("module", fileVo.getModule());
        try ( HttpResponse execute = ((HttpRequest) HttpRequest.get(host + "/api/file/v2/common/download/" + fileVo.getFileId()).form(paramMap).headerMap(headers, true)).execute();
              InputStream inputStream = execute.bodyStream();){
            String contentDisposition = execute.header("Content-Disposition");
            return new FileData(inputStream.readAllBytes(),execute.header("Content-Type"),extractFileName(contentDisposition));
        }
    }

    private static String extractFileName(String contentDisposition) {
        if (StrUtil.isBlank(contentDisposition)) return null;
        String[] parts = contentDisposition.split(";");
        for (String part : parts) {
            part = part.trim();
            if (part.startsWith("filename=")) {
                String filename = part.substring("filename=".length()).trim();
                if (filename.startsWith("\"") && filename.endsWith("\"")) {
                    filename = filename.substring(1, filename.length() - 1);
                }
                return filename;
            }
        }
        return null;
    }

    public static DocResultVo createDoc(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("folderId", fileVo.getFolderId());
        paramMap.put("virtualFolderIds", fileVo.getVirtualFolderIds());
        paramMap.put("name", fileVo.getName());
        paramMap.put("content", fileVo.getContent());
        paramMap.put("contentType", fileVo.getContentType());
        paramMap.put("folderType", fileVo.getFolderType());
        paramMap.put("attIds", fileVo.getAttIds());
        paramMap.put("tags", fileVo.getTags());
        paramMap.put("secLevel", fileVo.getSecLevel());
        paramMap.put("validity", fileVo.getValidity());
        paramMap.put("formData", fileVo.getFormData());
        paramMap.put("customFormData", fileVo.getCustomFormData());
        paramMap.put("operateType", fileVo.getOperateType());
        String jsonRes = ((HttpRequest)HttpRequest.post(host + String.format("/api/doc/document/v2/create?access_token=%s&userid=%s", fileVo.getAccessToken(), fileVo.getUserid())).body(JSONObject.toJSONString(paramMap)).headerMap(headers, true)).execute().body();
        return (DocResultVo)JSONObject.parseObject(jsonRes, DocResultVo.class);
    }

    public static DocAttResultVo attList(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/x-www-form-urlencoded");
        String jsonRes = ((HttpRequest)HttpRequest.get(host + String.format("/api/doc/v2/attList/%s?access_token=%s&userid=%s", fileVo.getDocId(), fileVo.getAccessToken(), fileVo.getUserid())).headerMap(headers, true)).execute().body();
        return (DocAttResultVo)JSONObject.parseObject(jsonRes, DocAttResultVo.class);
    }

    public static DocDelResultVo delDoc(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("docIds", fileVo.getDocIds());
        paramMap.put("definitieve", fileVo.getDefinitieve());
        String jsonRes = ((HttpRequest)HttpRequest.post(host + String.format("/api/doc/document/v2/destory?access_token=%s&userid=%s", fileVo.getAccessToken(), fileVo.getUserid())).body(JSONObject.toJSONString(paramMap)).headerMap(headers, true)).execute().body();
        return (DocDelResultVo)JSONObject.parseObject(jsonRes, DocDelResultVo.class);
    }

    public static DocTabInfoResultVo docTabInfo(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/x-www-form-urlencoded");
        String jsonRes = ((HttpRequest)HttpRequest.get(host + String.format("/api/doc/document/v2/tabInfo?access_token=%s", fileVo.getAccessToken())).headerMap(headers, true)).execute().body();
        return (DocTabInfoResultVo)JSONObject.parseObject(jsonRes, DocTabInfoResultVo.class);
    }

    public static DocResultVo docInfo(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/x-www-form-urlencoded");
        String jsonRes = ((HttpRequest)HttpRequest.get(host + String.format("/api/doc/document/v2/info?access_token=%s&id=%s&userid=%s", fileVo.getAccessToken(), fileVo.getDocId(), fileVo.getUserid())).headerMap(headers, true)).execute().body();
        return (DocResultVo)JSONObject.parseObject(jsonRes, DocResultVo.class);
    }

    public static DocResultVo docProperty(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/x-www-form-urlencoded");
        String jsonRes = ((HttpRequest)HttpRequest.get(host + String.format("/api/doc/document/v2/property?access_token=%s&id=%s&userid=%s", fileVo.getAccessToken(), fileVo.getDocId(), fileVo.getUserid())).headerMap(headers, true)).execute().body();
        return (DocResultVo)JSONObject.parseObject(jsonRes, DocResultVo.class);
    }

    public static DocVersionResultVo docVersion(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/x-www-form-urlencoded");
        String jsonRes = ((HttpRequest)HttpRequest.get(host + String.format("/api/doc/document/v2/versions?access_token=%s&id=%s&userid=%s&pageNo=%s&pageSize=%s&startDate=%s&endDate=%s&creatorId=%s", fileVo.getAccessToken(), fileVo.getDocId(), fileVo.getUserid(), StrUtil.isNotEmpty(fileVo.getPageNo()) ? fileVo.getPageNo() : "", StrUtil.isNotEmpty(fileVo.getPageSize()) ? fileVo.getPageSize() : "", StrUtil.isNotEmpty(fileVo.getStartDate()) ? fileVo.getStartDate() : "", StrUtil.isNotEmpty(fileVo.getEndDate()) ? fileVo.getEndDate() : "", StrUtil.isNotEmpty(fileVo.getCreatorId()) ? fileVo.getCreatorId() : "")).headerMap(headers, true)).execute().body();
        return (DocVersionResultVo)JSONObject.parseObject(jsonRes, DocVersionResultVo.class);
    }

    public static DocLogResultVo docLog(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/x-www-form-urlencoded");
        String jsonRes = ((HttpRequest)HttpRequest.get(host + String.format("/api/doc/document/v2/logs?access_token=%s&id=%s&userid=%s&pageNo=%s&pageSize=%s", fileVo.getAccessToken(), fileVo.getDocId(), fileVo.getUserid(), StrUtil.isNotEmpty(fileVo.getPageNo()) ? fileVo.getPageNo() : "", StrUtil.isNotEmpty(fileVo.getPageSize()) ? fileVo.getPageSize() : "")).headerMap(headers, true)).execute().body();
        return (DocLogResultVo)JSONObject.parseObject(jsonRes, DocLogResultVo.class);
    }

    public static DocCountResultVo docCount(DocVo docVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("conditions", docVo.getConditions());
        paramMap.put("keywords", docVo.getKeywords());
        paramMap.put("folder", docVo.getFolder());
        String jsonRes = ((HttpRequest)HttpRequest.post(host + String.format("/api/doc/document/v2/docCount?access_token=%s&userid=%s", docVo.getAccessToken(), docVo.getUserid())).headerMap(headers, true)).body(JSONObject.toJSONString(paramMap)).execute().body();
        return (DocCountResultVo)JSONObject.parseObject(jsonRes, DocCountResultVo.class);
    }

    public static DocListResultVo docList(DocVo docVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("folderId", docVo.getFolderId());
        String jsonRes = ((HttpRequest)HttpRequest.post(host + String.format("/api/doc/document/v2/folderDocList?access_token=%s", docVo.getAccessToken())).headerMap(headers, true)).body(JSONObject.toJSONString(paramMap)).execute().body();
        return (DocListResultVo)JSONObject.parseObject(jsonRes, DocListResultVo.class);
    }

    public static DocSaveResultVo docSave(DocVo docVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("document", docVo.getDocument());
        paramMap.put("folderId", docVo.getFolderId());
        String jsonRes = ((HttpRequest)HttpRequest.post(host + String.format("/api/doc/document/v2/saveDoc?access_token=%s&userid=%s", docVo.getAccessToken(), docVo.getUserid())).headerMap(headers, true)).body(JSONObject.toJSONString(paramMap)).execute().body();
        return (DocSaveResultVo)JSONObject.parseObject(jsonRes, DocSaveResultVo.class);
    }

    public static DocSaveResultVo docCheck(DocVo docVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("document", docVo.getDocument());
        String jsonRes = ((HttpRequest)HttpRequest.post(host + String.format("/api/doc/document/v2/checkEditLock?access_token=%s&userid=%s", docVo.getAccessToken(), docVo.getUserid())).headerMap(headers, true)).body(JSONObject.toJSONString(paramMap)).execute().body();
        return (DocSaveResultVo)JSONObject.parseObject(jsonRes, DocSaveResultVo.class);
    }

    public static DocResultVo addDocLog(DocVo docVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("operatetype", docVo.getOperateType());
        paramMap.put("targetId", docVo.getTargetId());
        String jsonRes = ((HttpRequest)HttpRequest.post(host + String.format("/api/doc/document/v2/addDocumentLog?access_token=%s&userid=%s", docVo.getAccessToken(), docVo.getUserid())).headerMap(headers, true)).body(JSONObject.toJSONString(paramMap)).execute().body();
        return (DocResultVo)JSONObject.parseObject(jsonRes, DocResultVo.class);
    }

    public static MessageResult docInvalidate(DocVo docVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "multipart/form-data");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("access_token", docVo.getAccessToken());
        paramMap.put("docId", docVo.getDocId());
        paramMap.put("userid", docVo.getUserid());
        String jsonRes = HttpRequest.post(host + "/api/doc/document/v2/invalidate").form(paramMap).execute().body();
        return (MessageResult)JSONObject.parseObject(jsonRes, MessageResult.class);
    }

    public static DocumentListResultVo docListAll(DocVo docVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("pageNo", docVo.getPageNo());
        paramMap.put("pageSize", docVo.getPageSize());
        paramMap.put("keywords", docVo.getKeywords());
        paramMap.put("queryReadStatus", docVo.getQueryReadStatus());
        paramMap.put("order", docVo.getOrder());
        paramMap.put("conditions", docVo.getConditions());
        paramMap.put("queryAbstractText", docVo.isQueryAbstractText());
        paramMap.put("queryCoverPicture", docVo.isQueryCoverPicture());
        String jsonRes = HttpRequest.post(host + String.format("/api/doc/documents/list/all?access_token=%s&userid=%s", docVo.getAccessToken(), docVo.getUserid())).body(JSONObject.toJSONString(paramMap)).execute().body();
        return (DocumentListResultVo)JSONObject.parseObject(jsonRes, DocumentListResultVo.class);
    }

    public static DocumentListResultVo docListMine(DocVo docVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("pageNo", docVo.getPageNo());
        paramMap.put("pageSize", docVo.getPageSize());
        paramMap.put("keywords", docVo.getKeywords());
        paramMap.put("queryReadStatus", docVo.getQueryReadStatus());
        paramMap.put("order", docVo.getOrder());
        paramMap.put("conditions", docVo.getConditions());
        String jsonRes = HttpRequest.post(host + String.format("/api/doc/documents/list/mine?access_token=%s&userid=%s", docVo.getAccessToken(), docVo.getUserid())).body(JSONObject.toJSONString(paramMap)).execute().body();
        return (DocumentListResultVo)JSONObject.parseObject(jsonRes, DocumentListResultVo.class);
    }

    public static DocCountResultVo docListAllCount(DocVo docVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("keywords", docVo.getKeywords());
        paramMap.put("conditions", docVo.getConditions());
        String jsonRes = HttpRequest.post(host + String.format("/api/doc/documents/list/getAllCount?access_token=%s&userid=%s", docVo.getAccessToken(), docVo.getUserid())).body(JSONObject.toJSONString(paramMap)).execute().body();
        return (DocCountResultVo)JSONObject.parseObject(jsonRes, DocCountResultVo.class);
    }

    public static DocNodeResultVo rootNodes(DocVo docVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("type", docVo.getType());
        String jsonRes = HttpRequest.post(host + String.format("/api/doc/folder/tree/rootNodes?access_token=%s&userid=%s", docVo.getAccessToken(), docVo.getUserid())).body(JSONObject.toJSONString(paramMap)).execute().body();
        return (DocNodeResultVo)JSONObject.parseObject(jsonRes, DocNodeResultVo.class);
    }

    public static DocSubNodeResultVo rootSubNodes(DocVo docVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("folderId", docVo.getFolderId());
        paramMap.put("module", docVo.getModule());
        paramMap.put("moduleId", docVo.getModuleId());
        String jsonRes = HttpRequest.post(host + String.format("/api/doc/folder/tree/subNodes?access_token=%s&userid=%s", docVo.getAccessToken(), docVo.getUserid())).body(JSONObject.toJSONString(paramMap)).execute().body();
        return (DocSubNodeResultVo)JSONObject.parseObject(jsonRes, DocSubNodeResultVo.class);
    }

    public static FolderListResultVo docFolderList(DocVo docVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("folderId", docVo.getFolderId());
        String jsonRes = HttpRequest.post(host + String.format("/api/doc/folder/list?access_token=%s&userid=%s", docVo.getAccessToken(), docVo.getUserid())).body(JSONObject.toJSONString(paramMap)).execute().body();
        return (FolderListResultVo)JSONObject.parseObject(jsonRes, FolderListResultVo.class);
    }

    public static FileResultVo uploadBase64File(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/x-www-form-urlencoded");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("access_token", fileVo.getAccessToken());
        paramMap.put("base64", fileVo.getBase64());
        paramMap.put("contentType", fileVo.getContentType());
        paramMap.put("name", fileVo.getName());
        paramMap.put("size", fileVo.getSize());
        paramMap.put("lastModified", fileVo.getLastModified());
        paramMap.put("module", fileVo.getModule());
        paramMap.put("folderId", fileVo.getFolderId());
        paramMap.put("createDocContent", fileVo.isCreateDocContent());
        paramMap.put("createDocument", fileVo.isCreateDocument());
        paramMap.put("refId", fileVo.getRefId());
        String resJson = ((HttpRequest)HttpRequest.post(host + "/api/file/v2/common/upload/base64").form(paramMap).headerMap(headers, true)).execute().body();
        return (FileResultVo)JSONObject.parseObject(resJson, FileResultVo.class);
    }

    public static FilePageResultVo queryFileInfo(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        String url = host + "/api/file/v2/common/file/queryFileInfo?access_token=" + fileVo.getAccessToken();
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("userid", fileVo.getUserid());
        bodyMap.put("fileIdList", fileVo.getFileIdList());
        final String json = JSON.toJSONString(bodyMap);
        headers.put("Content-Type", "application/json");
        final String body = HttpRequest.post(url).body(json).headerMap(headers, true).execute().body();
        return JSONObject.parseObject(body, FilePageResultVo.class);
    }

    public static FileResultVo getFileVersion(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/json");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("access_token", fileVo.getAccessToken());
        paramMap.put("userid", fileVo.getUserid());
        paramMap.put("fileId", fileVo.getFileId());
        paramMap.put("name", fileVo.getName());
        paramMap.put("startTime", fileVo.getStartTime());
        paramMap.put("endTime", fileVo.getEndTime());
        paramMap.put("pageNo", fileVo.getPageNo());
        paramMap.put("pageSize", fileVo.getPageSize());
        paramMap.put("creatorIds", fileVo.getCreatorIds());
        paramMap.put("tenantKey", fileVo.getTenantKey());
        paramMap.put("module", fileVo.getModule());
        String resJson = ((HttpRequest)HttpRequest.post(host + "/api/file/v2/common/file/getFileVersion").form(paramMap).headerMap(headers, true)).execute().body();
        return (FileResultVo)JSONObject.parseObject(resJson, FileResultVo.class);
    }

    public static InputStream downloadE9File(FileVo fileVo, String host, Map<String, String> headers) {
        host = HostUtil.beforeRequestCheckHeaders(host, headers, "application/x-www-form-urlencoded");
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("access_token", fileVo.getAccessToken());
        paramMap.put("userid", fileVo.getUserid());
        paramMap.put("e9FileKey", fileVo.getE9FileKey());
        return ((HttpRequest)HttpRequest.get(host + "/api/file/v2/common/download/e9").form(paramMap).headerMap(headers, true)).execute().bodyStream();
    }
}

-- 按钮父菜单ID
SELECT @parentId := 1912680518862688258;

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '系统日历查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:calendar:list',        '#', 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '系统日历新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:calendar:add',          '#', 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '系统日历修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:calendar:edit',         '#', 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '系统日历删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'system:calendar:remove',       '#', 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '系统日历导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'system:calendar:export',       '#', 1, sysdate(), null, null, '');
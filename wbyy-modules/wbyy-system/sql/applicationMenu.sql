-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(),0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(@parentId, '应用管理', '1', '1', 'application', 'system/application/index', 1, 0, 'C', '0', '0', 'system:application:list', '#', 'admin', sysdate(), '', null, '应用管理菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(),0), '应用管理查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:application:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(),0), '应用管理新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:application:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(),0), '应用管理修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:application:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(),0), '应用管理删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'system:application:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(),0), '应用管理导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'system:application:export',       '#', 'admin', sysdate(), '', null, '');
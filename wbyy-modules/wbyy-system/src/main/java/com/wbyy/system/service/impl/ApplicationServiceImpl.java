package com.wbyy.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.constant.HttpMagConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.system.domain.Application;
import com.wbyy.system.mapper.ApplicationMapper;
import com.wbyy.system.service.IApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 应用管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApplicationServiceImpl extends ServiceImpl<ApplicationMapper, Application> implements IApplicationService {
    private final ApplicationMapper applicationMapper;

    /**
     * 查询应用管理
     *
     * @param id 应用管理主键
     * @return 应用管理
     */
    @Override
    public Application selectApplicationById(Long id) {
        return this.getById(id);
    }

    /**
     * 查询应用管理列表
     *
     * @param application 应用管理
     * @return 应用管理
     */
    @Override
    public List<Application> selectApplicationList(Application application) {
        LambdaQueryWrapper<Application> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotBlank(application.getName()), Application::getName, application.getName());
        wrapper.like(StrUtil.isNotBlank(application.getShortName()), Application::getShortName, application.getShortName());
        wrapper.eq(null != application.getStatus() , Application::getStatus, application.getStatus());
        wrapper.orderByAsc(Application::getOrderSort);
        return this.list(wrapper);
    }

    /**
     * 新增应用管理
     *
     * @param application 应用管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertApplication(Application application) {
        String code = application.getCode();
        Application applicationDb = getApplicationByCode(code);
        if (applicationDb != null) {
            throw new ServiceException("应用编码不能重复");
        }

        return this.save(application);
    }

    /**
     * 修改应用管理
     *
     * @param application 应用管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateApplication(Application application) {
        String code = application.getCode();
        Application applicationDb = getApplicationByCode(code);
        if (applicationDb != null && !applicationDb.getId().equals(application.getId())) {
            throw new ServiceException("应用编码不能重复");
        }

        return this.updateById(application);
    }

    /**
     * 批量删除应用管理
     *
     * @param ids 需要删除的应用管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteApplicationByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    /**
     * 根据 code 查询应用
     *
     * @param code 应用代码
     * @return
     */
    public Application getApplicationByCode(String code) {

        Optional.ofNullable(code).orElseThrow(() -> new ServiceException(HttpMagConstants.PARAMS_NOT_NULL));

        LambdaQueryWrapper<Application> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Application::getCode, code);
        return this.getOne(wrapper, false);
    }

}

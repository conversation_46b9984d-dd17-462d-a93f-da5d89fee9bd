package com.wbyy.system.domain.vo;

import com.wbyy.common.core.utils.StringUtils;
import lombok.Data;

/**
 * 路由显示信息
 * 
 * <AUTHOR>
 */
@Data
public class MetaVo
{
    /**
     * 设置该路由在侧边栏和面包屑中展示的名字
     */
    private String title;

    /**
     * 设置该路由的图标，对应路径src/assets/icons/svg
     */
    private String icon;

    /**
     * 设置为true，则不会被 <keep-alive>缓存
     */
    private boolean noCache;

    /**
     * 内链地址（http(s)://开头）
     */
    private String link;

    /**
     * 是否隐藏路由，当设置 true 的时候该路由不会再侧边栏出现
     */
    private boolean hidden;

    /** 重定向 */
    private String redirect;

    //是否显示在菜单中显示隐藏一级路由（默认值：false）
    private Boolean levelHidden;

    //是否是自定义svg图标（默认值：false，如果设置true，那么需要把你的svg拷贝到icon下，然后icon字段配置上你的图标名）
    private Boolean isCustomSvg;

    //当前路由是否可关闭多标签页，同上（2021年10月后新版支持）
    private Boolean noClosable;

    //是否隐藏分栏，仅在分栏布局中二级路由生效（默认值：false，不推荐使用）
    private Boolean noColumn;

    //badge小标签（只支持子级，String类型，支持自定义）
    private String badge;

    //当前路由是否不显示多标签页（默认值：false，不推荐使用）
    private Boolean tabHidden;

    //是否浏览新标签页打开（不适用于分栏布局左侧tab部分，不推荐使用）
    private String target;

    //高亮指定菜单，要从跟路由的path开始拼接（用于隐藏页高亮）
    private String activeMenu;

    //小圆点（默认值：false）
    private Boolean dot;

    //动态传参路由是否新开标签页（默认值：false）
    private Boolean dynamicNewTab;

    //面包屑是否显示（默认值：false）
    private Boolean breadcrumbHidden;

    public MetaVo()
    {
    }

    public MetaVo(String title, String icon)
    {
        this.title = title;
        this.icon = icon;
    }

    public MetaVo(String title, String icon, boolean noCache)
    {
        this.title = title;
        this.icon = icon;
        this.noCache = noCache;
    }

    public MetaVo(String title, String icon, String link, boolean hidden)
    {
        this.hidden = hidden;
        this.title = title;
        this.icon = icon;
        this.link = link;
    }

    public MetaVo(String title, String icon, boolean noCache, String link, boolean hidden)
    {
        this.hidden = hidden;
        this.title = title;
        this.icon = icon;
        this.noCache = noCache;
        if (StringUtils.ishttp(link))
        {
            this.link = link;
        }
    }

}

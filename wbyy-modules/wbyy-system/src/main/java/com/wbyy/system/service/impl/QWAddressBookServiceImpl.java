package com.wbyy.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.wbyy.common.core.constant.AppCodeConst;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.qyweixin.config.WxCpConfiguration;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.system.domain.SysDept;
import com.wbyy.system.domain.SysUser;
import com.wbyy.system.domain.SysUserDept;
import com.wbyy.system.mapper.SysUserDeptMapper;
import com.wbyy.system.model.AsyncDeptDto;
import com.wbyy.system.model.AsyncUserDto;
import com.wbyy.system.model.DeptTree;
import com.wbyy.system.model.UpdateQwUserIdDto;
import com.wbyy.system.service.ISysDeptService;
import com.wbyy.system.service.ISysUserService;
import com.wbyy.system.service.AddressBookService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpDepartmentService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.WxCpUserService;
import me.chanjar.weixin.cp.bean.WxCpDepart;
import me.chanjar.weixin.cp.bean.WxCpUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wbyy.common.core.constant.Constants.*;

@Service(value = "qWAddressBookService")
@Slf4j
public class QWAddressBookServiceImpl implements AddressBookService {
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private SysUserDeptMapper userDeptMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    @XxlJob(value = "qwTxlAsyncJobHandler")
    public ReturnT<String> async() {
        List<WxCpDepart> wxDeptList = null;
        WxCpService cpService = WxCpConfiguration.getCpService(AppCodeConst.MESSAGE);
        WxCpDepartmentService wxCpDepartmentService = cpService.getDepartmentService();
        WxCpUserService wxCpUserService = cpService.getUserService();
        try {
            // 获取企微部门
            wxDeptList = wxCpDepartmentService.list(null);
        } catch (WxErrorException e) {
            log.error("获取企微部门失败", e);
            return ReturnT.FAIL;
        }
        if (CollectionUtil.isEmpty(wxDeptList)) {
            log.error("企微部门信息为空");
            return ReturnT.FAIL;
        }
        // 通过树形结构构造企微部门的ancestors，祖级数据结构
        List<DeptTree> qwDeptList = handleQwDeptList(wxDeptList);
        // 系统部门数据
        List<SysDept> deptList = sysDeptService.list();
        // 企微部门map
        Map<Long, SysDept> qwDeptIdMap = deptList.stream()
                .collect(Collectors.toMap(SysDept::getDeptId, Function.identity(), (o1, o2) -> o1));
        // 要保存的部门数据
        List<SysDept> saveDeptList = new ArrayList<>();
        // 要更新的部门数据
        List<SysDept> updateDeptList = new ArrayList<>();
        // 企微部门id集合
        Set<Long> qwDeptIdSet = new HashSet<>();
        // 系统用户数据
        List<SysUser> userList = sysUserService.list();
        // 企微用户idmap
        Map<String, SysUser> qwUserIdMap = userList.stream()
                .collect(Collectors.toMap(SysUser::getQwUserId, Function.identity(), (o1, o2) -> o1));
        // 要保存的用户数据
        List<SysUser> saveUserList = new ArrayList<>();
        // 要修改的用户数据
        List<SysUser> updateUserList = new ArrayList<>();
        // 企微用户id集合
        Set<String> qwUserIdSet = new HashSet<>();
        int deptSort = 0;
        // 企微用户和企微部门id关联映射
        Map<String, Set<Long>> qwUserIdDeptIdSetMap = new HashMap<>();
        for (DeptTree qwDept : qwDeptList) {
            Long qwDeptId = qwDept.getId();
            qwDeptIdSet.add(qwDeptId);
            SysDept findDept = qwDeptIdMap.get(qwDeptId);
            if (null == findDept) {
                // 要新增的部门
                findDept = new SysDept();
                findDept.setDeptId(qwDeptId);
                findDept.setParentId(qwDept.getParentId());
                findDept.setAncestors(qwDept.getAncestors());
                findDept.setDeptName(qwDept.getName());
                findDept.setOrderSort(deptSort);
                findDept.setLeader(qwDept.getLeader());
                findDept.setStatus(ENABLE_STATUS);
                findDept.setDelFlag(NO_DELETE);
                findDept.setCreateBy(SecurityUtils.getUserId());
                findDept.setQwDeptId(qwDeptId);
                saveDeptList.add(findDept);
            } else {
                // 要修改的部门
                findDept.setParentId(qwDept.getParentId());
                findDept.setAncestors(qwDept.getAncestors());
                findDept.setDeptName(qwDept.getName());
                findDept.setLeader(qwDept.getLeader());
                findDept.setUpdateBy(SecurityUtils.getUserId());
                updateDeptList.add(findDept);
            }
            deptSort++;
            List<WxCpUser> wxUserList;
            try {
                // 获取企微部门下的用户
                wxUserList = wxCpUserService.listByDepartment(qwDeptId, false, 0);
            } catch (WxErrorException e) {
                log.error("根据部门id【{}】获取企微人员失败", qwDeptId, e);
                return ReturnT.FAIL;
            }
            if (CollectionUtil.isEmpty(wxUserList)) {
                log.warn("根据部门id【{}】获取企微人员为空", qwDeptId);
            }
            for (WxCpUser wxCpUser : wxUserList) {
                String qwUserId = wxCpUser.getUserId();
                qwUserIdSet.add(qwUserId);
                // 工号
                String workNumber = "";
                List<WxCpUser.Attr> extAttrs = wxCpUser.getExtAttrs();
                if (CollectionUtil.isNotEmpty(extAttrs)) {
                    workNumber = extAttrs.stream().filter(item -> "工号".equals(item.getName())).map(WxCpUser.Attr::getTextValue)
                            .findFirst().orElse("");
                }
                // 用户-部门映射关系处理
                Set<Long> deptIdSet = qwUserIdDeptIdSetMap.get(qwUserId);
                if (CollectionUtil.isNotEmpty(deptIdSet)){
                    deptIdSet.add(qwDeptId);
                    qwUserIdDeptIdSetMap.put(qwUserId,deptIdSet);
                    continue;
                }else {
                    deptIdSet = new HashSet<>();
                    deptIdSet.add(qwDeptId);
                    qwUserIdDeptIdSetMap.put(qwUserId,deptIdSet);
                }
                SysUser findUser = qwUserIdMap.get(qwUserId);
                if (null == findUser) {
                    // 新增用户
                    findUser = new SysUser();
                    findUser.setUserId(IdUtil.getSnowflake().nextId());
                    findUser.setUserName(wxCpUser.getName());
                    findUser.setRealName(wxCpUser.getName());
                    findUser.setWorkNumber(workNumber);
                    findUser.setPassword(SecurityUtils.encryptPassword(DEFAULT_PASSWORD));
                    findUser.setStatus(ENABLE_STATUS);
                    findUser.setDelFlag(NO_DELETE);
                    findUser.setQwUserId(qwUserId);
                    findUser.setCreateBy(SecurityUtils.getUserId());
                    saveUserList.add(findUser);
                } else {
                    // 修改用户
                    findUser.setUserName(wxCpUser.getName());
                    findUser.setRealName(wxCpUser.getName());
                    findUser.setWorkNumber(workNumber);
                    findUser.setUpdateBy(SecurityUtils.getUserId());
                    updateUserList.add(findUser);
                }
            }
        }
        Set<Long> deleteDeptIdSet = deptList.stream().map(SysDept::getDeptId)
                .filter(deptId -> !qwDeptIdSet.contains(deptId)).collect(Collectors.toSet());
        Set<Long> deleteUserIdSet = userList.stream().filter(item -> !qwUserIdSet.contains(item.getQwUserId()))
                .filter(item -> !(null!=item.getUserId()&&item.getUserId()==1L))
                .map(SysUser::getUserId).collect(Collectors.toSet());
        Set<Long> deleteUserDeptIdSet = new HashSet<>(deleteUserIdSet);
        List<SysUserDept> saveUserDeptList = new ArrayList<>();
        // 构造用户-部门关系表数据
        for (String qwUserId : qwUserIdDeptIdSetMap.keySet()) {
            Long userId = null;
            if (CollectionUtil.isNotEmpty(saveUserList)){
                userId = saveUserList.stream().filter(item->qwUserId.equals(item.getQwUserId()))
                        .map(SysUser::getUserId).findFirst().orElse(null);
            }else if (CollectionUtil.isNotEmpty(updateUserList)){
                userId = updateUserList.stream().filter(item->qwUserId.equals(item.getQwUserId()))
                        .map(SysUser::getUserId).findFirst().orElse(null);
            }
            if (null==userId) continue;
            Set<Long> deptIdSet = qwUserIdDeptIdSetMap.get(qwUserId);
            for (Long deptId : deptIdSet) {
                SysUserDept userDept = new SysUserDept();
                userDept.setUserId(userId);
                userDept.setDeptId(deptId);
                deleteUserDeptIdSet.add(userId);
                saveUserDeptList.add(userDept);
            }
        }
        return transactionTemplate.execute(status -> {
            try {
                if (CollectionUtil.isNotEmpty(deleteUserIdSet)){
                    sysUserService.deleteFromRemoteByUserIdSet(deleteUserIdSet);
                }
                if (CollectionUtil.isNotEmpty(deleteDeptIdSet)){
                    sysDeptService.removeBatchByIds(deleteDeptIdSet);
                }
                if (CollectionUtil.isNotEmpty(deleteUserDeptIdSet)){
                    userDeptMapper.deleteUserDept(deleteUserDeptIdSet.toArray(Long[]::new));
                }
                if (CollectionUtil.isNotEmpty(saveDeptList)){
                    sysDeptService.saveBatchFromRemote(saveDeptList);
                }
                if (CollectionUtil.isNotEmpty(updateDeptList)){
                    sysDeptService.updateBatchFromRemote(updateDeptList);
                }
                if (CollectionUtil.isNotEmpty(saveUserList)){
                    sysUserService.saveBatchFromRemote(saveUserList);
                }
                if (CollectionUtil.isNotEmpty(updateUserList)){
                    sysUserService.updateBatchFromRemote(updateUserList);
                }
                if (CollectionUtil.isNotEmpty(saveUserDeptList)){
                    userDeptMapper.batchUserDept(saveUserDeptList);
                }
            }catch (Exception e){
                log.error("数据库操作失败",e);
                status.setRollbackOnly();
                return ReturnT.FAIL;
            }
            log.info("企微通讯录同步成功");
            return ReturnT.SUCCESS;
        });
    }

    @Override
    public Boolean asyncUser(List<AsyncUserDto> dto) {
        return null;
    }

    @Override
    public Boolean asyncDept(List<AsyncDeptDto> dto) {
        return null;
    }

    @Override
    public void asyncUserid() {
        List<WxCpDepart> wxDeptList = null;
        WxCpService cpService = WxCpConfiguration.getCpService(AppCodeConst.MESSAGE);
        WxCpDepartmentService wxCpDepartmentService = cpService.getDepartmentService();
        WxCpUserService wxCpUserService = cpService.getUserService();
        try {
            // 获取企微部门
            wxDeptList = wxCpDepartmentService.list(null);
        } catch (WxErrorException e) {
            log.error("获取企微部门失败", e);
            return;
        }
        if (CollectionUtil.isEmpty(wxDeptList)) {
            log.error("企微部门信息为空");
            return;
        }
        List<UpdateQwUserIdDto> updateQwUserIds = new ArrayList<>();
        // 通过树形结构构造企微部门的ancestors，祖级数据结构
        List<DeptTree> qwDeptList = handleQwDeptList(wxDeptList);
        for (DeptTree qwDept : qwDeptList) {
            Long qwDeptId = qwDept.getId();
            List<WxCpUser> wxUserList;
            try {
                // 获取企微部门下的用户
                wxUserList = wxCpUserService.listByDepartment(qwDeptId, false, 0);
            } catch (WxErrorException e) {
                log.error("根据部门id【{}】获取企微人员失败", qwDeptId, e);
                return;
            }
            if (CollectionUtil.isEmpty(wxUserList)) {
                log.warn("根据部门id【{}】获取企微人员为空", qwDeptId);
            }
            for (WxCpUser wxCpUser : wxUserList) {
                String qwUserId = wxCpUser.getUserId();
                UpdateQwUserIdDto dto = new UpdateQwUserIdDto();
                dto.setQwUserId(qwUserId);
                dto.setUserName(wxCpUser.getName());
                updateQwUserIds.add(dto);
            }
        }
        sysUserService.updateQwUserId(updateQwUserIds);
    }

    @Override
    public void asyncPost() {

    }

    /**
     * 企微部门列表的祖级数据结构处理
     * @param wxDeptList
     * @return
     */
    private List<DeptTree> handleQwDeptList(List<WxCpDepart> wxDeptList) {
        DeptTree deptTree = buildQwDeptTree(wxDeptList);
        List<DeptTree> result = new ArrayList<>();
        flattenTreeHelper(deptTree, result);
        return result;
    }

    /**
     * 构建部门树结构
     * @param wxDeptList
     * @return
     */
    private DeptTree buildQwDeptTree(List<WxCpDepart> wxDeptList) {
        // 创建一个 Map 来存储每个部门 ID 对应的 QwDeptTree 节点
        Map<Long, DeptTree> deptTreeMap = new HashMap<>();

        // 遍历 wxDeptList，为每个部门创建一个 QwDeptTree 节点，并将其放入 Map 中
        for (WxCpDepart wxCpDepart : wxDeptList) {
            DeptTree deptTree = new DeptTree();
            deptTree.setId(wxCpDepart.getId());
            deptTree.setName(wxCpDepart.getName());
            deptTree.setParentId(wxCpDepart.getParentId());
            deptTree.setLeader(null != wxCpDepart.getDepartmentLeader() && wxCpDepart.getDepartmentLeader().length > 0 ? wxCpDepart.getDepartmentLeader()[0] : "");
            deptTreeMap.put(wxCpDepart.getId(), deptTree);
        }

        // 再次遍历 wxDeptList，根据每个部门的父部门 ID，将子节点添加到父节点的子节点列表中
        for (WxCpDepart wxCpDepart : wxDeptList) {
            Long parentId = wxCpDepart.getParentId();
            if (parentId != null && deptTreeMap.containsKey(parentId)) {
                deptTreeMap.get(parentId).getChildren().add(deptTreeMap.get(wxCpDepart.getId()));
            }
        }

        // 找到根节点（ID 为 1）并将其设置为 qwDeptTree 的根节点
        DeptTree rootDeptTree = deptTreeMap.get(1L);
        if (rootDeptTree == null) {
            log.error("未找到根节点（ID 为 1）");
            throw new ServiceException("未找到根节点（ID 为 1）");
        }

        // 设置每个节点的 ancestors 字段
        setAncestors(rootDeptTree, "");

        return rootDeptTree;
    }




}

package com.wbyy.system.config.qw.handler;


import com.alibaba.fastjson2.JSON;
import com.wbyy.common.qyweixin.config.builder.TextBuilder;
import com.wbyy.common.qyweixin.config.handler.IWxCpMessageHandler;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import me.chanjar.weixin.cp.constant.WxCpConsts;
import me.chanjar.weixin.cp.message.WxCpMessageHandler;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 通讯录变更事件处理器.
 *
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Slf4j
@Component
public class ContactChangeHandler implements IWxCpMessageHandler {

    @Override
    public WxCpXmlOutMessage handle(WxCpXmlMessage wxMessage, Map<String, Object> context, WxCpService cpService,
                                    WxSessionManager sessionManager) {
        String content = "收到通讯录变更事件，内容：" + JSON.toJSONString(wxMessage);
        log.info(content);

        return new TextBuilder().build(content, wxMessage, cpService);
    }

    @Override
    public String getMsgType() {
        return WxConsts.XmlMsgType.EVENT;
    }

    @Override
    public String getEvent() {
        return WxCpConsts.EventType.CHANGE_CONTACT;
    }
}

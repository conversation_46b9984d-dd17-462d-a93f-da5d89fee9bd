package com.wbyy.system;

import com.wbyy.common.core.constant.SecurityConstants;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.wbyy.common.security.annotation.EnableCustomConfig;
import com.wbyy.common.security.annotation.EnableRyFeignClients;

/**
 * 系统模块
 * 
 * <AUTHOR>
 */
@Slf4j
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
public class WbyySystemApplication
{
    public static void main(String[] args)
    {
        MDC.put(SecurityConstants.TRACE_ID, "system");
        SpringApplication.run(WbyySystemApplication.class, args);
        log.info("(♥◠‿◠)ﾉﾞ  系统模块启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    }
}

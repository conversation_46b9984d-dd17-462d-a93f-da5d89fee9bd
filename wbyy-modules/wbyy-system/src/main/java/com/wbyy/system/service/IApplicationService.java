package com.wbyy.system.service;

import java.util.List;
import com.wbyy.system.domain.Application;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 应用管理Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IApplicationService  extends IService<Application> {
    /**
     * 查询应用管理
     *
     * @param id 应用管理主键
     * @return 应用管理
     */
    Application selectApplicationById(Long id);

    /**
     * 查询应用管理列表
     *
     * @param application 应用管理
     * @return 应用管理集合
     */
    List<Application> selectApplicationList(Application application);

    /**
     * 新增应用管理
     *
     * @param application 应用管理
     * @return 结果
     */
    boolean insertApplication(Application application);

    /**
     * 修改应用管理
     *
     * @param application 应用管理
     * @return 结果
     */
    boolean updateApplication(Application application);

    /**
     * 批量删除应用管理
     *
     * @param ids 需要删除的应用管理主键集合
     * @return 结果
     */
    boolean deleteApplicationByIds(Long[] ids);

    /**
     * 根据 code 查询应用
     *
     * @param code 应用代码
     * @return
     */
    Application getApplicationByCode(String code);
}

package com.wbyy.system.service;

import java.util.List;
import java.util.Set;

import com.wbyy.system.domain.SysPost;
import com.wbyy.system.domain.SysUser;

/**
 * 权限信息 服务层
 * 
 * <AUTHOR>
 */
public interface ISysPermissionService
{
    /**
     * 获取角色数据权限
     * 
     * @param userId 用户Id
     * @return 角色权限信息
     */
    public Set<String> getRolePermission(SysUser user);
    public Set<String> getRolePermissionByApplicationCode(SysUser user,String applicationCode);

    /**
     * 获取菜单数据权限
     * 
     * @param userId 用户Id
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(SysUser user);
    public Set<String> getMenuPermissionByApplication(SysUser user,String applicationCode);

    public Set<Long> getDeptPermission(SysUser user);

    Set<Long> getSonDeptPermission(Set<Long> depts);

    String getDeptFullName(SysUser user);

    List<SysPost> getPosts(SysUser user);
}

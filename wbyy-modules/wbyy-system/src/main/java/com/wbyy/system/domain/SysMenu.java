package com.wbyy.system.domain;

import java.util.ArrayList;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.orm.core.domain.BaseEntity;

/**
 * 菜单权限表 sys_menu
 * 
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_menu")
public class SysMenu extends BaseEntity
{
    /** 菜单ID */
    @TableId(value = "menu_id")
    private Long menuId;

    /** 菜单名称 */
    private String menuName;

    /** 父菜单名称 */
    @TableField(exist = false)
    private String parentName;

    /** 父菜单ID */
    private Long parentId;

    /** 显示顺序 */
    private Integer menuSort;

    /** 路由地址 */
    private String path;

    /** 组件路径 */
    private String component;


    /** 路由参数 */
    private String query;

    /** 路由名称，默认和路由地址相同的驼峰格式（注意：因为vue3版本的router会删除名称相同路由，为避免名字的冲突，特殊情况可以自定义） */
    private String routeName;

    /** 是否为外链（0是 1否） */
    private String isFrame;

    /** 是否缓存（0缓存 1不缓存） */
    private String isCache;

    /** 类型（M目录 C菜单 F按钮） */
    private String menuType;

    /** 显示状态（0显示 1隐藏） */
    private Integer visible;
    
    /** 菜单状态（0正常 1停用） */
    private Integer status;

    /** 权限字符串 */
    private String perms;

    /** 菜单图标 */
    private String icon;

    private String remark;

    /** 重定向 */
    private String redirect;

    //是否显示在菜单中显示隐藏一级路由（默认值：false）
    private Boolean levelHidden;

    //是否是自定义svg图标（默认值：false，如果设置true，那么需要把你的svg拷贝到icon下，然后icon字段配置上你的图标名）
    private Boolean isCustomSvg;

    //当前路由是否可关闭多标签页，同上（2021年10月后新版支持）
    private Boolean noClosable;

    //是否隐藏分栏，仅在分栏布局中二级路由生效（默认值：false，不推荐使用）
    private Boolean noColumn;

    //badge小标签（只支持子级，String类型，支持自定义）
    private String badge;

    //当前路由是否不显示多标签页（默认值：false，不推荐使用）
    private Boolean tabHidden;

    //是否浏览新标签页打开（不适用于分栏布局左侧tab部分，不推荐使用）
    private String target;

    //高亮指定菜单，要从跟路由的path开始拼接（用于隐藏页高亮）
    private String activeMenu;

    //小圆点（默认值：false）
    private Boolean dot;

    //动态传参路由是否新开标签页（默认值：false）
    private Boolean dynamicNewTab;

    //面包屑是否显示（默认值：false）
    private Boolean breadcrumbHidden;

    // 应用编码
    @NotBlank(message = "应用编码不能为空")
    private String applicationCode;


    /** 子菜单 */
    @TableField(exist = false)
    private List<SysMenu> children = new ArrayList<SysMenu>();

    @NotBlank(message = "菜单名称不能为空")
    @Size(min = 0, max = 50, message = "菜单名称长度不能超过50个字符")
    public String getMenuName()
    {
        return menuName;
    }

    @NotNull(message = "显示顺序不能为空")
    public Integer getMenuSort()
    {
        return menuSort;
    }

    @Size(min = 0, max = 200, message = "路由地址不能超过200个字符")
    public String getPath()
    {
        return path;
    }

    @Size(min = 0, max = 200, message = "组件路径不能超过255个字符")
    public String getComponent()
    {
        return component;
    }


    @NotBlank(message = "菜单类型不能为空")
    public String getMenuType()
    {
        return menuType;
    }

    @Size(min = 0, max = 100, message = "权限标识长度不能超过100个字符")
    public String getPerms()
    {
        return perms;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("menuId", getMenuId())
            .append("menuName", getMenuName())
            .append("parentId", getParentId())
            .append("orderNum", getMenuSort())
            .append("path", getPath())
            .append("component", getComponent())
            .append("query", getQuery())
            .append("routeName", getRouteName())
            .append("isFrame", getIsFrame())
            .append("IsCache", getIsCache())
            .append("menuType", getMenuType())
            .append("visible", getVisible())
            .append("status ", getStatus())
            .append("perms", getPerms())
            .append("icon", getIcon())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

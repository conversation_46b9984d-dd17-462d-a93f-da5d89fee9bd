package com.wbyy.system.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DeptTree {
    private Long id;
    private String name;
    private Long parentId;
    private String ancestors;
    private String leader;
    /**
     * 0:启用;1:禁用
     */
    private Integer status;
    /**
     * 0:未删除;1:已删除
     */
    private Integer delFlag;
    private List<DeptTree> children = new ArrayList<>();
}

package com.wbyy.system.service;

import com.wbyy.system.model.AsyncDeptDto;
import com.wbyy.system.model.AsyncUserDto;
import com.wbyy.system.model.DeptTree;
import com.xxl.job.core.biz.model.ReturnT;

import java.util.List;

public interface AddressBookService {
    ReturnT<String> async();

    /**
     * @Description: 同步用户
     * @param dto
     * @return: Boolean
     * @author: 王金都
     * @Date: 2025/5/6 9:26
     */
    Boolean asyncUser(List<AsyncUserDto> dto);

    /**
     * @Description: 同步部门
     * @param dto
     * @return: Boolean
     * @author: 王金都
     * @Date: 2025/5/6 9:26
     */
    Boolean asyncDept(List<AsyncDeptDto> dto);

    /**
     * @Description: 同步userid
     * @param
     * @return: void
     * @author: 王金都
     * @Date: 2025/5/19 10:16
     */
    void asyncUserid();

    /**
     * @Description: 同步岗位
     * @param
     * @return: void
     * @author: 王金都
     * @Date: 2025/5/20 10:02
     */
    void asyncPost();

    /**
     * 部门祖级数据设置
     * @param node
     * @param ancestors
     */
    default void setAncestors(DeptTree node, String ancestors) {
        if (node == null) {
            return;
        }
        if (!ancestors.isEmpty()) {
            node.setAncestors(ancestors + "," + node.getId());
        } else {
            node.setAncestors(String.valueOf(node.getId()));
        }
        for (DeptTree child : node.getChildren()) {
            setAncestors(child, node.getAncestors());
        }
    }

    /**
     * 展开部门树
     * @param node
     * @param flatList
     */
    default void flattenTreeHelper(DeptTree node, List<DeptTree> flatList) {
        if (node == null) {
            return;
        }
        flatList.add(node);
        for (DeptTree child : node.getChildren()) {
            flattenTreeHelper(child, flatList);
        }
    }
}

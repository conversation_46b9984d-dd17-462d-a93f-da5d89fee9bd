package com.wbyy.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.system.domain.SysDept;
import com.wbyy.system.domain.SysUserDept;
import com.wbyy.system.domain.vo.TreeSelect;
import com.wbyy.system.model.ManageDeptDto;
import com.wbyy.system.model.UserCenterDeptVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 部门管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysDeptService extends IService<SysDept> {
    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<SysDept> selectDeptList(SysDept dept);

    /**
     * 查询部门树结构信息
     *
     * @param dept 部门信息
     * @return 部门树信息集合
     */
    public List<TreeSelect> selectDeptTreeList(SysDept dept);

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    public List<SysDept> buildDeptTree(List<SysDept> depts);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    public List<Long> selectDeptListByRoleId(Long roleId);

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    public SysDept selectDeptById(Long deptId);

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    public int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在部门子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public boolean hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    public boolean checkDeptNameUnique(SysDept dept);

    /**
     * 校验部门编码是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    boolean checkDeptCodeUnique(SysDept dept);

    /**
     * 校验部门是否有数据权限
     *
     * @param deptIds 部门id
     */
    public void checkDeptDataScope(Long... deptIds);

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int insertDept(SysDept dept);

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int updateDept(SysDept dept);

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId);

    public Set<Long> selectDeptPermissionByUserId(Long userId);

    void saveBatchFromRemote(List<SysDept> list);

    void updateBatchFromRemote(List<SysDept> list);


    /**
     * 查询所有子集
     *
     * @param centerIds
     * @return
     */
    List<SysDept> selectAllChildrenByDeptIds(List<Long> centerIds);

    /**
     * @param userId
     * @Description: 根据用户Id获取用户直属部门和所属中心
     * @return: UserCenterDeptVo
     * @author: 王金都
     * @Date: 2025/5/7 11:03
     */
    UserCenterDeptVo getUserCenterDept(Long userId);

    List<SysDept> listCenterDept(Set<Long> deptIds);

    Set<Long> selectSonDeptPermission(Set<Long> depts);

    List<SysDept> selectAllParentByDeptId(Long deptId);

    /**
     * @param dto
     * @Description: 查询部门和下属部门
     * @return: List<SysDept>
     * @author: 王金都
     * @Date: 2025/6/10 16:52
     */
    List<SysDept> listManageDept(ManageDeptDto dto);

    /**
     * @param userIds
     * @Description: 根据用户id查询集合
     * @return: List<SysUserDept>
     * @author: 王金都
     * @Date: 2025/6/11 14:24
     */
    List<SysUserDept> listByUserIds(List<Long> userIds);

    List<SysDept> listByDeptIds(Set<Long> deptIds);
}

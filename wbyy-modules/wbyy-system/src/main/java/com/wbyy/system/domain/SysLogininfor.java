package com.wbyy.system.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.common.core.annotation.Excel.ColumnType;
import com.wbyy.orm.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统访问记录表 sys_logininfor
 * 
 * <AUTHOR>
 */
@Data
@TableName("sys_logininfor")
public class SysLogininfor
{
    /** ID */
    @Excel(name = "序号", cellType = ColumnType.NUMERIC)
    @TableId(value = "info_id")
    private Long infoId;

    /** 用户账号 */
    @Excel(name = "用户账号")
    private String userName;

    /** 状态 0成功 1失败 */
    @Excel(name = "状态", readConverterExp = "0=成功,1=失败")
    private String status;

    /** 地址 */
    @Excel(name = "地址")
    private String ipaddr;

    /** 描述 */
    @Excel(name = "描述")
    private String msg;

    /** 访问时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "访问时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date accessTime;

    @TableField(exist = false)
    private Date beginTime;

    @TableField(exist = false)
    private Date endTime;

}
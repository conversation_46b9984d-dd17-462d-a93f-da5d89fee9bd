package com.wbyy.system.domain.vo;

import com.wbyy.system.domain.Calendar;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CalendarVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 7932516739531557226L;


    /** 节假日名称 */
    private String holidayName;

    /** 设置类型（1：休息日；2：工作日） */
    private Integer dateType;

    /** 节假日日期 */
    private String day;

}

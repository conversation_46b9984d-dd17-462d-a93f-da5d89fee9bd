package com.wbyy.system.api.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.PageUtils;
import com.wbyy.common.core.utils.ServletUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.core.utils.bean.BeanUtils;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.core.web.page.TableDataUtil;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.system.api.UserApi;
import com.wbyy.system.api.domain.ApiDept;
import com.wbyy.system.api.domain.ApiRole;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.api.model.LoginUser;
import com.wbyy.system.converter.UserConverter;
import com.wbyy.system.domain.SysDept;
import com.wbyy.system.domain.SysRole;
import com.wbyy.system.domain.SysUser;
import com.wbyy.system.service.ISysConfigService;
import com.wbyy.system.service.ISysPermissionService;
import com.wbyy.system.service.ISysUserService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.wbyy.common.security.utils.SecurityUtils.ADMIN_ROLE;

/**
 * 内部调用用户相关接口
 *
 * <AUTHOR>
 * @date 2025/6/30 16:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
public class UserApiImpl implements UserApi {

    private final ISysUserService userService;
    private final ISysPermissionService permissionService;
    private final ISysConfigService configService;

    /**
     * 根据用户 IDS 查询用户信息
     *
     * @param username
     * @param source
     * @return R<LoginUser>
     * <AUTHOR>
     * @date 2025/6/30 16:04
     */
    @Override
    @GetMapping("/info/{username}")
    @InnerAuth
    public R<LoginUser> getUserInfo(@PathVariable("username") String username,
                                    @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        String applicationCode;
        HttpServletRequest httpServletRequest = ServletUtils.getRequest();
        if (null!=httpServletRequest){
            applicationCode = httpServletRequest.getHeader(SecurityConstants.APPLICATION_CODE);
        } else {
            applicationCode = "";
        }
//        SysUser sysUser = userService.selectUserByUserName(username);
        SysUser sysUser = userService.selectUser(username);
        if (StringUtils.isNull(sysUser)) {
            return R.fail("用户名或密码错误");
        }
        sysUser.setRoles(Optional
                .of(sysUser.getRoles().stream()
                        .filter(r->applicationCode.equals(r.getApplicationCode()))
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>()));
        // 部门集合
        Set<Long> depts = permissionService.getDeptPermission(sysUser);
        // 角色集合
        Set<String> roles = permissionService.getRolePermissionByApplicationCode(sysUser,applicationCode);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermissionByApplication(sysUser,applicationCode);
        LoginUser sysUserVo = new LoginUser();
        ApiUser remoteUser = new ApiUser();
        BeanUtils.copyBeanProp(remoteUser, sysUser);
        List<ApiRole> remoteRoles = new ArrayList<>();
        List<ApiDept> remoteDepts = new ArrayList<>();
        for (SysRole role : sysUser.getRoles()) {
            ApiRole remoteRole = new ApiRole();
            BeanUtils.copyBeanProp(remoteRole, role);
            remoteRoles.add(remoteRole);
        }
        for (SysDept dept : sysUser.getDepts()) {
            ApiDept remoteDept = new ApiDept();
            BeanUtils.copyBeanProp(remoteDept, dept);
            remoteDepts.add(remoteDept);
        }
        remoteUser.setRoles(remoteRoles);
        remoteUser.setDepts(remoteDepts);
        boolean isAdmin;
        if (CollectionUtil.isEmpty(roles)) isAdmin = false;
        else isAdmin = roles.contains(ADMIN_ROLE);
        remoteUser.setCheckAdmin(isAdmin);
        sysUserVo.setSonDepts(permissionService.getSonDeptPermission(depts));
        sysUserVo.setDepts(depts);
        sysUserVo.setSysUser(remoteUser);
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        sysUserVo.setHireDate(sysUser.getHireDate());
        sysUserVo.setDepartureDate(sysUser.getDepartureDate());
        sysUserVo.setWeaverUserId(sysUser.getWeaverUserId());
        return R.ok(sysUserVo);
    }


    /**
     * 根据用户 IDS 查询用户信息
     *
     * @param userIds 用户ID 集合
     * @return 用户信息
     */
    @GetMapping("/info/by-user-ids")
    @InnerAuth
    @Override
    public R<List<ApiUser>> getUserInfoByUserIds(@RequestParam List<Long> userIds,
                                                 @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        return R.ok(userService.getUserInfo(userIds));
    }

    @Override
    @InnerAuth
    @PostMapping("/register")
    public R<Boolean> registerUserInfo(@RequestBody ApiUser apiUser,
                                       @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        String username = apiUser.getUserName();
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser")))) {
            return R.fail("当前系统没有开启注册功能！");
        }

        SysUser sysUser = new SysUser();
        BeanUtils.copyProperties(apiUser, sysUser);

        if (!userService.checkUserNameUnique(sysUser)) {
            return R.fail("保存用户'" + username + "'失败，注册账号已存在");
        }
        return R.ok(userService.registerUser(sysUser));
    }

    @Override
    @InnerAuth
    @PutMapping("/recordlogin")
    public R<Boolean> recordUserLogin(@RequestBody ApiUser sysUser,
                                      @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        userService.recordlogin(sysUser.getUserId(), sysUser.getLoginIp(), sysUser.getLoginDate());
        return R.ok();
    }

    @Override
    @InnerAuth
    @GetMapping("/info-list")
    public R<List<ApiUser>> infoList(@RequestParam("userIds") List<Long> userIds,
                                     @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        List<SysUser> list = userService.infoList(userIds);
        List<ApiUser> result = new ArrayList<>();
        for (SysUser sysUser : list) {
            ApiUser apiUser = new ApiUser();
            BeanUtils.copyProperties(sysUser, apiUser);
            result.add(apiUser);
        }
        return R.ok(result);
    }


    @GetMapping("/get-by-id")
    @InnerAuth
    @Override
    public R<ApiUser> getById(@RequestParam("userId") Long userId,
                              @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        SysUser user = userService.selectUserById(userId);
        if (null == user) {
            throw new ServiceException("获取用户信息失败");
        }
        ApiUser apiUser = new ApiUser();
        BeanUtil.copyProperties(user, apiUser);
        return R.ok(apiUser);
    }

    @Override
    @InnerAuth
    @GetMapping("/info-by-qw")
    public R<LoginUser> getUserInfoByQw(@RequestParam("qwUserId") String userId,
                                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        SysUser sysUser = userService.selectUserByQwUserId(userId);
        if (null == sysUser) {
            throw new ServiceException("获取用户信息失败");
        }
        // 部门集合
        Set<Long> depts = permissionService.getDeptPermission(sysUser);
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser);
        LoginUser sysUserVo = new LoginUser();
        ApiUser remoteUser = new ApiUser();
        BeanUtils.copyBeanProp(remoteUser, sysUser);
        List<ApiRole> remoteRoles = new ArrayList<>();
        List<ApiDept> remoteDepts = new ArrayList<>();
        for (SysRole role : sysUser.getRoles()) {
            ApiRole remoteRole = new ApiRole();
            BeanUtils.copyBeanProp(remoteRole, role);
            remoteRoles.add(remoteRole);
        }
        for (SysDept dept : sysUser.getDepts()) {
            ApiDept remoteDept = new ApiDept();
            BeanUtils.copyBeanProp(remoteDept, dept);
            remoteDepts.add(remoteDept);
        }
        remoteUser.setRoles(remoteRoles);
        remoteUser.setDepts(remoteDepts);
        boolean isAdmin;
        if (CollectionUtil.isEmpty(roles)) isAdmin = false;
        else isAdmin = roles.contains(ADMIN_ROLE);
        remoteUser.setCheckAdmin(isAdmin);
        sysUserVo.setSonDepts(permissionService.getSonDeptPermission(depts));
        sysUserVo.setDepts(depts);
        sysUserVo.setSysUser(remoteUser);
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        sysUserVo.setHireDate(sysUser.getHireDate());
        sysUserVo.setDepartureDate(sysUser.getDepartureDate());
        sysUserVo.setWeaverUserId(sysUser.getWeaverUserId());
        return R.ok(sysUserVo);
    }

    @Override
    @InnerAuth
    @GetMapping("/info-by-weaver-user-id")
    public R<ApiUser> infoByWeaverUserId(@RequestParam("weaverUserId") String weaverUserId,
                                         @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        SysUser one = userService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getWeaverUserId, weaverUserId), false);
        ApiUser user = BeanUtil.copyProperties(one, ApiUser.class);
        return R.ok(user);
    }

    @Override
    @InnerAuth
    @GetMapping("/get-all-user-by-dept")
    public R<List<ApiUser>> getAllUserByDept(@RequestParam("deptId") Long deptId,
                                             @RequestParam(value = "keyword", required = false) String keyword,
                                             @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        return R.ok(userService.getAllUserByDept(deptId, keyword));
    }

    @Override
    @InnerAuth
    @GetMapping("/get-all-user-by-dept/page")
    public TableDataInfo<ApiUser> pageAllUserByDept(@RequestParam("deptId") Long deptId,
                                                    @RequestParam(value = "keyword", required = false) String keyword,
                                                    @RequestParam(required = false) String pageNum,
                                                    @RequestParam(required = false) String pageSize,
                                                    @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        PageUtils.startPage("userId", "asc"); // 重新激活分页
        List<SysUser> allUserByDept = userService.pageAllUserByDept(deptId, keyword);
        return TableDataUtil.buildInfo(UserConverter.INSTANCT.user2ApiUser(allUserByDept), new PageInfo<>(allUserByDept).getTotal());
    }

    @Override
    @InnerAuth
    @GetMapping("/get-by-work-number")
    public R<ApiUser> getByWorkNumber(@RequestParam("workNumber") String workNumber,
                                      @RequestHeader(SecurityConstants.FROM_SOURCE) String source) {
        SysUser user = userService.getByWorkNumber(workNumber);
        return R.ok(BeanUtil.copyProperties(user, ApiUser.class));
    }
}
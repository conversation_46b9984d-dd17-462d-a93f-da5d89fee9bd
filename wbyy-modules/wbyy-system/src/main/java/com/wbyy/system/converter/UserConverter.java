package com.wbyy.system.converter;

import com.wbyy.system.api.domain.ApiDept;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.domain.SysDept;
import com.wbyy.system.domain.SysUser;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UserConverter {
    UserConverter INSTANCT = Mappers.getMapper(UserConverter.class);

    ApiDept dept2ApiDept(SysDept model);

    ApiUser user2ApiUser(SysUser model);

    List<ApiUser> user2ApiUser(List<SysUser> model);

}

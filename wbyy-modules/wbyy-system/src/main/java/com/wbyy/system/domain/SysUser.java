package com.wbyy.system.domain;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.common.core.annotation.Excel.ColumnType;
import com.wbyy.common.core.annotation.Excel.Type;
import com.wbyy.common.core.xss.Xss;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.orm.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_user")
public class SysUser extends BaseEntity {

    /**
     * 用户ID
     */
    @Excel(name = "用户序号", type = Type.EXPORT, cellType = ColumnType.NUMERIC, prompt = "用户编号")
    @TableId(value = "user_id")
    private Long userId;

    /**
     * 用户账号
     */
    @Excel(name = "登录名称")
    private String userName;

    /**
     * 真实姓名
     */
    @Excel(name = "真实姓名")
    private String realName;

    /**
     * 工号
     */
    @Excel(name = "工号")
    private String workNumber;

    /**
     * 用户邮箱
     */
    @Excel(name = "用户邮箱")
    private String email;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码", cellType = ColumnType.TEXT)
    private String phone;

    /**
     * 用户性别
     */
    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    private Integer sex;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 密码
     */
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private Integer status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 最后登录IP
     */
    @Excel(name = "最后登录IP", type = Type.EXPORT)
    private String loginIp;

    /**
     * 最后登录时间
     */
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
    private Date loginDate;

    private String remark;

    private String qwUserId;

    /**
     * 泛微userid
     */
    private String weaverUserId;

    @TableField(exist = false)
    private Long deptId;

    /**
     * 部门对象
     */
    @TableField(exist = false)
    private List<SysDept> depts;

    /**
     * 角色对象
     */
    @TableField(exist = false)
    private List<SysRole> roles;

    /**
     * 部门组
     */
    @TableField(exist = false)
    private Long[] deptIds;

    /**
     * 角色组
     */
    @TableField(exist = false)
    private Long[] roleIds;

    /**
     * 岗位组
     */
    @TableField(exist = false)
    private Long[] postIds;
    @TableField(exist = false)
    private List<SysPost> posts;

    /**
     * 角色ID
     */
    @TableField(exist = false)
    private Long roleId;

    @TableField(exist = false)
    private String deptNameStr;

    @TableField(exist = false)
    private String centerDeptName;

    @Schema(description = "查询条件，关键字（姓名、ID、工号）")
    @TableField(exist = false)
    private String keyword;

    @TableField(exist = false)
    @Schema(description = "部门全路径")
    private String deptFullName;

    @TableField(exist = false)
    private Long postId;
    @TableField(exist = false)
    private String postName;

    @Schema(description = "入职时间")
    private Date hireDate;

    @Schema(description = "离职时间")
    private Date departureDate;

    @Schema(description = "密码修改时间")
    private Date updatePwdTime;

    @Schema(description = "是否需要修改密码")
    @TableField(exist = false)
    private boolean hasToUpPwd;

    public String getDeptNameStr() {
        if (CollectionUtil.isEmpty(this.depts)) {
            return null;
        }
        return this.depts.stream().map(SysDept::getDeptName).collect(Collectors.joining(","));
    }

    public SysUser() {

    }

    public SysUser(Long user_id) {
        this.userId = user_id;
    }

    public boolean isAdmin() {
        return SecurityUtils.isAdmin();
    }

    @Xss(message = "用户昵称不能包含脚本字符")
    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    public String getRealName() {
        return realName;
    }

    @Xss(message = "用户账号不能包含脚本字符")
    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    public String getUserName() {
        return userName;
    }

    @Xss(message = "用户工号不能包含脚本字符")
//    @NotBlank(message = "用户工号不能为空")
//    @Size(min = 0, max = 8, message = "用户工号长度不能超过8个字符")
    public String getWorkNumber() {
        return workNumber;
    }

    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    public String getEmail() {
        return email;
    }

    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    public String getPhone() {
        return phone;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("userId", getUserId())
                .append("userName", getUserName())
                .append("nickName", getRealName())
                .append("email", getEmail())
                .append("phonenumber", getPhone())
                .append("sex", getSex())
                .append("avatar", getAvatar())
                .append("password", getPassword())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("loginIp", getLoginIp())
                .append("loginDate", getLoginDate())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("dept", getDepts())
                .toString();
    }
}

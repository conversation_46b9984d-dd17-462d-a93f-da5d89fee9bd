package com.wbyy.system.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.web.domain.AjaxResult;
import com.wbyy.common.qyweixin.config.WxCpConfiguration;
import com.wbyy.system.model.AsyncDeptDto;
import com.wbyy.system.model.AsyncUserDto;
import com.wbyy.system.service.AddressBookService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import me.chanjar.weixin.cp.util.crypto.WxCpCryptUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("address-book")
@Slf4j
public class AddressBookController {
    @Resource(name = "qWAddressBookService")
    private AddressBookService qWAddressBookService;

    @Resource(name = "weaverAddressBookService")
    private AddressBookService weaverAddressBookService;

    @PostMapping("async-user")
    public R<Boolean> asyncUser(@RequestParam("type")String type,@RequestBody List<AsyncUserDto> dto){
        log.info("收到同步用户信息，同步类型【{}】，数据：\n{}",type,JSON.toJSONString(dto));
        weaverAddressBookService.asyncUser(dto);
        return R.ok(true);
    }

    @PostMapping("async-dept")
    public R<Boolean> asyncDept(@RequestParam("type")String type,@RequestBody List<AsyncDeptDto> dto){
        log.info("收到同步部门信息，同步类型【{}】，数据：\n{}",type,JSON.toJSONString(dto));
        weaverAddressBookService.asyncDept(dto);
        return R.ok(true);
    }

    @PostMapping("async-userid")
    public R<?> asyncUserid(){
        weaverAddressBookService.asyncUserid();
        qWAddressBookService.asyncUserid();
        return R.ok();
    }

    @PostMapping("async-post")
    public R<?> asyncPost(){
        weaverAddressBookService.asyncPost();
        return R.ok();
    }

    @GetMapping("async-weaver")
    public AjaxResult asyncWeaver(){
        weaverAddressBookService.async();
        return AjaxResult.success();
    }

    @GetMapping("async-qw")
    public AjaxResult asyncQw(){
        qWAddressBookService.async();
        return AjaxResult.success();
    }

    @GetMapping(produces = "text/plain;charset=utf-8")
    public String authGet(@PathVariable String agentCode,
                          @RequestParam(name = "msg_signature", required = false) String signature,
                          @RequestParam(name = "timestamp", required = false) String timestamp,
                          @RequestParam(name = "nonce", required = false) String nonce,
                          @RequestParam(name = "echostr", required = false) String echostr) {
        log.info("\n接收到来自微信服务器的认证消息：signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}]",
                signature, timestamp, nonce, echostr);

        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }

        final WxCpService wxCpService = WxCpConfiguration.getCpService(agentCode);
        if (wxCpService == null) {
            throw new IllegalArgumentException(String.format("未找到对应agentCode=[%s]的配置，请核实！", agentCode));
        }

        if (wxCpService.checkSignature(signature, timestamp, nonce, echostr)) {
            return new WxCpCryptUtil(wxCpService.getWxCpConfigStorage()).decrypt(echostr);
        }

        return "非法请求";
    }

    @PostMapping(produces = "application/xml; charset=UTF-8")
    public String post(@PathVariable String agentCode,
                       @RequestBody String requestBody,
                       @RequestParam("msg_signature") String signature,
                       @RequestParam("timestamp") String timestamp,
                       @RequestParam("nonce") String nonce) {
        log.info("\n接收微信请求：[signature=[{}], timestamp=[{}], nonce=[{}], requestBody=[\n{}\n] ",
                signature, timestamp, nonce, requestBody);

        final WxCpService wxCpService = WxCpConfiguration.getCpService(agentCode);
        WxCpXmlMessage inMessage = WxCpXmlMessage.fromEncryptedXml(requestBody, wxCpService.getWxCpConfigStorage(),
                timestamp, nonce, signature);
        log.debug("\n消息解密后内容为：\n{} ", JSON.toJSONString(inMessage));
        WxCpXmlOutMessage outMessage = this.route(agentCode, inMessage);
        if (outMessage == null) {
            return "";
        }

        String out = outMessage.toEncryptedXml(wxCpService.getWxCpConfigStorage());
        log.debug("\n组装回复信息：{}", out);
        return out;
    }

    private WxCpXmlOutMessage route(String agentCode, WxCpXmlMessage message) {
        try {
            return WxCpConfiguration.getRouters().get(agentCode).route(message);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }
}

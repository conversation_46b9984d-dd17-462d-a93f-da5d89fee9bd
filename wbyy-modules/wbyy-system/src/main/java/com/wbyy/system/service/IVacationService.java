package com.wbyy.system.service;

import java.util.Date;
import java.util.List;
import com.wbyy.system.domain.Vacation;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 请假Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IVacationService  extends IService<Vacation> {
    /**
     * 查询请假
     *
     * @param id 请假主键
     * @return 请假
     */
    Vacation selectById(Long id);

    /**
     * 查询请假列表
     *
     * @param vacation 请假
     * @return 请假集合
     */
    List<Vacation> selectList(Vacation vacation);

    /**
     * 新增请假
     *
     * @param vacation 请假
     * @return 结果
     */
    boolean insert(Vacation vacation);

    /**
     * 修改请假
     *
     * @param vacation 请假
     * @return 结果
     */
    boolean update(Vacation vacation);

    /**
     * 批量删除请假
     *
     * @param ids 需要删除的请假主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    void asyncAll(String startDate,String endDate);

    void asyncPreDay();

    /**
     * @Description: 用户请假数据
     * @param userId
     * @param startDate
     * @param endDate
     * @return: List<Vacation>
     * @author: 王金都
     * @Date: 2025/6/16 10:05
     */
    List<Vacation> listUserVacation(Long userId, Date startDate,Date endDate);

    /**
     * @Description: 用户集合的请假数据
     * @param userIds
     * @param startDate
     * @param endDate
     * @return: List<Vacation>
     * @author: 王金都
     * @Date: 2025/6/16 18:21
     */
    List<Vacation> listUsersVacation(List<Long> userIds, Date startDate,Date endDate);
}

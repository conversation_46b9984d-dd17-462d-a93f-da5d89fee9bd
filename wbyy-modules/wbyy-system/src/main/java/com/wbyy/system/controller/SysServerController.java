package com.wbyy.system.controller;

import cn.hutool.core.date.DateUtil;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.system.api.model.LoginUser;
import com.wbyy.system.domain.SysUser;
import com.wbyy.system.domain.vo.NowVo;
import com.wbyy.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: 王金都
 * @date: 2025/6/6 8:51
 */
@RestController
@RequestMapping("/sys-server")
@RequiredArgsConstructor
public class SysServerController {

    private final ISysUserService sysUserService;

    @GetMapping("/now")
    public R<NowVo> now(){
        NowVo nowVo=new NowVo();
        nowVo.setNow(DateUtil.now());
        Long userId = SecurityUtils.getUserId();
        SysUser byId = sysUserService.getById(userId);
        nowVo.setHireDate(DateUtil.format(byId.getHireDate(),"yyyy-MM-dd HH:mm:ss"));
        nowVo.setDepartureDate(DateUtil.format(byId.getDepartureDate(),"yyyy-MM-dd HH:mm:ss"));
        return R.ok(nowVo);
    }
}

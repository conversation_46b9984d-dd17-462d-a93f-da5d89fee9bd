package com.wbyy.system.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseEntity;

/**
 * 应用管理对象 application
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
@Data
@Schema(description = "应用管理实体类")
@EqualsAndHashCode(callSuper = true)
public class Application extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** id */
    @Schema(description = "id")
    private Long id;

    /** 应用名称 */
    @Excel(name = "应用名称")
    @Schema(description = "应用名称")
    @NotBlank(message = "应用名称不能为空")
    private String name;

    /** 应用简称 */
    @Excel(name = "应用简称")
    @Schema(description = "应用简称")
    @NotBlank(message = "应用简称不能为空")
    private String shortName;

    /** 应用编码 */
    @Excel(name = "应用编码")
    @Schema(description = "应用编码")
    @NotBlank(message = "应用编码不能为空")
    private String code;

    /** 备注 */
    @Excel(name = "备注")
    @Schema(description = "备注")
    @Size(max = 255, message = "备注不能超过 255 个字符")
    private String remark;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    @Schema(description = "显示顺序")
    @NotNull(message = "显示顺序不能为空")
    private Integer orderSort;

    /** 应用状态（0正常 1停用） */
    @Excel(name = "应用状态", readConverterExp = "0=正常,1=停用")
    @Schema(description = "应用状态（0正常 1停用）")
    private Integer status;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("shortName", getShortName())
            .append("code", getCode())
            .append("remark", getRemark())
            .append("orderSort", getOrderSort())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}

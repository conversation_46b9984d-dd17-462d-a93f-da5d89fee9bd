package com.wbyy.system.controller;

import com.wbyy.common.core.constant.CacheConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.ip.IpUtils;
import com.wbyy.common.core.utils.security.RSAUtils;
import com.wbyy.common.redis.service.RedisService;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/15 14:53
 */
@RestController
@RequestMapping("/security")
@RequiredArgsConstructor
public class SecurityController {

    private final RedisService redisService;
    private final RedissonClient redissonClient;

    @GetMapping("/public-key")
    public R<Map<String, String>> publicKey() {
        String rateLimitKey = String.format(CacheConstants.RATE_LIMIT_PUBLIC_KEY,IpUtils.getHostIp());
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(rateLimitKey);
        rateLimiter.trySetRate(RateType.OVERALL, 20, 60, RateIntervalUnit.SECONDS);

        if (!rateLimiter.tryAcquire()) {
            return R.fail("请求过于频繁，请稍后再试");
        }

        RSAUtils.KeyPairInfo keyPairInfo = RSAUtils.generateKeyPairInfoAndSafeGet();
        String keyId = UUID.randomUUID().toString();
        String cacheKey = String.format(CacheConstants.RSA_PRIVATE_KEY, keyId);
        redisService.setCacheObject(cacheKey, keyPairInfo.getPrivateKey(), 5L, TimeUnit.MINUTES);

        Map<String, String> result = new HashMap<>();
        result.put("keyId", keyId);
        result.put("publicKey", keyPairInfo.getPublicKey());
        return R.ok(result);
    }


}

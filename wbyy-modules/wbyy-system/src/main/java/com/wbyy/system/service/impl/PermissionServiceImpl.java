package com.wbyy.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.ServletUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.core.utils.pool.FutureUtils;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.system.api.domain.ApiPermissionSql;
import com.wbyy.system.api.model.LoginUser;
import com.wbyy.system.domain.SysDept;
import com.wbyy.system.domain.SysRole;
import com.wbyy.system.domain.SysRoleDept;
import com.wbyy.system.mapper.PermissionMapper;
import com.wbyy.system.mapper.SysDeptMapper;
import com.wbyy.system.mapper.SysRoleDeptMapper;
import com.wbyy.system.service.IPermissionService;
import com.wbyy.system.service.ISysRoleService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.wbyy.common.core.constant.ScopeConst.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements IPermissionService {

    private final static String POOL_NAME = "permissionQueryPool";

    private final PermissionMapper permissionMapper;
    private final ISysRoleService sysRoleService;
    private final SysRoleDeptMapper sysRoleDeptMapper;
    private final SysDeptMapper sysDeptMapper;

    @Override
    public String getPermissionSql(ApiPermissionSql permissionSqlDTO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getSysUser().getUserId();
        Set<Long> deptIds = loginUser.getDepts();

        HttpServletRequest httpServletRequest = ServletUtils.getRequest();

        String applicationCode;
        if (StringUtils.isNotNull(httpServletRequest)) {
            Map<String, String> headers = ServletUtils.getHeaders(httpServletRequest);
            applicationCode = headers.get(SecurityConstants.APPLICATION_CODE);
        } else {
            throw new ServiceException("权限有误，请联系管理员");
        }
        if (StrUtil.isEmpty(applicationCode)){
            throw new ServiceException("权限有误，请联系管理员");
        }

        // 根据应用编码查询角色列表
        List<SysRole> roles = sysRoleService.selectRoleIdsByApplicationCode(userId, applicationCode);
        if (CollUtil.isEmpty(roles)) {
            throw new ServiceException("权限有误，请联系管理员");
        }

        // 所有角色的数据范围
        Map<Long, Integer> roleIdAndDataScopeMap = roles.stream().collect(Collectors.toMap(SysRole::getRoleId, SysRole::getDataScope));

        List<String> permissionSqlList = new ArrayList<>();
        if (roleIdAndDataScopeMap.size() > 2) {
            // 如果角色数量大于2，并发查询角色数据权限
            List<Future<String>> futures = new ArrayList<>();
            roleIdAndDataScopeMap.forEach((roleId, dataScope) -> {
                switch (dataScope) {
                    case DATA_SCOPE_ALL:
                        futures.add(FutureUtils.taskAsync(POOL_NAME, () ->
                                getPermissionSqlByDataScope(userId, roleId, deptIds, dataScope, permissionSqlDTO)));
                        break;
                    case DATA_SCOPE_CUSTOM:
                        // 如果有自定义权限，则返回自定义权限
                        futures.add(FutureUtils.taskAsync(POOL_NAME, () ->
                                getPermissionSqlByDataScope(userId, roleId, deptIds, dataScope, permissionSqlDTO)));
                        break;
                    case DATA_SCOPE_DEPT:
                        // 如果有部门权限，则返回部门权限
                        futures.add(FutureUtils.taskAsync(POOL_NAME, () ->
                                getPermissionSqlByDataScope(userId, roleId, deptIds, dataScope, permissionSqlDTO)));
                        break;
                    case DATA_SCOPE_DEPT_AND_CHILD:
                        // 如果有部门及以下权限，则返回部门及以下权限
                        futures.add(FutureUtils.taskAsync(POOL_NAME, () ->
                                getPermissionSqlByDataScope(userId, roleId, deptIds, dataScope, permissionSqlDTO)));
                        break;
                    case DATA_SCOPE_SELF:
                        // 如果有个人权限，则返回个人权限
                        futures.add(FutureUtils.taskAsync(POOL_NAME, () ->
                                getPermissionSqlByDataScope(userId, roleId, deptIds, dataScope, permissionSqlDTO)));
                        break;
                    default:
                        // 如果没有权限，则返回空
                        throw new ServiceException(StrUtil.format("【{}】数据权限配置错误，请联系管理员", roleId));
                }
            });
            if (CollUtil.isNotEmpty(futures)) {
                futures.forEach(future -> {
                    try {
                        permissionSqlList.add(future.get());
                    } catch (Exception e) {
                        Thread.currentThread().interrupt();
                        log.error("【获取个人角色权限】异常。", e);
                        throw new ServiceException("获取个人角色权限异常，请稍后重试");
                    }
                });
            }
        } else {
            roleIdAndDataScopeMap.forEach((roleId, dataScope) ->
                    permissionSqlList.add(getPermissionSqlByDataScope(userId, roleId, deptIds, dataScope, permissionSqlDTO)));
        }

        // 合并所有SQL条件（示例）
        String sqlStr = permissionSqlList.stream().filter(Objects::nonNull)
                .collect(Collectors.joining(" OR "));
        return " AND ( " + sqlStr + " )";
    }

    /**
     * 根据数据权限 查询权限SQL
     *
     * @param userId           用户ID
     * @param roleId           角色ID
     * @param deptIds          部门ID集合
     * @param dataScope        数据权限
     * @param permissionSqlDTO
     * @return 权限SQL
     */
    public String getPermissionSqlByDataScope(Long userId, Long roleId,
                                              Set<Long> deptIds, int dataScope, ApiPermissionSql permissionSqlDTO) {

        // 1 全部数据权限
        if (dataScope == DATA_SCOPE_ALL) {
            return " 1=1 ";
        }

        String deptAlias = permissionSqlDTO.getDeptAlias();
        String userAlias = permissionSqlDTO.getUserAlias();

        // 2 自定数据权限
        if (dataScope == DATA_SCOPE_CUSTOM) {
            // 获取角色对定义的部门权限
            LambdaQueryWrapper<SysRoleDept> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SysRoleDept::getRoleId, roleId);
            List<SysRoleDept> sysRoleDepts = sysRoleDeptMapper.selectList(wrapper);
            if (CollUtil.isEmpty(sysRoleDepts)) {
                return " 1=0 ";
            }
            if (StrUtil.isNotEmpty(deptAlias)) {
                return StrUtil.format(" {}.dept_id in ( " + sysRoleDepts.stream().map(SysRoleDept::getDeptId).map(String::valueOf).collect(Collectors.joining(",")) + " ) ", deptAlias);
            } else {
                return " dept_id in ( " + sysRoleDepts.stream().map(SysRoleDept::getDeptId).map(String::valueOf).collect(Collectors.joining(",")) + " ) ";
            }

        }

        // 3 部门数据权限
        if (dataScope == DATA_SCOPE_DEPT) {
            // 获取角色对定义的部门权限
            if (CollUtil.isEmpty(deptIds)) {
                throw new ServiceException("当前用户没有所属部门，请联系管理员");
            }
            if (StrUtil.isNotEmpty(deptAlias)) {
                return StrUtil.format(" {}.dept_id in ( " + StrUtil.join(",", deptIds) + " ) ", deptAlias);
            } else {
                return " dept_id in ( " + StrUtil.join(",", deptIds) + " ) ";
            }

        }

        // 4 部门及以下数据权限
        if (dataScope == DATA_SCOPE_DEPT_AND_CHILD) {
            if (CollUtil.isEmpty(deptIds)) {
                throw new ServiceException("当前用户没有所属部门，请联系管理员");
            }
            LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery();
            wrapper.select(SysDept::getDeptId)
                    .and(w -> {
                        for (Long deptId : deptIds) {
                            w.or().like(SysDept::getAncestors, deptId);
                        }
                    });
            List<SysDept> sysDepts = sysDeptMapper.selectList(wrapper);
            Set<Long> allDeptIds = sysDepts.stream().map(SysDept::getDeptId).collect(Collectors.toSet());
            if (StrUtil.isNotEmpty(deptAlias)) {
                return StrUtil.format(" {}.dept_id in (" + StrUtil.join(",", allDeptIds) + ") ", deptAlias);
            } else {
                return " dept_id in (" + StrUtil.join(",", allDeptIds) + ") ";
            }

        }

        // 5 仅本人数据权限
        if (dataScope == DATA_SCOPE_SELF) {
            if (StringUtils.isNotEmpty(userAlias)) {
                return StrUtil.format(" {}.user_id = {} ", userAlias, userId);
            } else {
                return " user_id = " + userId;
            }
        }

        return " 1=0 ";
    }
}

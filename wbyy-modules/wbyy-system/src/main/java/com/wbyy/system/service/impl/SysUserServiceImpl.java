package com.wbyy.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.constant.UserConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.SpringUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.core.utils.bean.BeanValidators;
import com.wbyy.common.datascope.annotation.SysDataScope;
import com.wbyy.common.redis.service.RedisService;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.log.model.UserInfo;
import com.wbyy.log.service.UserInfoService;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.converter.UserConverter;
import com.wbyy.system.domain.*;
import com.wbyy.system.mapper.*;
import com.wbyy.system.model.UpdateQwUserIdDto;
import com.wbyy.system.service.ISysConfigService;
import com.wbyy.system.service.ISysDeptService;
import com.wbyy.system.service.ISysPostService;
import com.wbyy.system.service.ISysUserService;
import com.wbyy.weaver.api.model.EmployeeData;
import jakarta.validation.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.wbyy.common.core.constant.Constants.NO_DELETE;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService, UserInfoService {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);
    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    protected Validator validator;

    @Autowired
    private SysUserDeptMapper userDeptMapper;
    @Autowired
    private RedisService redisService;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @SysDataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user) {
        List<SysUser> list = userMapper.selectUserList(user);
        Set<Long> deptIds = list.stream().map(item -> {
            if (CollectionUtil.isEmpty(item.getDepts())) return null;
            return item.getDepts().get(0).getDeptId();
        }).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SysDept> centerDepts = deptService.listCenterDept(deptIds);

        if (CollUtil.isEmpty(list)) {
            return List.of();
        }

        List<Long> userIds = list.stream().map(SysUser::getUserId).toList();
        // 翻译岗位名称
        List<SysPost> sysPosts = postService.selectPostListByUserIds(userIds);
        Map<Long, List<SysPost>> userPostMap;
        if (CollUtil.isNotEmpty(sysPosts)) {
            userPostMap = sysPosts.stream().collect(Collectors.groupingBy(SysPost::getUserId));
        } else {
            userPostMap = new HashMap<>();
        }

        list.forEach(item -> {
            item.setPostIds(
                    CollUtil.isNotEmpty(userPostMap.get(item.getUserId()))
                            ? userPostMap.get(item.getUserId()).stream().map(SysPost::getPostId).toArray(Long[]::new)
                            : null);
            item.setPosts(userPostMap.get(item.getUserId()));

            if (CollectionUtil.isNotEmpty(item.getDepts())) {
                Long deptId = item.getDepts().get(0).getDeptId();
                centerDepts.stream().filter(center -> center.getOriginalDeptId().equals(deptId))
                        .findFirst().ifPresent(center -> item.setCenterDeptName(center.getDeptName()));
            }
        });
        return list;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @SysDataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user) {
        return userMapper.selectAllocatedList(user);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @SysDataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUnallocatedList(SysUser user) {
        return userMapper.selectUnallocatedList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }

    @Override
    public SysUser selectUser(String value) {
        return userMapper.selectUser(value);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }

    @Override
    public SysUser selectUserByIdByApplication(Long userId, String applicationCode) {
        return userMapper.selectUserByIdByApplication(userId,applicationCode);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user) {
        long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkUserNameUnique(user.getUserName());
        if (StringUtils.isNotNull(info) && !info.getUserId().equals(userId)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(SysUser user) {
        long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhone());
        if (StringUtils.isNotNull(info) && !info.getUserId().equals(userId)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkEmailUnique(SysUser user) {
        long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && !info.getUserId().equals(userId)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (null != user && user.getUserId() == 1L) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!SecurityUtils.isAdmin()) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUser user) {
        // 新增用户信息
        int rows = userMapper.insert(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        // 新增用户与部门关联
        insertUserDept(user);
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        return userMapper.insert(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(SysUser user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);
        // 删除用户与部门关联
        userDeptMapper.deleteUserDeptByUserId(userId);
        // 新增用户与部门关联
        insertUserDept(user);
        return userMapper.updateById(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        return userMapper.updateById(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean updateUserProfile(SysUser user) {
        return userMapper.updateById(user) > 0;
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        return userMapper.initUserPwd(user.getUserId(),user.getPassword());
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    public void insertUserDept(SysUser user) {
        this.insertUserDept(user.getUserId(), user.getDeptIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>();
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    public void insertUserDept(Long userId, Long[] deptIds) {
        if (StringUtils.isNotEmpty(deptIds)) {
            // 新增用户与部门关联
            List<SysUserDept> list = new ArrayList<>();
            for (Long deptId : deptIds) {
                SysUserDept ud = new SysUserDept();
                ud.setDeptId(deptId);
                ud.setUserId(userId);
                list.add(ud);
            }
            userDeptMapper.batchUserDept(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.batchUserRole(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        // 删除用户与部门关联
        userDeptMapper.deleteUserDept(userIds);
        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operId          操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, Long operId) {
        if (StringUtils.isNull(userList) || userList.isEmpty()) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SysUser user : userList) {
            try {
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u)) {
                    BeanValidators.validateWithException(validator, user);

                    deptService.checkDeptDataScope(user.getDeptIds());
                    String password = configService.selectConfigByKey("sys.user.initPassword");
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operId);
                    userMapper.insert(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(u);
                    checkUserDataScope(u.getUserId());
                    // 多部门
                    deptService.checkDeptDataScope(user.getDeptIds());
                    user.setUserId(u.getUserId());
                    user.setUpdateBy(operId);
                    userMapper.updateById(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public void deleteFromRemoteByUserIdSet(Set<Long> userIdSet) {
        Long[] userIds = userIdSet.stream().filter(item -> 1L != item).toArray(Long[]::new);
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        // 删除用户与部门关联
        userDeptMapper.deleteUserDept(userIds);
        userMapper.deleteUserByIds(userIds);
    }

    public void saveBatchFromRemote(List<SysUser> list) {
        userMapper.saveBatchFromRemote(list);
    }

    public void updateBatchFromRemote(List<SysUser> list) {
        userMapper.updateBatchFromRemote(list);
    }

    public void recordlogin(Long userId, String loginIp, Date loginDate) {
        SysUser byId = this.getById(userId);
        if (null != byId) {
            byId.setLoginDate(loginDate);
            byId.setLoginIp(loginIp);
            this.updateById(byId);
        }
    }

    @Override
    public List<SysUser> listByWorkNumbers(Set<String> workNums) {
        if (CollectionUtil.isEmpty(workNums)) return new ArrayList<>();
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUser::getWorkNumber, workNums);
        queryWrapper.eq(SysUser::getDelFlag, NO_DELETE);
        return this.list(queryWrapper);
    }

    @Override
    public List<ApiUser> getUserInfo(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds))
            return List.of();
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUser::getUserId, userIds);
        List<SysUser> list = this.list(queryWrapper);

        return BeanUtil.copyToList(list, ApiUser.class);
    }

    @Override
    public UserInfo getUserInfo() {
        UserInfo info = new UserInfo();
        Long userId = SecurityUtils.getUserId();
        info.setUserId(userId.toString());
        String username = SecurityUtils.getUsername();
        info.setUserName(username);
        Set<Long> depts = SecurityUtils.getLoginUser().getDepts();
        depts.stream().findFirst().ifPresent(dept -> {
            SysDept byId = deptService.getById(dept);
            if (null != byId) {
                info.setDeptId(dept.toString());
                info.setDeptName(byId.getDeptName());
            }
        });
        return info;
    }

    @Override
    public void updateWeaverUserId(List<EmployeeData> employees) {
        if (CollectionUtil.isEmpty(employees)) return;
        this.baseMapper.updateWeaverUserId(employees);
    }

    public void updateQwUserId(List<UpdateQwUserIdDto> qwUserIds) {
        if (CollectionUtil.isEmpty(qwUserIds)) return;
        this.baseMapper.updateQwUserId(qwUserIds);
    }

    @Override
    public List<SysUser> infoList(List<Long> userIds) {
        if (CollectionUtil.isEmpty(userIds)) return new ArrayList<>();
        List<SysUser> list = this.listByIds(userIds);
        if (CollectionUtil.isEmpty(list)) return new ArrayList<>();
        // 组装部门
        List<SysUserDept> userDepts = deptService.listByUserIds(userIds);
        // 组长岗位
        List<SysPost> userPosts = postMapper.selectPostListByUserIds(userIds);
        list.forEach(item -> {
            Long userId = item.getUserId();
            userDepts.stream().filter(dept -> userId.equals(dept.getUserId()))
                    .findFirst().ifPresent(dept -> {
                        item.setDeptId(dept.getDeptId());
                    });
            userPosts.stream().filter(post -> userId.equals(post.getUserId()))
                    .findFirst().ifPresent(post -> {
                        item.setPostId(post.getPostId());
                        item.setPostName(post.getPostName());
                    });
        });
        return list;
    }

    /**
     * 通过企微用户id查询用户
     *
     * @param qwUserId 企微用户id
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByQwUserId(String qwUserId) {
        return baseMapper.selectUserByQwUserId(qwUserId);
    }


    @Transactional(rollbackFor = Exception.class)
    public boolean asyncUserHireData() {
        List<SysUser> users = this.list();
        for (SysUser user : users) {
            if (null != user.getHireDate()) {
                redisService.setCacheObject(String.format(UserConstants.HIRE_TIME_CACHE_KEY,
                                user.getUserId().toString()),
                        DateUtil.formatDateTime(user.getHireDate()));
            }
            if (null != user.getDepartureDate()) {
                redisService.setCacheObject(String.format(UserConstants.DEPARTURE_TIME_CACHE_KEY,
                                user.getUserId().toString()),
                        DateUtil.formatDateTime(user.getHireDate()));
            }
        }
        return true;
    }

    @Override
    public List<ApiUser> getAllUserByDept(Long deptId, String keyword) {
        return UserConverter.INSTANCT.user2ApiUser(this.pageAllUserByDept(deptId, keyword));
    }

    @Override
    public List<SysUser> pageAllUserByDept(Long deptId, String keyword) {
        SysUser params = new SysUser();
        params.setDeptId(deptId);
        params.setKeyword(keyword);
        List<SysUser> list = userMapper.selectUserList(params);
        Set<Long> deptIds = list.stream().map(item -> {
            if (CollectionUtil.isEmpty(item.getDepts())) return null;
            return item.getDepts().get(0).getDeptId();
        }).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SysDept> centerDepts = deptService.listCenterDept(deptIds);

        if (CollUtil.isEmpty(list)) {
            return List.of();
        }

        List<Long> userIds = list.stream().map(SysUser::getUserId).toList();
        // 翻译岗位名称
        List<SysPost> sysPosts = postService.selectPostListByUserIds(userIds);
        Map<Long, List<SysPost>> userPostMap;
        if (CollUtil.isNotEmpty(sysPosts)) {
            userPostMap = sysPosts.stream().collect(Collectors.groupingBy(SysPost::getUserId));
        } else {
            userPostMap = new HashMap<>();
        }

        list.forEach(item -> {
            item.setPostIds(
                    CollUtil.isNotEmpty(userPostMap.get(item.getUserId()))
                            ? userPostMap.get(item.getUserId()).stream().map(SysPost::getPostId).toArray(Long[]::new)
                            : null);
            item.setPosts(userPostMap.get(item.getUserId()));

            if (CollectionUtil.isNotEmpty(item.getDepts())) {
                Long firstdeptId = item.getDepts().get(0).getDeptId();
                centerDepts.stream().filter(center ->
                                center.getOriginalDeptId().equals(firstdeptId))
                        .findFirst().ifPresent(center ->
                                item.setCenterDeptName(center.getDeptName()));
            }
        });
        return list;
    }

    @Override
    public SysUser getByWorkNumber(String workNumber) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getWorkNumber,workNumber);
        return this.getOne(queryWrapper,false);
    }
}

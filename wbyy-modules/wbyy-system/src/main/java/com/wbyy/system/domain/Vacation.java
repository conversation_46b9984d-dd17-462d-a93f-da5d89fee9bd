package com.wbyy.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;

/**
 * 请假对象 vacation
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "vacation", autoResultMap = true)
@Schema(description = "请假实体类")
@EqualsAndHashCode(callSuper = true)
public class Vacation extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /** 假期名称 */
    @Excel(name = "假期名称")
    @Schema(description = "假期名称")
    @Size(max = 100, message = "假期名称不能超过 100 个字符")
    private String name;

    /** 假期开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "假期开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "假期开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "假期开始时间不能为空")
    private Date startTime;

    /** 假期结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "假期结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "假期结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "假期结束时间不能为空")
    private Date endTime;

    /** 假期id（泛微） */
    @Excel(name = "假期id", readConverterExp = "泛=微")
    @Schema(description = "假期id（泛微）")
    @Size(max = 255, message = "假期id不能超过 255 个字符")
    private String vacationId;

    /** 请假时长（小时） */
    @Schema(description = "请假时长（小时）")
    private BigDecimal timeLength;

    // user_id-name-start_time-end_time
    private String checkKey;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("name", getName())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("vacationId", getVacationId())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}

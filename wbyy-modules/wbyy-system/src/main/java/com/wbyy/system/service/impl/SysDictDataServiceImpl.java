package com.wbyy.system.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.constant.CacheConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.redis.service.CommonCacheService;
import com.wbyy.common.redis.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wbyy.system.domain.SysDictData;
import com.wbyy.system.mapper.SysDictDataMapper;
import com.wbyy.system.service.ISysDictDataService;

/**
 * 字典 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysDictDataServiceImpl extends ServiceImpl<SysDictDataMapper, SysDictData> implements ISysDictDataService
{
    @Autowired
    private SysDictDataMapper dictDataMapper;
    @Autowired
    private CommonCacheService<SysDictData> commonCacheService;

    /**
     * 根据条件分页查询字典数据
     * 
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictData> selectDictDataList(SysDictData dictData)
    {
        return dictDataMapper.selectDictDataList(dictData);
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     * 
     * @param dictType 字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @Override
    public String selectDictLabel(String dictType, String dictValue)
    {
        return dictDataMapper.selectDictLabel(dictType, dictValue);
    }

    /**
     * 根据字典数据ID查询信息
     * 
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictData selectDictDataById(Long dictCode)
    {
        return dictDataMapper.selectDictDataById(dictCode);
    }

    /**
     * 批量删除字典数据信息
     * 
     * @param dictCodes 需要删除的字典数据ID
     */
    @Override
    public void deleteDictDataByIds(Long[] dictCodes)
    {
        for (Long dictCode : dictCodes)
        {
            SysDictData data = selectDictDataById(dictCode);
            dictDataMapper.deleteDictDataById(dictCode);
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());

            commonCacheService.setDictCache(CacheConstants.SYS_DICT_KEY, data.getDictType(), dictDatas);
        }
    }

    /**
     * 新增保存字典数据信息
     * 
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    public int insertDictData(SysDictData data)
    {
        // 数据唯一校验
        checkDictDataByDictType(data);
        int row = dictDataMapper.insert(data);
        if (row > 0)
        {
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            commonCacheService.setDictCache(CacheConstants.SYS_DICT_KEY, data.getDictType(), dictDatas);
        }
        return row;
    }

    private void checkDictDataByDictType(SysDictData data) {
        LambdaQueryWrapper<SysDictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysDictData::getDictType, data.getDictType())
                .eq(SysDictData::getDictValue, data.getDictValue());
        if (data.getDictCode() != null){
            wrapper.ne(SysDictData::getDictCode, data.getDictCode());
        }
        long count = this.count(wrapper);
        if (count > 0){
            throw new ServiceException("数据字典值不能重复");
        }

    }

    /**
     * 修改保存字典数据信息
     * 
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    public int updateDictData(SysDictData data)
    {
        int row = dictDataMapper.updateById(data);
        if (row > 0)
        {
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            commonCacheService.setDictCache(CacheConstants.SYS_DICT_KEY, data.getDictType(), dictDatas);
        }
        return row;
    }
}

package com.wbyy.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.ServletUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.domain.AjaxResult;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.system.domain.SysDept;
import com.wbyy.system.domain.SysRole;
import com.wbyy.system.domain.SysUser;
import com.wbyy.system.domain.SysUserRole;
import com.wbyy.system.mapper.SysUserRoleMapper;
import com.wbyy.system.service.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class SysUserController extends BaseController {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private ISysPermissionService permissionService;

    @Autowired
    private ISysConfigService configService;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    /**
     * 获取用户列表
     */
    @RequiresPermissions("system:user:list")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    /**
     * 获取用户列表
     */
    @RequiresPermissions("system:user:list")
    @GetMapping("/list-all")
    public R<List<SysUser>> listAll(SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        return R.ok(list);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:user:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:user:import")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        Long operId = SecurityUtils.getUserId();
        String message = userService.importUser(userList, updateSupport, operId);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 获取当前用户信息
     */
//    @InnerAuth
//    @GetMapping("/info/{username}")
//    public R<LoginUser> info(@PathVariable("username") String username) {
////        SysUser sysUser = userService.selectUserByUserName(username);
//        SysUser sysUser = userService.selectUser(username);
//        if (StringUtils.isNull(sysUser)) {
//            return R.fail("用户名或密码错误");
//        }
//        // 部门集合
//        Set<Long> depts = permissionService.getDeptPermission(sysUser);
//        // 角色集合
//        Set<String> roles = permissionService.getRolePermission(sysUser);
//        // 权限集合
//        Set<String> permissions = permissionService.getMenuPermission(sysUser);
//        LoginUser sysUserVo = new LoginUser();
//        ApiUser remoteUser = new ApiUser();
//        BeanUtils.copyBeanProp(remoteUser, sysUser);
//        List<ApiRole> remoteRoles = new ArrayList<>();
//        List<ApiDept> remoteDepts = new ArrayList<>();
//        for (SysRole role : sysUser.getRoles()) {
//            ApiRole remoteRole = new ApiRole();
//            BeanUtils.copyBeanProp(remoteRole, role);
//            remoteRoles.add(remoteRole);
//        }
//        for (SysDept dept : sysUser.getDepts()) {
//            ApiDept remoteDept = new ApiDept();
//            BeanUtils.copyBeanProp(remoteDept, dept);
//            remoteDepts.add(remoteDept);
//        }
//        remoteUser.setRoles(remoteRoles);
//        remoteUser.setDepts(remoteDepts);
//        boolean isAdmin;
//        if (CollectionUtil.isEmpty(roles)) isAdmin = false;
//        else isAdmin = roles.contains(ADMIN_ROLE);
//        remoteUser.setCheckAdmin(isAdmin);
//        sysUserVo.setSonDepts(permissionService.getSonDeptPermission(depts));
//        sysUserVo.setDepts(depts);
//        sysUserVo.setSysUser(remoteUser);
//        sysUserVo.setRoles(roles);
//        sysUserVo.setPermissions(permissions);
//        sysUserVo.setHireDate(sysUser.getHireDate());
//        sysUserVo.setDepartureDate(sysUser.getDepartureDate());
//        sysUserVo.setWeaverUserId(sysUser.getWeaverUserId());
//        return R.ok(sysUserVo);
//    }

    /**
     * 根据用户 IDS 查询用户信息
     *
     * @param userIds 用户ID 集合
     * @return 用户信息
     */
//    @GetMapping("/info/by-user-ids")
//    @InnerAuth
//    public R<List<ApiUser>> getUserInfo(@RequestParam List<Long> userIds) {
//        return R.ok(userService.getUserInfo(userIds));
//    }

    /**
     * 注册用户信息
     */
//    @InnerAuth
//    @PostMapping("/register")
//    public R<Boolean> register(@RequestBody SysUser sysUser) {
//        String username = sysUser.getUserName();
//        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser")))) {
//            return R.fail("当前系统没有开启注册功能！");
//        }
//        if (!userService.checkUserNameUnique(sysUser)) {
//            return R.fail("保存用户'" + username + "'失败，注册账号已存在");
//        }
//        return R.ok(userService.registerUser(sysUser));
//    }

    /**
     * 记录用户登录IP地址和登录时间
     */
//    @InnerAuth
//    @PutMapping("/recordlogin")
//    public R<Boolean> recordlogin(@RequestBody SysUser sysUser) {
//        userService.recordlogin(sysUser.getUserId(), sysUser.getLoginIp(), sysUser.getLoginDate());
//        return R.ok();
//    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        String applicationCode;
        HttpServletRequest httpServletRequest = ServletUtils.getRequest();
        if (null!=httpServletRequest){
            applicationCode = httpServletRequest.getHeader(SecurityConstants.APPLICATION_CODE);
        } else {
            applicationCode = "";
        }
        SysUser user = userService.selectUserById(SecurityUtils.getUserId());
        user.setRoles(Optional
                .of(user.getRoles().stream()
                        .filter(r->applicationCode.equals(r.getApplicationCode()))
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>()));
        //部门全路径
        user.setDeptFullName(permissionService.getDeptFullName(user));
        // 部门集合
        Set<Long> depts = permissionService.getDeptPermission(user);
        // 角色集合
        Set<String> roles = permissionService.getRolePermissionByApplicationCode(user,applicationCode);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermissionByApplication(user,applicationCode);
        //岗位集合
        user.setPosts(permissionService.getPosts(user));
        user.setHasToUpPwd(false);
        if ("true".equals(configService.selectConfigByKey("update.password.on.first.login"))){
            user.setHasToUpPwd(null==user.getUpdatePwdTime());
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("depts", depts);
        ajax.put("permissions", permissions);
        return ajax;

    }

    /**
     * 根据用户编号获取详细信息
     */
    @RequiresPermissions("system:user:query")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SecurityUtils.isAdmin() ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            SysUser sysUser = userService.selectUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
            ajax.put("deptIds", sysUser.getDepts().stream().map(SysDept::getDeptId).collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @RequiresPermissions("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        deptService.checkDeptDataScope(user.getDeptIds());
        roleService.checkRoleDataScope(user.getRoleIds());
        if (!userService.checkUserNameUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhone()) && !userService.checkPhoneUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(SecurityUtils.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());

        deptService.checkDeptDataScope(user.getDeptIds());
        roleService.checkRoleDataScope(user.getRoleIds());
        if (!userService.checkUserNameUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhone()) && !userService.checkPhoneUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(SecurityUtils.getUserId());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @RequiresPermissions("system:user:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds) {
        if (ArrayUtils.contains(userIds, SecurityUtils.getUserId())) {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(SecurityUtils.getUserId());
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(SecurityUtils.getUserId());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @RequiresPermissions("system:user:query")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId) {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SecurityUtils.isAdmin() ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds) {
        userService.checkUserDataScope(userId);
        roleService.checkRoleDataScope(roleIds);
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    /**
     * 获取部门树列表
     */
    @RequiresPermissions("system:user:list")
    @GetMapping("/deptTree")
    public AjaxResult deptTree(SysDept dept) {
        return success(deptService.selectDeptTreeList(dept));
    }

//    @GetMapping("/info-list")
//    @InnerAuth
//    public R<List<ApiUser>> infoList(@RequestParam("userIds") List<Long> userIds) {
//        List<SysUser> list = userService.infoList(userIds);
//        List<ApiUser> result = new ArrayList<>();
//        for (SysUser sysUser : list) {
//            ApiUser apiUser = new ApiUser();
//            BeanUtils.copyProperties(sysUser, apiUser);
//            result.add(apiUser);
//        }
//        return R.ok(result);
//    }

//    @GetMapping("/get-by-id")
//    @InnerAuth
//    public R<ApiUser> getById(@RequestParam("userId")Long userId) {
//        SysUser user = userService.selectUserById(userId);
//        if (null == user) {
//            throw new ServiceException("获取用户信息失败");
//        }
//        ApiUser apiUser = new ApiUser();
//        BeanUtil.copyProperties(user, apiUser);
//        return R.ok(apiUser);
//    }

//    @GetMapping("/info-by-qw")
//    @InnerAuth
//    public R<LoginUser> getById(@RequestParam("qwUserId")String userId) {
//        SysUser sysUser = userService.selectUserByQwUserId(userId);
//        if (null == sysUser) {
//            throw new ServiceException("获取用户信息失败");
//        }
//        // 部门集合
//        Set<Long> depts = permissionService.getDeptPermission(sysUser);
//        // 角色集合
//        Set<String> roles = permissionService.getRolePermission(sysUser);
//        // 权限集合
//        Set<String> permissions = permissionService.getMenuPermission(sysUser);
//        LoginUser sysUserVo = new LoginUser();
//        ApiUser remoteUser = new ApiUser();
//        BeanUtils.copyBeanProp(remoteUser, sysUser);
//        List<ApiRole> remoteRoles = new ArrayList<>();
//        List<ApiDept> remoteDepts = new ArrayList<>();
//        for (SysRole role : sysUser.getRoles()) {
//            ApiRole remoteRole = new ApiRole();
//            BeanUtils.copyBeanProp(remoteRole, role);
//            remoteRoles.add(remoteRole);
//        }
//        for (SysDept dept : sysUser.getDepts()) {
//            ApiDept remoteDept = new ApiDept();
//            BeanUtils.copyBeanProp(remoteDept, dept);
//            remoteDepts.add(remoteDept);
//        }
//        remoteUser.setRoles(remoteRoles);
//        remoteUser.setDepts(remoteDepts);
//        boolean isAdmin;
//        if (CollectionUtil.isEmpty(roles)) isAdmin = false;
//        else isAdmin = roles.contains(ADMIN_ROLE);
//        remoteUser.setCheckAdmin(isAdmin);
//        sysUserVo.setSonDepts(permissionService.getSonDeptPermission(depts));
//        sysUserVo.setDepts(depts);
//        sysUserVo.setSysUser(remoteUser);
//        sysUserVo.setRoles(roles);
//        sysUserVo.setPermissions(permissions);
//        sysUserVo.setHireDate(sysUser.getHireDate());
//        sysUserVo.setDepartureDate(sysUser.getDepartureDate());
//        sysUserVo.setWeaverUserId(sysUser.getWeaverUserId());
//        return R.ok(sysUserVo);
//    }

    @GetMapping("/async-user-hire-data")
    public R<?> asyncUserHireData(){
        return R.ok(userService.asyncUserHireData());
    }

//    @GetMapping("/info-by-weaver-user-id")
//    @InnerAuth
//    public R<ApiUser> infoByWeaverUserId(@RequestParam("weaverUserId")String weaverUserId){
//        SysUser one = userService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getWeaverUserId, weaverUserId), false);
//        ApiUser user = BeanUtil.copyProperties(one, ApiUser.class);
//        return R.ok(user);
//    }

    @GetMapping("/async-pm-role")
    public R<Void> asyncPmRole(){
        sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getRoleId, 2L));
        List<SysUserRole> list = new ArrayList<>();
        for (Long userId : userService.list().stream().map(SysUser::getUserId).toList()) {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(2L);
            list.add(ur);
        }
        sysUserRoleMapper.insert(list);
        return R.ok();
    }
}

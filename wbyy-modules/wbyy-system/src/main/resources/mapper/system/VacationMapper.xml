<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.system.mapper.VacationMapper">

    <select id="listUserVacation" resultType="com.wbyy.system.domain.Vacation">
        select id,
               user_id,
               name,
               start_time,
               end_time,
               vacation_id,
               time_length
        from vacation
        where user_id = #{userId}
          and (
            -- 假期开始时间在查询时间之间
            (start_time &gt;= #{startDate} and start_time &lt;= #{endDate})
                -- 假期结束时间在查询时间之间
                or (end_time &gt;= #{startDate} and end_time &lt;= #{endDate})
                -- 假期开始与结束时间横跨查询时间
                or (start_time &lt;= #{startDate} and end_time &gt;= #{endDate})
            )
          and del_flag = 0
    </select>

    <select id="listUsersVacation" resultType="com.wbyy.system.domain.Vacation">
        select id,
               user_id,
               name,
               start_time,
               end_time,
               vacation_id,
               time_length
        from vacation
        where user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
          and (
            -- 假期开始时间在查询时间之间
            (start_time &gt;= #{startDate} and start_time &lt;= #{endDate})
                -- 假期结束时间在查询时间之间
                or (end_time &gt;= #{startDate} and end_time &lt;= #{endDate})
                -- 假期开始与结束时间横跨查询时间
                or (start_time &lt;= #{startDate} and end_time &gt;= #{endDate})
            )
          and del_flag = 0
    </select>
</mapper>
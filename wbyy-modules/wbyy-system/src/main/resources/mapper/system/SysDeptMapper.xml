<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.system.mapper.SysDeptMapper">

	<resultMap type="com.wbyy.system.domain.SysDept" id="SysDeptResult">
		<id property="deptId" column="dept_id"     />
		<result property="parentId"   column="parent_id"   />
		<result property="ancestors"  column="ancestors"   />
		<result property="deptName"   column="dept_name"   />
		<result property="orderSort" column="order_sort"   />
		<result property="leader"     column="leader"      />
		<result property="phone"      column="phone"       />
		<result property="email"      column="email"       />
		<result property="status"     column="status"      />
		<result property="delFlag"    column="del_flag"    />
		<result property="parentName" column="parent_name" />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
		<result property="center" column="center" />
		<result property="deptCode" column="dept_code" />
		<result property="originalDeptId" column="original_dept_id"/>
	</resultMap>
	
	<sql id="selectDeptVo">
        select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_sort, d.leader, d.phone, d.email, d.status, d.del_flag, d.create_by, d.create_time, d.center, d.dept_code
        from sys_dept d
    </sql>
    
	<select id="selectDeptList" parameterType="com.wbyy.system.domain.SysDept" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
		<if test="deptId != null and deptId != 0">
            AND dept_id = #{deptId}
        </if>
        <if test="ancestors != null and ancestors != ''">
			AND ancestors like concat('%',#{ancestors},'%')
		</if>
        <if test="parentId != null and parentId != 0">
			AND parent_id = #{parentId}
		</if>
		<if test="deptName != null and deptName != ''">
			AND dept_name like concat('%', #{deptName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND status = #{status}
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
		order by d.parent_id, d.order_sort
    </select>
    
    <select id="selectDeptListByRoleId" resultType="Long">
		select d.dept_id
		from sys_dept d
            left join sys_role_dept rd on d.dept_id = rd.dept_id
        where rd.role_id = #{roleId}
            <if test="deptCheckStrictly">
              and d.dept_id not in (select d.parent_id from sys_dept d inner join sys_role_dept rd on d.dept_id = rd.dept_id and rd.role_id = #{roleId})
            </if>
		order by d.parent_id, d.order_sort
	</select>
    
    <select id="selectDeptById" parameterType="Long" resultMap="SysDeptResult">
		<include refid="selectDeptVo"/>
		where dept_id = #{deptId}
	</select>

    <select id="checkDeptExistUser" parameterType="Long" resultType="int">
		select count(1) from sys_user_dept where dept_id = #{deptId}
	</select>
	
	<select id="hasChildByDeptId" parameterType="Long" resultType="int">
		select count(1) from sys_dept
		where del_flag = '0' and parent_id = #{deptId} limit 1
	</select>
	
	<select id="selectChildrenDeptById" parameterType="Long" resultMap="SysDeptResult">
		select * from sys_dept where find_in_set(#{deptId}, ancestors)
	</select>
	
	<select id="selectNormalChildrenDeptById" parameterType="Long" resultType="int">
		select count(*) from sys_dept where status = 0 and del_flag = '0' and find_in_set(#{deptId}, ancestors)
	</select>
	
	<select id="checkDeptNameUnique" resultMap="SysDeptResult">
	    <include refid="selectDeptVo"/>
		where dept_name=#{deptName} and parent_id = #{parentId} and del_flag = '0' limit 1
	</select>
    
    <insert id="insertDept" parameterType="com.wbyy.system.domain.SysDept">
 		insert into sys_dept(
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="parentId != null and parentId != 0">parent_id,</if>
 			<if test="deptName != null and deptName != ''">dept_name,</if>
 			<if test="ancestors != null and ancestors != ''">ancestors,</if>
 			<if test="orderSort != null">order_sort,</if>
 			<if test="leader != null and leader != ''">leader,</if>
 			<if test="phone != null and phone != ''">phone,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="status != null">status,</if>
 			<if test="center != null and center != ''">center,</if>
 			<if test="deptCode != null">dept_code,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			create_time
 		)values(
 			<if test="deptId != null and deptId != 0">#{deptId},</if>
 			<if test="parentId != null and parentId != 0">#{parentId},</if>
 			<if test="deptName != null and deptName != ''">#{deptName},</if>
 			<if test="ancestors != null and ancestors != ''">#{ancestors},</if>
 			<if test="orderSort != null">#{orderSort},</if>
 			<if test="leader != null and leader != ''">#{leader},</if>
 			<if test="phone != null and phone != ''">#{phone},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="status != null">#{status},</if>
 			<if test="center != null and center != ''">#{center},</if>
 			<if test="deptCode != null and deptCode != ''">#{deptCode},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			sysdate()
 		)
	</insert>
	
	<update id="updateDept" parameterType="com.wbyy.system.domain.SysDept">
 		update sys_dept
 		<set>
 			<if test="parentId != null and parentId != 0">parent_id = #{parentId},</if>
 			<if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
 			<if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
 			<if test="orderSort != null">order_sort = #{orderSort},</if>
 			<if test="leader != null">leader = #{leader},</if>
 			<if test="phone != null">phone = #{phone},</if>
 			<if test="email != null">email = #{email},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="center != null and center != ''">center = #{center},</if>
 			<if test="deptCode != null and deptCode != ''">dept_code = #{deptCode},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
        where dept_id = #{deptId}
    </update>
	
	<update id="updateDeptChildren" parameterType="java.util.List">
	    update sys_dept set ancestors =
	    <foreach collection="depts" item="item" index="index"
	        separator=" " open="case deptId" close="end">
	        when #{item.deptId} then #{item.ancestors}
	    </foreach>
	    where dept_id in
	    <foreach collection="depts" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.deptId}
	    </foreach>
	</update>
	 
	<update id="updateDeptStatusNormal" parameterType="Long">
 	    update sys_dept set status = '0' where dept_id in
 	    <foreach collection="array" item="deptId" open="(" separator="," close=")">
        	#{deptId}
        </foreach>
	</update>
	
	<delete id="deleteDeptById" parameterType="Long">
		update sys_dept set del_flag = '1' where dept_id = #{deptId}
	</delete>

	<select id="selectDeptPermissionByUserId" parameterType="Long" resultMap="SysDeptResult">
		select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_sort, d.leader, d.phone, d.email, d.status, d.del_flag, d.create_by, d.create_time, d.center, d.dept_code
		from sys_dept d
		left join sys_user_dept ud on ud.dept_id=d.dept_id
		left join sys_user u on ud.user_id=u.user_id
		where u.user_id=#{userId}
	</select>
	<select id="selectAllChildrenByDeptIds" resultType="com.wbyy.system.domain.SysDept">
		WITH RECURSIVE chapter_tree AS (
			SELECT *,dept_id as deptCenterId,dept_name as deptCenterName
			FROM sys_dept
			WHERE dept_id in
			<foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
				#{deptId}
			</foreach>
			UNION ALL
			SELECT c.*,ct.deptCenterId,ct.deptCenterName
			FROM sys_dept c
					 JOIN chapter_tree ct ON c.parent_id = ct.dept_id
			WHERE c.del_flag = 0 -- 假设只查询未被逻辑删除的记录
		)
		SELECT * FROM chapter_tree;
	</select>

	<insert id="saveBatchFromRemote">
		insert into sys_dept(dept_id,parent_id,dept_name,ancestors,order_sort,leader,status,qw_dept_id,create_by,create_time,weaver_dept_id,center, dept_code )
		values
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.deptId},#{item.parentId},#{item.deptName},#{item.ancestors},#{item.orderSort},#{item.leader},#{item.status},#{item.qwDeptId},#{item.createBy},sysdate(),#{item.weaverDeptId},#{item.center},#{item.deptCode}
			)
		</foreach>
	</insert>

	<update id="updateBatchFromRemote">
		<foreach collection="list" item="item" index="index" open="" close="" separator=";">
			update sys_dept
			<set>
				<if test="item.parentId != null and item.parentId != ''">parent_id = #{item.parentId},</if>
				<if test="item.deptName != null and item.deptName != ''">dept_name = #{item.deptName},</if>
				<if test="item.ancestors != null and item.ancestors != ''">ancestors = #{item.ancestors},</if>
				<if test="item.leader != null">leader = #{item.leader},</if>
				<if test="item.updateBy != null and item.updateBy != ''">update_by = #{item.updateBy},</if>
				<if test="item.qwDeptId != null and item.qwDeptId != ''">qw_dept_id = #{item.qwDeptId},</if>
				<if test="item.weaverDeptId != null and item.weaverDeptId != ''">weaver_dept_id = #{item.weaverDeptId},</if>
				<if test="item.center != null and item.center != ''">center = #{item.center},</if>
				<if test="item.deptCode != null and item.deptCode != ''">dept_code = #{item.deptCode},</if>
				update_time = sysdate()
			</set>
			where dept_id = #{item.deptId}
		</foreach>
	</update>

	<select id="getDeptByUserId" resultMap="SysDeptResult">
		select sd.dept_id,
			   sd.parent_id,
			   sd.ancestors,
			   sd.dept_name,
			   sd.dept_code,
			   sd.center
		from sys_dept sd
				 left join sys_user_dept sud on sd.dept_id = sud.dept_id
		where sd.del_flag = 0
		  and sd.status = 0
		  and sud.user_id = #{userId}
		limit 1
	</select>

	<select id="getCenterDept" resultMap="SysDeptResult">
		WITH RECURSIVE DeptHierarchy AS (
			-- 初始选择：从指定的部门开始
			SELECT dept_id, parent_id, center, dept_name, dept_code
			FROM sys_dept
			WHERE status = 0
			  and del_flag = 0
			  and dept_id = #{deptId}
			UNION ALL
			-- 递归部分：连接自身以找到所有上级部门
			SELECT d.dept_id, d.parent_id, d.center, d.dept_name, d.dept_code
			FROM sys_dept d
					 INNER JOIN DeptHierarchy dh ON d.dept_id = dh.parent_id
			WHERE d.status = 0
			  and d.del_flag = 0)
		-- 最终选择：过滤出标记为 'Y' 的中心部门
		SELECT dept_id, dept_name, center, dept_code
		FROM DeptHierarchy
		WHERE center = 'Y';
	</select>

	<select id="listCenterDept" resultMap="SysDeptResult">
		WITH RECURSIVE DeptHierarchy AS (
			-- 初始选择：从指定的dept_id范围内开始
			SELECT dept_id, parent_id, dept_name,center, dept_id as original_dept_id
			FROM sys_dept
			WHERE dept_id in
			<foreach collection="deptIds" separator="," open="(" close=")" item="deptId">
				#{deptId}
			</foreach>

			UNION ALL

			-- 递归部分：连接自身以找到所有上级部门
			SELECT d.dept_id, d.parent_id, d.dept_name,d.center, dh.original_dept_id
			FROM sys_dept d
					 INNER JOIN DeptHierarchy dh ON d.dept_id = dh.parent_id
		)
-- 最终选择：可以选择需要的字段
		SELECT dept_id, parent_id, dept_name,center, original_dept_id
		FROM DeptHierarchy WHERE center = 'Y'
		ORDER BY original_dept_id, dept_id;
	</select>

	<select id="selectSonDeptPermission" resultType="java.lang.Long">
		WITH RECURSIVE dept_hierarchy AS (
		SELECT dept_id, parent_id
		FROM sys_dept
		WHERE dept_id in
		<foreach collection="depts" item="dept" open="(" close=")" separator=",">
			#{dept}
		</foreach>

		UNION ALL

		SELECT s.dept_id, s.parent_id
		FROM sys_dept s
		INNER JOIN dept_hierarchy dh ON s.parent_id = dh.dept_id
		)
		SELECT dept_id FROM dept_hierarchy;
	</select>

	<select id="selectAllParentByDeptId" resultType="com.wbyy.system.domain.SysDept">
		WITH RECURSIVE chapter_tree AS (
			SELECT *, 0 AS level
			FROM sys_dept
			WHERE dept_id = #{deptId}
			UNION ALL
			SELECT c.*, ct.level + 1
			FROM sys_dept c
					 JOIN chapter_tree ct ON ct.parent_id = c.dept_id
			WHERE c.del_flag = 0
		)
		SELECT * FROM chapter_tree
		ORDER BY level desc;
	</select>

	<select id="listManageDept" resultMap="SysDeptResult">
		WITH RECURSIVE d AS (
		SELECT dept_id, parent_id, dept_name
		FROM sys_dept
		WHERE dept_id in
		<foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
			#{deptId}
		</foreach>
		UNION ALL
		SELECT c.dept_id, c.parent_id, c.dept_name
		FROM sys_dept c
		JOIN d ct ON c.parent_id = ct.dept_id
		WHERE c.del_flag = 0 -- 假设只查询未被逻辑删除的记录
		)
		SELECT d.dept_id, d.parent_id, d.dept_name FROM d left join sys_user_dept ud on d.dept_id=ud.dept_id
		where 1=1
		<!-- 数据范围过滤 -->
		${params.dataScope}
		group by d.dept_id, d.parent_id, d.dept_name
		order by d.dept_id, d.parent_id
		;
	</select>

	<select id="listByUserIds" resultType="com.wbyy.system.domain.SysUserDept">
        select user_id, dept_id from sys_user_dept
        where user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>
</mapper>
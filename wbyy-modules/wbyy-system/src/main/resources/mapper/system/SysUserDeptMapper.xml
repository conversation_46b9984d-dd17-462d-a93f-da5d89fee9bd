<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.system.mapper.SysUserDeptMapper">
    <resultMap type="com.wbyy.system.domain.SysUserDept" id="SysUserDeptResult">
        <result property="userId"     column="user_id"      />
        <result property="deptId"     column="dept_id"      />
    </resultMap>

    <delete id="deleteUserDeptByUserId" parameterType="Long">
        delete from sys_user_dept where user_id=#{userId}
    </delete>

    <select id="countUserDeptByDeptId" resultType="Integer">
        select count(1) from sys_user_dept where dept_id=#{deptId}
    </select>

    <delete id="deleteUserDept" parameterType="Long">
        delete from sys_user_dept where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <insert id="batchUserDept">
        insert into sys_user_dept(user_id, dept_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.userId},#{item.deptId})
        </foreach>
    </insert>

    <delete id="deleteUserDeptInfo" parameterType="com.wbyy.system.domain.SysUserDept">
        delete from sys_user_dept where user_id=#{userId} and dept_id=#{deptId}
    </delete>

    <delete id="deleteUserDeptInfos">
        delete from sys_user_dept where dept_id=#{deptId} and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>
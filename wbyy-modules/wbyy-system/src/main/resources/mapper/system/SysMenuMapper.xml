<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.system.mapper.SysMenuMapper">

	<resultMap type="com.wbyy.system.domain.SysMenu" id="SysMenuResult">
		<id property="menuId" column="menu_id"        />
		<result property="menuName"       column="menu_name"      />
		<result property="parentName"     column="parent_name"    />
		<result property="parentId"       column="parent_id"      />
		<result property="menuSort" column="menu_sort"      />
		<result property="path"           column="path"           />
		<result property="component"      column="component"      />
		<result property="query"          column="query"          />
		<result property="routeName"      column="route_name"     />
		<result property="isFrame"        column="is_frame"       />
		<result property="isCache"        column="is_cache"       />
		<result property="menuType"       column="menu_type"      />
		<result property="visible"        column="visible"        />
		<result property="status"         column="status"         />
		<result property="perms"          column="perms"          />
		<result property="icon"           column="icon"           />
		<result property="createBy"       column="create_by"      />
		<result property="createTime"     column="create_time"    />
		<result property="updateTime"     column="update_time"    />
		<result property="updateBy"       column="update_by"      />
		<result property="remark"         column="remark"         />
	</resultMap>

	<sql id="selectMenuVo">
        select m.*, ifnull(m.perms,'') as perms
		from sys_menu m
    </sql>
    
    <select id="selectMenuList" parameterType="com.wbyy.system.domain.SysMenu" resultMap="SysMenuResult">
		<include refid="selectMenuVo"/>
		<where>
			<if test="menuName != null and menuName != ''">
				AND m.menu_name like concat('%', #{menuName}, '%')
			</if>
			<if test="visible != null and visible != ''">
				AND m.visible = #{visible}
			</if>
			<if test="status != null and status != ''">
				AND m.status = #{status}
			</if>
			<if test="applicationCode != null and applicationCode != ''">
				And m.application_code = #{applicationCode}
			</if>
		</where>
		order by m.parent_id, m.menu_sort
	</select>
	
	<select id="selectMenuTreeAll" resultMap="SysMenuResult">
		select
		    distinct m.*, ifnull(m.perms,'') as perms
		from
		    sys_menu m
		where
		    m.menu_type in ('M', 'C')
		  and m.status = 0
		  and m.application_code = #{applicationCode}
		order by
		    m.parent_id, m.menu_sort
	</select>
	
	<select id="selectMenuListByUserId" parameterType="com.wbyy.system.domain.SysMenu" resultMap="SysMenuResult">
		select
		    distinct m.*,
		             ifnull(m.perms,'') as perms
		from sys_menu m
		left join sys_role_menu rm on m.menu_id = rm.menu_id
		left join sys_user_role ur on rm.role_id = ur.role_id
		left join sys_role ro on ur.role_id = ro.role_id
		where ur.user_id = #{params.userId}
		<if test="menuName != null and menuName != ''">
            AND m.menu_name like concat('%', #{menuName}, '%')
		</if>
		<if test="visible != null and visible != ''">
            AND m.visible = #{visible}
		</if>
		<if test="status != null and status != ''">
            AND m.status = #{status}
		</if>
		<if test="applicationCode != null and applicationCode != ''">
			And m.application_code = #{applicationCode}
		</if>
		order by m.parent_id, m.menu_sort
	</select>
    
    <select id="selectMenuTreeByUserId" resultMap="SysMenuResult">
		select
		    distinct m.*, ifnull(m.perms,'') as perms
		from sys_menu m
			 left join sys_role_menu rm on m.menu_id = rm.menu_id
			 left join sys_user_role ur on rm.role_id = ur.role_id
			 left join sys_role ro on ur.role_id = ro.role_id
			 left join sys_user u on ur.user_id = u.user_id
		where
		    u.user_id = #{userId}
		  and m.menu_type in ('M', 'C')
		  and m.status = 0
		  AND ro.status = 0
		  and m.application_code = #{applicationCode}
		order by m.parent_id, m.menu_sort
	</select>
	
	<select id="selectMenuListByRoleId" resultType="Long">
		select m.menu_id
		from sys_menu m
            left join sys_role_menu rm on m.menu_id = rm.menu_id
        where rm.role_id = #{roleId}
            <if test="menuCheckStrictly">
              and m.menu_id not in (select m.parent_id from sys_menu m inner join sys_role_menu rm on m.menu_id = rm.menu_id and rm.role_id = #{roleId})
            </if>
		order by m.parent_id, m.menu_sort
	</select>
	
	<select id="selectMenuPerms" resultType="String">
		select distinct m.perms
		from sys_menu m
			 left join sys_role_menu rm on m.menu_id = rm.menu_id
			 left join sys_user_role ur on rm.role_id = ur.role_id
	</select>

	<select id="selectMenuPermsByUserId" parameterType="Long" resultType="String">
		select distinct m.perms
		from sys_menu m
			 left join sys_role_menu rm on m.menu_id = rm.menu_id
			 left join sys_user_role ur on rm.role_id = ur.role_id
			 left join sys_role r on r.role_id = ur.role_id
		where m.status = '0' and r.status = '0' and ur.user_id = #{userId}
	</select>
	
	<select id="selectMenuPermsByRoleId" parameterType="Long" resultType="String">
		select distinct m.perms
		from sys_menu m
			 left join sys_role_menu rm on m.menu_id = rm.menu_id
		where m.status = '0' and rm.role_id = #{roleId}
	</select>

	<select id="hasChildByMenuId" resultType="Integer">
	    select count(1) from sys_menu where parent_id = #{menuId}  
	</select>
	
	<select id="checkMenuNameUnique" parameterType="com.wbyy.system.domain.SysMenu" resultMap="SysMenuResult">
		<include refid="selectMenuVo"/>
		where
		menu_name=#{menuName}
		and parent_id = #{parentId}
		and application_code = #{applicationCode}
		limit 1
	</select>

	<select id="listByApplicationCode" resultMap="SysMenuResult">
		<include refid="selectMenuVo"/>
		where
		application_code = #{applicationCode}
		and menu_type != 'F'
		and status = 0
    </select>

	<select id="listFunction" resultMap="SysMenuResult">
		<include refid="selectMenuVo"/>
		where
		parent_id = #{menuId}
		and menu_type = 'F'
		and status = 0
	</select>

	<select id="selectMenuPermsByUserIdByApplicationCode" resultType="java.lang.String">
		select distinct m.perms
		from sys_menu m
				 left join sys_role_menu rm on m.menu_id = rm.menu_id
				 left join sys_user_role ur on rm.role_id = ur.role_id
				 left join sys_role r on r.role_id = ur.role_id
		where m.status = '0' and r.status = '0' and ur.user_id = #{userId}
		and r.application_code=#{applicationCode}
    </select>

	<select id="selectMenuPermsByRoleIdByApplicationCode" resultType="java.lang.String">
		select distinct m.perms
		from sys_menu m
				 left join sys_role_menu rm on m.menu_id = rm.menu_id
			left join sys_role r on r.role_id = rm.role_id
		where m.status = '0' and rm.role_id = #{roleId}
		and r.application_code=#{applicationCode}
	</select>
</mapper>
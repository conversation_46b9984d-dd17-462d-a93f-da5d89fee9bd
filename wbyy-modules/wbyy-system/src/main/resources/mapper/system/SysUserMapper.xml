<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.system.mapper.SysUserMapper">

    <resultMap type="com.wbyy.system.domain.SysUser" id="SysUserResult">
        <id property="userId" column="user_id"      />
        <result property="userName"     column="user_name"    />
        <result property="realName" column="real_name"    />
		<result property="workNumber" column="work_number"    />
        <result property="email"        column="email"        />
        <result property="phone" column="phone"  />
        <result property="sex"          column="sex"          />
        <result property="avatar"       column="avatar"       />
        <result property="password"     column="password"     />
        <result property="status"       column="status"       />
        <result property="delFlag"      column="del_flag"     />
        <result property="loginIp"      column="login_ip"     />
        <result property="loginDate"    column="login_date"   />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
		<result property="hireDate" column="hire_date"/>
		<result property="departureDate" column="departure_date"/>
		<result property="updatePwdTime" column="update_pwd_time"/>
		<collection  property="depts"   javaType="java.util.List"  resultMap="DeptResult" />
        <collection  property="roles"   javaType="java.util.List"  resultMap="RoleResult" />
    </resultMap>
	
    <resultMap id="DeptResult" type="com.wbyy.system.domain.SysDept">
        <id     property="deptId"    column="dept_id"     />
        <result property="parentId"  column="parent_id"   />
        <result property="deptName"  column="dept_name"   />
        <result property="ancestors" column="ancestors"   />
        <result property="orderSort"  column="order_sort"   />
        <result property="leader"    column="leader"      />
        <result property="status"    column="dept_status" />
    </resultMap>
	
    <resultMap id="RoleResult" type="com.wbyy.system.domain.SysRole">
        <id     property="roleId"       column="role_id"        />
        <result property="roleName"     column="role_name"      />
        <result property="roleKey"      column="role_key"       />
        <result property="roleSort"     column="role_sort"      />
        <result property="dataScope"    column="data_scope"     />
        <result property="status"       column="role_status"    />
		<result property="applicationCode" column="application_code" />
    </resultMap>
	
	<sql id="selectUserVo">
        select u.user_id, u.user_name, u.real_name,u.work_number, u.email, u.avatar, u.phone, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
        d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_sort, d.leader, d.status as dept_status,
        r.role_id , r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status,
			r.application_code,
		u.hire_date,u.departure_date,u.weaver_user_id,u.qw_user_id,u.update_pwd_time
        from sys_user u
		left join sys_user_dept ud on u.user_id = ud.user_id
		    left join sys_dept d on ud.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>
    
    <select id="selectUserList" parameterType="com.wbyy.system.domain.SysUser" resultMap="SysUserResult">
		select
			u.user_id, u.user_name, u.real_name,u.work_number, u.email, u.avatar,
			u.phone, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by,
			u.create_time, u.remark, d.dept_name, d.leader,d.dept_id
		from sys_user u
		left join sys_user_dept ud on u.user_id = ud.user_id
		left join sys_dept d on ud.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="keyword != null and keyword != ''">
		  and (
			u.real_name like concat('%', #{keyword}, '%')
		    or
			u.work_number like concat('%', #{keyword}, '%')
		    or
			u.user_id like concat('%', #{keyword}, '%')
			)
		</if>
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phone != null and phone != ''">
			AND u.phone like concat('%', #{phone}, '%')
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (d.dept_id = #{deptId} OR d.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectAllocatedList" parameterType="com.wbyy.system.domain.SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.user_name, u.real_name,u.work_number, u.email, u.phone, u.status, u.create_time
	    from sys_user u
		left join sys_user_dept ud on u.user_id = ud.user_id
		left join sys_dept d on ud.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phone != null and phone != ''">
			AND u.phone like concat('%', #{phone}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectUnallocatedList" parameterType="com.wbyy.system.domain.SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.user_name, u.real_name,u.work_number, u.email, u.phone, u.status, u.create_time
	    from sys_user u
		left join sys_user_dept ud on u.user_id = ud.user_id
		left join sys_dept d on ud.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phone != null and phone != ''">
			AND u.phone like concat('%', #{phone}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName} and u.del_flag = '0'
	</select>
	
	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>
	
	<select id="checkUserNameUnique" parameterType="String" resultMap="SysUserResult">
		select user_id ,user_name from sys_user where user_name = #{userName} and del_flag = '0' limit 1
	</select>
	
	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id ,phone from sys_user where phone = #{phone} and del_flag = '0' limit 1
	</select>
	
	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select user_id ,email from sys_user where email = #{email} and del_flag = '0' limit 1
	</select>
	
	<insert id="insertUser" parameterType="com.wbyy.system.domain.SysUser" >
 		insert into sys_user(
 			<if test="userId != null and userId != 0">user_id</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="realName != null and realName != ''">real_name,</if>
		<if test="workNumber != null and workNumber != ''">work_number,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="phone != null and phone != ''">phone,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="password != null and password != ''">password,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			create_time
 		)values(
 			<if test="userId != null and userId != ''">#{userId},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="realName != null and realName != ''">#{real_name},</if>
		<if test="workNumber != null and workNumber != ''">#{work_number},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="phone != null and phone != ''">#{phone},</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="password != null and password != ''">#{password},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			sysdate()
 		)
	</insert>
	
	<update id="updateUser" parameterType="com.wbyy.system.domain.SysUser">
 		update sys_user
 		<set>
 			<if test="userName != null and userName != ''">user_name = #{userName},</if>
 			<if test="realName != null and realName != ''">nick_name = #{real_name},</if>
			<if test="workNumber != null and workNumber != ''">nick_name = #{work_number},</if>
 			<if test="email != null ">email = #{email},</if>
 			<if test="phone != null ">phone = #{phone},</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			update_time = sysdate()
 		</set>
		where user_id = #{userId}
	</update>
	
	<update id="updateUserStatus" parameterType="com.wbyy.system.domain.SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>
	
	<update id="updateUserAvatar" parameterType="com.wbyy.system.domain.SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>
	
	<update id="resetUserPwd" parameterType="com.wbyy.system.domain.SysUser">
 		update sys_user set password = #{password},update_pwd_time = now() where user_name = #{userName}
	</update>
	
	<delete id="deleteUserById" parameterType="Long">
 		update sys_user set del_flag = '1' where user_id = #{userId}
 	</delete>
 	
 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '1' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach> 
 	</delete>

    <insert id="saveBatchFromRemote">
		insert into sys_user(user_id,user_name,real_name,work_number,password,status,qw_user_id,create_by, create_time
		,email,phone,sex,weaver_user_id,avatar,hire_date,departure_date
		)
		values
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.userId},#{item.userName},#{item.realName},#{item.workNumber},#{item.password},#{item.status},#{item.qwUserId},#{item.createBy},
			sysdate(),#{item.email},#{item.phone},#{item.sex},#{item.weaverUserId},#{item.avatar},#{item.hireDate},#{item.departureDate}
			)
		</foreach>
	</insert>

    <update id="updateBatchFromRemote">
		<foreach collection="list" item="item" index="index" separator=";">
			update sys_user
			<set>
				<if test="item.userName != null and item.userName != ''">user_name = #{item.userName},</if>
				<if test="item.realName != null and item.realName != ''">real_name = #{item.realName},</if>
				<if test="item.workNumber != null and item.workNumber != ''">work_number = #{item.workNumber},</if>
				<if test="item.updateBy != null and item.updateBy != ''">update_by = #{item.updateBy},</if>
				<if test="item.email != null and item.email != ''">email = #{item.email},</if>
				<if test="item.phone != null and item.phone != ''">phone = #{item.phone},</if>
				<if test="item.sex != null and item.sex != ''">sex = #{item.sex},</if>
				<if test="item.status != null and item.status != ''">status = #{item.status},</if>
				<if test="item.qwUserId != null and item.qwUserId != ''">qw_user_id = #{item.qwUserId},</if>
				<if test="item.weaverUserId != null and item.weaverUserId != ''">weaver_user_id = #{item.weaverUserId},</if>
				<if test="item.avatar != null and item.avatar != ''">avatar = #{item.avatar},</if>
				<if test="item.hireDate != null">hire_date = #{item.hireDate},</if>
				<if test="item.departureDate != null">departure_date = #{item.departureDate},</if>
				update_time = sysdate()
			</set>
			where user_id = #{item.userId}
		</foreach>

	</update>

    <update id="updateWeaverUserId">
		<foreach collection="employees" item="item" index="index" separator=";">
			update sys_user
			<set>
				weaver_user_id=#{item.id}
			</set>
			where  work_number= #{item.job_num} and del_flag=0
		</foreach>
    </update>

    <update id="updateQwUserId">
		<foreach collection="qwUserIds" item="item" index="index" separator=";">
			update sys_user
			<set>
				qw_user_id=#{item.qwUserId}
			</set>
			where  user_name= #{item.userName}
		</foreach>
	</update>

	<select id="selectUserByQwUserId" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.qw_user_id = #{qwUserId} and u.del_flag = '0'
	</select>

    <select id="selectUser" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where (u.user_name = #{value} or u.work_number = #{value} or u.phone = #{value})
		and u.del_flag = '0'
	</select>

    <select id="selectUserByIdByApplication" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>
	<select id="getAllUserByDept" resultType="com.wbyy.system.api.domain.ApiUser">
		select
			u.user_id, u.user_name, u.real_name,u.work_number, u.email, u.avatar,
			u.phone, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by,
			u.create_time, u.remark
		from sys_user u
				 left join sys_user_dept ud on u.user_id = ud.user_id
				 left join sys_dept d on ud.dept_id = d.dept_id
		where
		    u.del_flag = 0
		  and d.del_flag = 0
		  and d.ancestors like concat('%', #{deptId}, '%')
		<if test="keyword != null and keyword != ''">
			and (
				 u.real_name like concat('%', #{keyword}, '%')
				or u.work_number like concat('%', #{keyword}, '%')
			)
		</if>
	</select>

    <update id="initUserPwd">
		update sys_user set password = #{password},update_pwd_time = null where user_id = #{userId}
	</update>
</mapper>
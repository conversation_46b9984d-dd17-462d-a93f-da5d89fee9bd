package com.wbyy.crm.modules.contracts.receivedamount.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.contracts.receivedamount.domain.ReceivedAmount;
import com.wbyy.crm.modules.contracts.receivedamount.mapper.ReceivedAmountMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.crm.modules.contracts.receivedamount.service.ReceivedAmountService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 实收金额表服务接口实现
 *
 * <AUTHOR>
 * @since 2025-04-01 16:53:31
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("receivedAmountService")
public class ReceivedAmountServiceImpl extends ServiceImpl<ReceivedAmountMapper, ReceivedAmount> implements ReceivedAmountService , BaseService {
    private final ReceivedAmountMapper receivedAmountMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<ReceivedAmount> remoteDataList = list.stream().map(ReceivedAmount.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(ReceivedAmount::getId).collect(Collectors.toSet());
        List<ReceivedAmount> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(ReceivedAmount::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【ReceivedAmount】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【ReceivedAmount】落库数据数：{}",remoteDataList.size());
    }

    @Override
    public List<ReceivedAmount> listByPaymentTimeRange(Date startTime, Date endTime) {
        LambdaQueryWrapper<ReceivedAmount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(ReceivedAmount::getPaymentTime,startTime);
        queryWrapper.le(ReceivedAmount::getPaymentTime,endTime);
        return list(queryWrapper);
    }
}
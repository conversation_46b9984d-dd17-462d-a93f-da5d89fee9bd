<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.crm.modules.contracts.contract.mapper.ContractMapper">
    <sql id="tableName">
        contract
    </sql>

    <sql id="baseColumn">
        id,department,department_id,contract_no,contract_name,customer_name,customer_id,project_no,contract_amount,business_type,registration_type,contract_type,unit_price,opportunity_name,opportunity_id,opportunity_no,internal_contract_no,responsible_person,responsible_person_id,owner_department,owner_department_id,collaborators_name,collaborators_id,created_by_name,created_by_id,created_at,updated_by_name,updated_by_id,updated_at
    </sql>

    <select id="selectUserNames" resultType="java.lang.String">
        SELECT responsible_person as user_name FROM contract
        WHERE responsible_person is NOT NULL
        GROUP BY responsible_person
        UNION ALL
        SELECT collaborators_name1 as user_name FROM contract
        WHERE collaborators_name1 is NOT NULL
        GROUP BY collaborators_name1
        UNION ALL
        SELECT collaborators_name2 as user_name FROM contract
        WHERE collaborators_name2 is NOT NULL
        GROUP BY collaborators_name2
        UNION ALL
        SELECT collaborators_name3 as user_name FROM contract
        WHERE collaborators_name3 is NOT NULL
        GROUP BY collaborators_name3
        UNION ALL
        SELECT
        SUBSTRING_INDEX( SUBSTRING_INDEX( collaborators_name4, ',', n ), ',', - 1 ) AS user_name
        FROM
        contract,
        ( SELECT @rownum := @rownum + 1 AS n FROM ( SELECT @rownum := 0 ) r, contract ) x
        WHERE
        n &lt;= ( LENGTH( collaborators_name4 ) - LENGTH( REPLACE ( collaborators_name4, ',', '' ) ) + 1 )

    </select>

</mapper>
package com.wbyy.crm.modules.contracts.plannedreceivables.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.contracts.plannedreceivables.domain.PlannedReceivables;
import com.wbyy.crm.modules.contracts.plannedreceivables.mapper.PlannedReceivablesMapper;
import com.wbyy.crm.modules.contracts.plannedreceivables.service.PlannedReceivablesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 计划回款表服务接口实现
 *
 * <AUTHOR>
 * @since 2025-03-26 11:25:54
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("plannedReceivablesService")
public class PlannedReceivablesServiceImpl extends ServiceImpl<PlannedReceivablesMapper, PlannedReceivables> implements PlannedReceivablesService, BaseService {
    private final PlannedReceivablesMapper plannedReceivablesMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<PlannedReceivables> remoteDataList = list.stream().map(PlannedReceivables.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(PlannedReceivables::getId).collect(Collectors.toSet());
        List<PlannedReceivables> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(PlannedReceivables::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【PlannedReceivables】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【PlannedReceivables】落库数据数：{}",remoteDataList.size());
    }
}
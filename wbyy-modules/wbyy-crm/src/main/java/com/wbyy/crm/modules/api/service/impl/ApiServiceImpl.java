package com.wbyy.crm.modules.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wbyy.crm.modules.api.model.dto.TargetCompleteQueryDto;
import com.wbyy.crm.modules.api.model.vo.TargetCompleteVo;
import com.wbyy.crm.modules.api.service.ApiService;
import com.wbyy.crm.modules.business.salesopportunity.domain.SalesOpportunity;
import com.wbyy.crm.modules.business.salesopportunity.service.SalesOpportunityService;
import com.wbyy.crm.modules.contracts.actualreceivables.domain.ActualReceivables;
import com.wbyy.crm.modules.contracts.actualreceivables.service.ActualReceivablesService;
import com.wbyy.crm.modules.contracts.contract.domain.Contract;
import com.wbyy.crm.modules.contracts.contract.service.ContractService;
import com.wbyy.crm.modules.contracts.receivedamount.domain.ReceivedAmount;
import com.wbyy.crm.modules.contracts.receivedamount.service.ReceivedAmountService;
import com.wbyy.crm.modules.customers.customer.domain.Customer;
import com.wbyy.crm.modules.customers.customer.service.CustomerService;
import com.wbyy.crm.modules.customers.followup.domain.FollowUp;
import com.wbyy.crm.modules.customers.followup.service.FollowUpService;
import com.wbyy.crm.modules.customers.visitcheckin.domain.VisitCheckin;
import com.wbyy.crm.modules.customers.visitcheckin.service.VisitCheckinService;
import com.wbyy.crm.modules.salescenter.personalmonthlygoal.domain.PersonalMonthlyGoal;
import com.wbyy.crm.modules.salescenter.personalmonthlygoal.service.PersonalMonthlyGoalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.wbyy.crm.modules.api.constant.TargetCompleteConst.*;

@Service
@Slf4j
public class ApiServiceImpl implements ApiService {

    @Autowired
    private ContractService contractService;
    @Autowired
    private PersonalMonthlyGoalService personalMonthlyGoalService;
    @Autowired
    private ActualReceivablesService actualReceivablesService;
    @Autowired
    private VisitCheckinService visitCheckinService;
    @Autowired
    private SalesOpportunityService salesOpportunityService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private FollowUpService followUpService;
    @Autowired
    private ReceivedAmountService receivedAmountService;

    private static final BigDecimal PERCENT_100 = new BigDecimal("100");
    private static final BigDecimal MILLION = new BigDecimal("10000");
    private static final BigDecimal NEGATIVE = new BigDecimal("-1");

    @Override
    public List<TargetCompleteVo> targetComplete(TargetCompleteQueryDto dto) {
        Date now = new Date();
        DateTime startTime = DateUtil.beginOfYear(now);
        DateTime endTime = DateUtil.endOfYear(now);
        int year = DateUtil.year(now);
        // 要计算的用户
        Set<String> userNames = personalMonthlyGoalService.userNames(year + "年");
        // 所有合同数据
        List<Contract> contractList = contractService.listByContractDateRange(startTime, endTime);
        // 所有实际回款 回款切换实收金额
        List<ReceivedAmount> receivedAmountList = receivedAmountService.listByPaymentTimeRange(startTime, endTime);
        // 所有拜访签到
        List<VisitCheckin> visitCheckinList = visitCheckinService.listByCheckinTimeRange(startTime, endTime);
        // 所有个人月计划数据
        List<PersonalMonthlyGoal> personalMonthlyGoalList = personalMonthlyGoalService.listByYear(year + "年");
        // 所有新增销售机会
        List<SalesOpportunity> salesOpportunityList = salesOpportunityService.listByCreatedAtRange(startTime, endTime);
        // 所有客户数据
        List<Customer> customerList = customerService.listByCreatedAtRange(startTime, endTime);
        // 所有跟进数据
        List<FollowUp> followUpList = followUpService.listByCreatedRange(startTime, endTime);
        // 所有赢单
        List<SalesOpportunity> winList = salesOpportunityService.listWinByStageChangeTimeRange(startTime, endTime);
        // 新客id
        Set<String> userNewCustomerIdSet = customerService.listNewCustomers().stream().map(item->item.getId().toString()).collect(Collectors.toSet());
        List<TargetCompleteVo> result = new ArrayList<>();
        int month = DateUtil.month(now) + 1;
        for (String userName : userNames) {
            // 用户的合同
            List<Contract> userContractList = getUserContractList(contractList, userName);
            // 用户的月计划
            List<PersonalMonthlyGoal> userPersonalMonthlyGoalList = personalMonthlyGoalList.stream()
                    .filter(item -> userName.equals(item.getResponsiblePerson()))
                    .toList();
            // 用户的实际回款
            List<ReceivedAmount> userReceviedAmountList = receivedAmountList.stream()
                    .filter(item -> userName.equals(item.getPaymentChargerName()))
                    .toList();
            // 用户的拜访签到
            List<VisitCheckin> userVisitCheckinList = visitCheckinList.stream()
                    .filter(item -> userName.equals(item.getOwnerName()))
                    .toList();
            // 用户的销售机会
            List<SalesOpportunity> userSalesOpportunityList = salesOpportunityList.stream()
                    .filter(item -> userName.equals(item.getOwnerName()))
                    .toList();
            // 用户的新建客户
            List<Customer> userCustomerList = customerList.stream()
                    .filter(item -> userName.equals(item.getResponsiblePerson()))
                    .toList();
            // 用户的跟进次数
            List<FollowUp> userFollowUpList = followUpList.stream()
                    .filter(item -> userName.equals(item.getResponsiblePerson()))
                    .toList();
            // 用户的赢单
            List<SalesOpportunity> userWinList = winList.stream()
                    .filter(item -> userName.equals(item.getOwnerName()))
                    .toList();
            Date queryStartTime = new Date(startTime.getTime());
            int quarter = DateUtil.quarter(now);
            for (int i = 0; i < 13; i++) {
                if (i > month) break;
                if (i == 0) {
                    // 合同额
                    BigDecimal contractTotal = getContractTotal(userName, userContractList);
                    // 新客合同额
                    List<Contract> userCustomerContractList = userContractList.stream()
                            .filter(item -> userNewCustomerIdSet.contains(item.getCustomerId()))
                            .toList();
                    BigDecimal customerContractTotal = getContractTotal(userName, userCustomerContractList);
                    // 回款
                    BigDecimal receviablesTotal = userReceviedAmountList.stream()
                            .map(item -> Optional.ofNullable(item.getActualPayment()).orElse(BigDecimal.ZERO))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 赢单金额
                    BigDecimal winAmountTotal = userWinList.stream().map(item -> Optional.ofNullable(item.getAmount()).orElse(BigDecimal.ZERO))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 目标
                    List<PersonalMonthlyGoal> goalList = userPersonalMonthlyGoalList.stream().filter(item -> {
                        String s = item.getMonth().replaceAll("月", "");
                        return Integer.valueOf(s).compareTo(month) <= 0;
                    }).toList();
                    // 合同额-目标
                    BigDecimal contractTargetTotal = getGoalValue(goalList, GOAL_TYPE_CONTRACT_AMOUNT_TEXT);
                    result.add(new TargetCompleteVo(userName, CONTRACT_AMOUNT_TEXT, COMPLETE_TARGET_TEXT, contractTargetTotal.compareTo(NEGATIVE)==0?"-":getMillion(contractTargetTotal).toPlainString()));
                    // 拜访签到-目标
                    BigDecimal visitCheckinTargetTotal = getGoalValue(goalList, GOAL_TYPE_VISIT_CHECKIN_TEXT);
                    result.add(new TargetCompleteVo(userName, VISIT_CHECKIN_TEXT, COMPLETE_TARGET_TEXT, visitCheckinTargetTotal.compareTo(NEGATIVE)==0?"-":visitCheckinTargetTotal.toPlainString()));
                    // 新增销售机会-目标
                    BigDecimal salesOpportunityTargetTotal = getGoalValue(goalList, GOAL_TYPE_SALES_OPPORTUNITY_TEXT);
                    result.add(new TargetCompleteVo(userName, SALES_OPPORTUNITY_TEXT, COMPLETE_TARGET_TEXT, salesOpportunityTargetTotal.compareTo(NEGATIVE)==0?"-":salesOpportunityTargetTotal.toPlainString()));
                    // 新增客户-目标
                    BigDecimal customerTargetTotal = getGoalValue(goalList, GOAL_TYPE_CUSTOMER_TEXT);
                    result.add(new TargetCompleteVo(userName, CUSTOMER_TEXT, COMPLETE_TARGET_TEXT, customerTargetTotal.compareTo(NEGATIVE)==0?"-":customerTargetTotal.toPlainString()));
                    // 跟进次数-目标
                    BigDecimal followUpTargetTotal = getGoalValue(goalList, GOAL_TYPE_FOLLOW_UP_TEXT);
                    result.add(new TargetCompleteVo(userName, FOLLOW_UP_TEXT, COMPLETE_TARGET_TEXT, followUpTargetTotal.compareTo(NEGATIVE)==0?"-":followUpTargetTotal.toPlainString()));
                    // 回款-目标
                    BigDecimal receviablesTargetTotal = getGoalValue(goalList, GOAL_TYPE_RECEVIABLES_AMOUNT_TEXT);
                    result.add(new TargetCompleteVo(userName, RECEVIABLES_AMOUNT_TEXT, COMPLETE_TARGET_TEXT, receviablesTargetTotal.compareTo(NEGATIVE)==0?"-":getMillion(receviablesTargetTotal).toPlainString()));
                    // 赢单数-目标
                    BigDecimal winCountTargetTotal = getGoalValue(goalList, GOAL_TYPE_WIN_COUNT_TEXT);
                    result.add(new TargetCompleteVo(userName, WIN_COUNT_TEXT, COMPLETE_TARGET_TEXT, winCountTargetTotal.compareTo(NEGATIVE)==0?"-":winCountTargetTotal.toPlainString()));
                    // 赢单金额-目标
                    BigDecimal winAmountTargetTotal = getGoalValue(goalList, GOAL_TYPE_WIN_AMOUNT_TEXT);
                    result.add(new TargetCompleteVo(userName, WIN_AMOUNT_TEXT, COMPLETE_TARGET_TEXT, winAmountTargetTotal.compareTo(NEGATIVE)==0?"-":getMillion(winAmountTargetTotal).toPlainString()));
                    // 完成
                    {
                        // 合同额-完成情况
                        result.add(new TargetCompleteVo(userName, CONTRACT_AMOUNT_TEXT, COMPLETE_TEXT,
                                getMillion(contractTotal).toPlainString() +
                                        "/" +
                                        getMillion(customerContractTotal).toPlainString()));
                        // 拜访签到-完成情况
                        result.add(new TargetCompleteVo(userName, VISIT_CHECKIN_TEXT, COMPLETE_TEXT, String.valueOf(userVisitCheckinList.size())));
                        // 新增销售机会-完成情况
                        result.add(new TargetCompleteVo(userName, SALES_OPPORTUNITY_TEXT, COMPLETE_TEXT, String.valueOf(userSalesOpportunityList.size())));
                        // 新增客户-完成情况
                        result.add(new TargetCompleteVo(userName, CUSTOMER_TEXT, COMPLETE_TEXT, String.valueOf(userCustomerList.size())));
                        // 跟进次数-完成情况
                        result.add(new TargetCompleteVo(userName, FOLLOW_UP_TEXT, COMPLETE_TEXT, String.valueOf(userFollowUpList.size())));
                        // 回款-完成情况
                        result.add(new TargetCompleteVo(userName, RECEVIABLES_AMOUNT_TEXT, COMPLETE_TEXT, getMillion(receviablesTotal).toPlainString()));
                        // 赢单数-完成情况
                        result.add(new TargetCompleteVo(userName, WIN_COUNT_TEXT, COMPLETE_TEXT, String.valueOf(userWinList.size())));
                        // 赢单金额-完成情况
                        result.add(new TargetCompleteVo(userName, WIN_AMOUNT_TEXT, COMPLETE_TEXT, getMillion(winAmountTotal).toPlainString()));
                    }
                    // 完成率
                    // 目标
                    // 合同额-目标
                    result.add(new TargetCompleteVo(userName, CONTRACT_AMOUNT_TEXT, COMPLETE_PERCENT_TEXT, getPercent(contractTotal, contractTargetTotal)));
                    // 拜访签到-目标
                    result.add(new TargetCompleteVo(userName, VISIT_CHECKIN_TEXT, COMPLETE_PERCENT_TEXT, getPercent(BigDecimal.valueOf(userVisitCheckinList.size()), visitCheckinTargetTotal)));
                    // 新增销售机会-目标
                    result.add(new TargetCompleteVo(userName, SALES_OPPORTUNITY_TEXT, COMPLETE_PERCENT_TEXT, getPercent(BigDecimal.valueOf(userSalesOpportunityList.size()), salesOpportunityTargetTotal)));
                    // 新增客户-目标
                    result.add(new TargetCompleteVo(userName, CUSTOMER_TEXT, COMPLETE_PERCENT_TEXT, getPercent(BigDecimal.valueOf(userCustomerList.size()), customerTargetTotal)));
                    // 跟进次数-目标
                    result.add(new TargetCompleteVo(userName, FOLLOW_UP_TEXT, COMPLETE_PERCENT_TEXT, getPercent(BigDecimal.valueOf(userFollowUpList.size()), followUpTargetTotal)));
                    // 回款-目标
                    result.add(new TargetCompleteVo(userName, RECEVIABLES_AMOUNT_TEXT, COMPLETE_PERCENT_TEXT,receviablesTargetTotal.compareTo(BigDecimal.ZERO)<0?"-":getPercent(receviablesTotal, receviablesTargetTotal)));
                    // 赢单数-目标
                    result.add(new TargetCompleteVo(userName, WIN_COUNT_TEXT, COMPLETE_PERCENT_TEXT,winCountTargetTotal.compareTo(BigDecimal.ZERO)<0?"-":getPercent(BigDecimal.valueOf(userWinList.size()), winCountTargetTotal)));
                    // 赢单金额-目标
                    result.add(new TargetCompleteVo(userName, WIN_AMOUNT_TEXT, COMPLETE_PERCENT_TEXT,winAmountTargetTotal.compareTo(BigDecimal.ZERO)<0?"-":getPercent(winAmountTotal, winAmountTargetTotal)));
                    // 季度
                    {
                        for (int i1 = quarter; i1 > 0; i1--) {
                            Date quarterStart = getQuarterStart(year, i1);
                            Date quarterEnd = getQuarterEnd(year, i1);
                            int startMonth = DateUtil.month(quarterStart) + 1;
                            int endMonth = DateUtil.month(quarterEnd) + 1;
                            // 用户月合同
                            List<Contract> filterContractList = userContractList.stream()
                                    .filter(item -> item.getContractDate().compareTo(quarterStart) >= 0
                                            && item.getContractDate().compareTo(quarterEnd) <= 0)
                                    .toList();
                            BigDecimal contract = getContractTotal(userName, filterContractList);
                            // 用户月回款
                            List<ReceivedAmount> filterActualReceviablesList = userReceviedAmountList.stream()
                                    .filter(item -> item.getPaymentTime().compareTo(quarterStart) >= 0
                                            && item.getPaymentTime().compareTo(quarterEnd) <= 0)
                                    .toList();
                            BigDecimal receviables = getReceviablesTotal(filterActualReceviablesList);
                            // 用户月拜访签到
                            List<VisitCheckin> filterVisiCheckinList = userVisitCheckinList.stream()
                                    .filter(item -> item.getCheckinTime().compareTo(quarterStart) >= 0
                                            && item.getCheckinTime().compareTo(quarterEnd) <= 0)
                                    .toList();
                            // 用户月新增销售机会
                            List<SalesOpportunity> filterSalesOpportunityList = userSalesOpportunityList.stream()
                                    .filter(item -> item.getCreatedAt().compareTo(quarterStart) >= 0
                                            && item.getCreatedAt().compareTo(quarterEnd) <= 0)
                                    .toList();
                            // 用户月新建客户
                            List<Customer> filterCustomerList = userCustomerList.stream()
                                    .filter(item -> item.getCreatedAt().compareTo(quarterStart) >= 0
                                            && item.getCreatedAt().compareTo(quarterEnd) <= 0)
                                    .toList();
                            // 用户月跟进次数
                            List<FollowUp> filterFollowUpList = userFollowUpList.stream()
                                    .filter(item -> item.getCreated().compareTo(quarterStart) >= 0
                                            && item.getCreated().compareTo(quarterEnd) <= 0)
                                    .toList();
                            // 用户月赢单
                            List<SalesOpportunity> filterWinList = userWinList.stream()
                                    .filter(item -> item.getStageChangeTime().compareTo(quarterStart) >= 0
                                            && item.getStageChangeTime().compareTo(quarterEnd) <= 0)
                                    .toList();
                            // 用户月赢单金额
                            BigDecimal winAmount = filterWinList.stream().map(item -> Optional.ofNullable(item.getAmount()).orElse(BigDecimal.ZERO))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            // 合同额
                            BigDecimal targetContract = BigDecimal.ZERO;
                            // 回款
                            BigDecimal targetReceviables = BigDecimal.ZERO;
                            // 拜访签到
                            BigDecimal targetVisit = BigDecimal.ZERO;
                            // 新增销售机会
                            BigDecimal targetSale = BigDecimal.ZERO;
                            // 新增客户
                            BigDecimal targetCustomer = BigDecimal.ZERO;
                            // 跟进次数
                            BigDecimal targetFollow = BigDecimal.ZERO;
                            // 赢单金额
                            BigDecimal targetWinAmount = BigDecimal.ZERO;
                            // 赢单数
                            BigDecimal targetWinCount = BigDecimal.ZERO;
                            for (int i2 = startMonth; i2 <= endMonth; i2++) {
                                String thisMonth = i2 + "月";
                                List<PersonalMonthlyGoal> monthlyGoalList = userPersonalMonthlyGoalList.stream()
                                        .filter(item -> thisMonth.equals(item.getMonth()))
                                        .toList();
                                // 合同额
                                targetContract = targetContract.add(getGoalValue(monthlyGoalList, GOAL_TYPE_CONTRACT_AMOUNT_TEXT));
                                // 回款
                                targetReceviables = targetReceviables.add(getGoalValue(goalList, GOAL_TYPE_RECEVIABLES_AMOUNT_TEXT));
                                // 拜访签到
                                targetVisit = targetVisit.add(getGoalValue(monthlyGoalList, GOAL_TYPE_VISIT_CHECKIN_TEXT));
                                // 新增销售机会
                                targetSale = targetSale.add(getGoalValue(monthlyGoalList, GOAL_TYPE_SALES_OPPORTUNITY_TEXT));
                                // 新增客户
                                targetCustomer = targetCustomer.add(getGoalValue(monthlyGoalList, GOAL_TYPE_CUSTOMER_TEXT));
                                // 跟进次数
                                targetFollow = targetFollow.add(getGoalValue(monthlyGoalList, GOAL_TYPE_FOLLOW_UP_TEXT));
                                // 赢单金额
                                targetWinAmount = targetWinAmount.add(getGoalValue(monthlyGoalList,GOAL_TYPE_WIN_AMOUNT_TEXT));
                                // 赢单数
                                targetWinCount = targetWinCount.add(getGoalValue(monthlyGoalList,GOAL_TYPE_WIN_COUNT_TEXT));
                            }
                            // 合同额-目标
                            result.add(new TargetCompleteVo(userName, CONTRACT_AMOUNT_TEXT, i1 + QUARTER_TEXT,targetContract.compareTo(BigDecimal.ZERO)<0?"-":getPercent(contract, targetContract)));
                            // 拜访签到-目标
                            result.add(new TargetCompleteVo(userName, VISIT_CHECKIN_TEXT, i1 + QUARTER_TEXT, targetVisit.compareTo(BigDecimal.ZERO)<0?"-":getPercent(BigDecimal.valueOf(filterVisiCheckinList.size()), targetVisit)));
                            // 新增销售机会-目标
                            result.add(new TargetCompleteVo(userName, SALES_OPPORTUNITY_TEXT, i1 + QUARTER_TEXT, targetSale.compareTo(BigDecimal.ZERO)<0?"-":getPercent(BigDecimal.valueOf(filterSalesOpportunityList.size()), targetSale)));
                            // 新增客户-目标
                            result.add(new TargetCompleteVo(userName, CUSTOMER_TEXT, i1 + QUARTER_TEXT, targetCustomer.compareTo(BigDecimal.ZERO)<0?"-":getPercent(BigDecimal.valueOf(filterCustomerList.size()), targetCustomer)));
                            // 跟进次数-目标
                            result.add(new TargetCompleteVo(userName, FOLLOW_UP_TEXT, i1 + QUARTER_TEXT, targetFollow.compareTo(BigDecimal.ZERO)<0?"-":getPercent(BigDecimal.valueOf(filterFollowUpList.size()), targetFollow)));
                            // 回款-目标
                            result.add(new TargetCompleteVo(userName, RECEVIABLES_AMOUNT_TEXT, i1 + QUARTER_TEXT, targetReceviables.compareTo(BigDecimal.ZERO)<0?"-":getPercent(receviables, targetReceviables)));
                            // 赢单数-目标
                            result.add(new TargetCompleteVo(userName, WIN_COUNT_TEXT, i1 + QUARTER_TEXT, targetWinCount.compareTo(BigDecimal.ZERO)<0?"-":getPercent(BigDecimal.valueOf(filterWinList.size()), targetWinCount)));
                            // 赢单金额-目标
                            result.add(new TargetCompleteVo(userName, WIN_AMOUNT_TEXT, i1 + QUARTER_TEXT, targetWinAmount.compareTo(BigDecimal.ZERO)<0?"-":getPercent(winAmount, targetWinAmount)));
                        }
                    }
                } else {
                    Date queryEndTime = DateUtil.offsetMonth(queryStartTime, 1);
                    Date finalQueryStartTime = queryStartTime;
                    // 用户月合同
                    List<Contract> filterContractList = userContractList.stream()
                            .filter(item -> item.getContractDate().compareTo(finalQueryStartTime) >= 0
                                    && item.getContractDate().compareTo(queryEndTime) < 0)
                            .toList();
                    // 用户月回款
                    List<ReceivedAmount> filterActualReceviablesList = userReceviedAmountList.stream()
                            .filter(item -> item.getPaymentTime().compareTo(finalQueryStartTime) >= 0
                                    && item.getPaymentTime().compareTo(queryEndTime) < 0)
                            .toList();
                    // 用户月拜访签到
                    List<VisitCheckin> filterVisiCheckinList = userVisitCheckinList.stream()
                            .filter(item -> item.getCheckinTime().compareTo(finalQueryStartTime) >= 0
                                    && item.getCheckinTime().compareTo(queryEndTime) < 0)
                            .toList();
                    // 用户月新增销售机会
                    List<SalesOpportunity> filterSalesOpportunityList = userSalesOpportunityList.stream()
                            .filter(item -> item.getCreatedAt().compareTo(finalQueryStartTime) >= 0
                                    && item.getCreatedAt().compareTo(queryEndTime) < 0)
                            .toList();
                    // 用户月新建客户
                    List<Customer> filterCustomerList = userCustomerList.stream()
                            .filter(item -> item.getCreatedAt().compareTo(finalQueryStartTime) >= 0
                                    && item.getCreatedAt().compareTo(queryEndTime) < 0)
                            .toList();
                    // 用户月跟进次数
                    List<FollowUp> filterFollowUpList = userFollowUpList.stream()
                            .filter(item -> item.getCreated().compareTo(finalQueryStartTime) >= 0
                                    && item.getCreated().compareTo(queryEndTime) < 0)
                            .toList();
                    // 用户月赢单
                    List<SalesOpportunity> filterWinList = userWinList.stream()
                            .filter(item -> item.getStageChangeTime().compareTo(finalQueryStartTime) >= 0
                                    && item.getStageChangeTime().compareTo(queryEndTime) < 0)
                            .toList();
                    // 用户月赢单金额
                    BigDecimal filterWinAmount = filterWinList.stream().map(item -> Optional.ofNullable(item.getAmount()).orElse(BigDecimal.ZERO))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 用户月目标
                    int finalI = i;
                    List<PersonalMonthlyGoal> filterPersonalMonthlyGoalList = userPersonalMonthlyGoalList.stream()
                            .filter(item -> (finalI + "月").equals(item.getMonth()))
                            .toList();
                    {
                        // 合同额-月目标
                        BigDecimal targetContract = getGoalValue(filterPersonalMonthlyGoalList, GOAL_TYPE_CONTRACT_AMOUNT_TEXT);
                        result.add(new TargetCompleteVo(userName, CONTRACT_AMOUNT_TEXT, i + MONTH_COMPLETE_TARGET_TEXT,targetContract.compareTo(NEGATIVE)==0?"-":getMillion(targetContract).toPlainString()));
                        // 合同额-月完成情况
                        BigDecimal contractTotal = getContractTotal(userName, filterContractList);
                        List<Contract> filterUserCustomerContractList = filterContractList.stream()
                                .filter(item -> userNewCustomerIdSet.contains(item.getCustomerId()))
                                .toList();
                        BigDecimal customerContractTotal = getContractTotal(userName, filterUserCustomerContractList);
                        result.add(new TargetCompleteVo(userName, CONTRACT_AMOUNT_TEXT, i + MONTH_COMPLETE_TEXT, getMillion(contractTotal).toPlainString() + "/" + getMillion(customerContractTotal).toPlainString()));
                    }
                    {
                        // 拜访签到-月目标
                        BigDecimal targetVisited = getGoalValue(filterPersonalMonthlyGoalList, GOAL_TYPE_VISIT_CHECKIN_TEXT);
                        result.add(new TargetCompleteVo(userName, VISIT_CHECKIN_TEXT, i + MONTH_COMPLETE_TARGET_TEXT,targetVisited.compareTo(NEGATIVE)==0?"-":targetVisited.toPlainString()));
                        // 拜访签到-月完成情况
                        result.add(new TargetCompleteVo(userName, VISIT_CHECKIN_TEXT, i + MONTH_COMPLETE_TEXT, String.valueOf(filterVisiCheckinList.size())));
                    }
                    {
                        // 新增销售机会-月目标
                        BigDecimal targetSales = getGoalValue(filterPersonalMonthlyGoalList, GOAL_TYPE_SALES_OPPORTUNITY_TEXT);
                        result.add(new TargetCompleteVo(userName, SALES_OPPORTUNITY_TEXT, i + MONTH_COMPLETE_TARGET_TEXT,targetSales.compareTo(NEGATIVE)==0?"-":targetSales.toPlainString()));
                        // 新增销售机会-月完成情况
                        result.add(new TargetCompleteVo(userName, SALES_OPPORTUNITY_TEXT, i + MONTH_COMPLETE_TEXT, String.valueOf(filterSalesOpportunityList.size())));
                    }
                    {
                        // 新增建客户-月目标
                        BigDecimal targetCustomer = getGoalValue(filterPersonalMonthlyGoalList, GOAL_TYPE_CUSTOMER_TEXT);
                        result.add(new TargetCompleteVo(userName, CUSTOMER_TEXT, i + MONTH_COMPLETE_TARGET_TEXT,targetCustomer.compareTo(NEGATIVE)==0?"-":targetCustomer.toPlainString()));
                        // 新增建客户-月完成情况
                        result.add(new TargetCompleteVo(userName, CUSTOMER_TEXT, i + MONTH_COMPLETE_TEXT, String.valueOf(filterCustomerList.size())));
                    }
                    {
                        // 跟进次数-月目标
                        BigDecimal targetFollowUp = getGoalValue(filterPersonalMonthlyGoalList, GOAL_TYPE_FOLLOW_UP_TEXT);
                        result.add(new TargetCompleteVo(userName, FOLLOW_UP_TEXT, i + MONTH_COMPLETE_TARGET_TEXT,targetFollowUp.compareTo(NEGATIVE)==0?"-":targetFollowUp.toPlainString()));
                        // 跟进次数-月完成情况
                        result.add(new TargetCompleteVo(userName, FOLLOW_UP_TEXT, i + MONTH_COMPLETE_TEXT, String.valueOf(filterFollowUpList.size())));
                    }
                    {
                        // 回款-月目标
                        BigDecimal targetReceviables = getGoalValue(filterPersonalMonthlyGoalList, GOAL_TYPE_RECEVIABLES_AMOUNT_TEXT);
                        result.add(new TargetCompleteVo(userName, RECEVIABLES_AMOUNT_TEXT, i + MONTH_COMPLETE_TARGET_TEXT,targetReceviables.compareTo(NEGATIVE)==0?"-":getMillion(targetReceviables).toPlainString()));
                        // 回款-月完成情况
                        result.add(new TargetCompleteVo(userName, RECEVIABLES_AMOUNT_TEXT, i + MONTH_COMPLETE_TEXT, getMillion(getReceviablesTotal(filterActualReceviablesList)).toPlainString()));
                    }
                    {
                        // 赢单数-月目标
                        BigDecimal targetWinCount = getGoalValue(filterPersonalMonthlyGoalList, GOAL_TYPE_WIN_COUNT_TEXT);
                        result.add(new TargetCompleteVo(userName, WIN_COUNT_TEXT, i + MONTH_COMPLETE_TARGET_TEXT,targetWinCount.compareTo(NEGATIVE)==0?"-":targetWinCount.toPlainString()));
                        // 赢单数-月完成情况
                        result.add(new TargetCompleteVo(userName, WIN_COUNT_TEXT, i + MONTH_COMPLETE_TEXT, String.valueOf(filterWinList.size())));
                    }
                    {
                        // 赢单金额-月目标
                        BigDecimal targetWinAmount = getGoalValue(filterPersonalMonthlyGoalList, GOAL_TYPE_WIN_AMOUNT_TEXT);
                        result.add(new TargetCompleteVo(userName, WIN_AMOUNT_TEXT, i + MONTH_COMPLETE_TARGET_TEXT,targetWinAmount.compareTo(NEGATIVE)==0?"-":getMillion(targetWinAmount).toPlainString()));
                        // 赢单金额-月完成情况
                        result.add(new TargetCompleteVo(userName, WIN_AMOUNT_TEXT, i + MONTH_COMPLETE_TEXT, getMillion(filterWinAmount).toPlainString()));
                    }
                    queryStartTime = queryEndTime;
                }
            }
        }
        Map<String, List<TargetCompleteVo>> groupByUser = result.stream().collect(Collectors.groupingBy(TargetCompleteVo::getUserName));
        List<TargetCompleteVo> monthSortList = new ArrayList<>();
        groupByUser.forEach((userName, entries) -> {
            entries.sort((o1, o2) -> {
                String o1TargetMonth = o1.getTargetMonth();
                BigDecimal o1Value;
                BigDecimal subtract = MAX_VALUE.subtract(BigDecimal.ONE).subtract(BigDecimal.ONE).subtract(BigDecimal.ONE);
                BigDecimal subtracted = subtract.subtract(BigDecimal.ONE).subtract(BigDecimal.ONE).subtract(BigDecimal.ONE);
                if (COMPLETE_TARGET_TEXT.equals(o1TargetMonth)) {
                    o1Value = MAX_VALUE;
                } else if (COMPLETE_TEXT.equals(o1TargetMonth)) {
                    o1Value = MAX_VALUE.subtract(BigDecimal.ONE);
                } else if (COMPLETE_PERCENT_TEXT.equals(o1TargetMonth)) {
                    o1Value = MAX_VALUE.subtract(BigDecimal.ONE).subtract(BigDecimal.ONE);
                } else if (QUARTER_4_TEXT.equals(o1TargetMonth)) {
                    o1Value = subtract;
                } else if (QUARTER_3_TEXT.equals(o1TargetMonth)) {
                    o1Value = subtract.subtract(BigDecimal.ONE);
                } else if (QUARTER_2_TEXT.equals(o1TargetMonth)) {
                    o1Value = subtract.subtract(BigDecimal.ONE).subtract(BigDecimal.ONE);
                } else if (QUARTER_1_TEXT.equals(o1TargetMonth)) {
                    o1Value = subtracted;
                } else {
                    o1Value = new BigDecimal(o1TargetMonth.replaceAll(MONTH_COMPLETE_TEXT, "").replaceAll(MONTH_COMPLETE_TARGET_TEXT, ""));
                }
                String o2TargetMonth = o2.getTargetMonth();
                BigDecimal o2Value;
                if (COMPLETE_TARGET_TEXT.equals(o2TargetMonth)) {
                    o2Value = MAX_VALUE;
                } else if (COMPLETE_TEXT.equals(o2TargetMonth)) {
                    o2Value = MAX_VALUE.subtract(BigDecimal.ONE);
                } else if (COMPLETE_PERCENT_TEXT.equals(o2TargetMonth)) {
                    o2Value = MAX_VALUE.subtract(BigDecimal.ONE).subtract(BigDecimal.ONE);
                } else if (QUARTER_4_TEXT.equals(o2TargetMonth)) {
                    o2Value = subtract;
                } else if (QUARTER_3_TEXT.equals(o2TargetMonth)) {
                    o2Value = subtract.subtract(BigDecimal.ONE);
                } else if (QUARTER_2_TEXT.equals(o2TargetMonth)) {
                    o2Value = subtract.subtract(BigDecimal.ONE).subtract(BigDecimal.ONE);
                } else if (QUARTER_1_TEXT.equals(o2TargetMonth)) {
                    o2Value = subtracted;
                } else {
                    o2Value = new BigDecimal(o2TargetMonth.replaceAll(MONTH_COMPLETE_TEXT, "").replaceAll(MONTH_COMPLETE_TARGET_TEXT, ""));
                }
                return o2Value.compareTo(o1Value);
            });
            monthSortList.addAll(entries);
        });
        if (null != dto && StrUtil.isNotBlank(dto.getField())) {
            Map<String, List<TargetCompleteVo>> monthSortedGroupByUser = monthSortList.stream().collect(Collectors.groupingBy(TargetCompleteVo::getUserName));
            Map<String, BigDecimal> valueMap = new HashMap<>();
            monthSortedGroupByUser.forEach((userName, entries) -> {
                BigDecimal value = entries.stream()
                        .filter(item -> COMPLETE_PERCENT_TEXT.equals(item.getTargetMonth()) && item.getTargetType().equals(dto.getField()))
                        .map(item -> {
                            if (item.getTargetValue().equals("-")) {
                                return BigDecimal.ZERO;
                            }
                            return new BigDecimal(item.getTargetValue().replaceAll("%", ""));
                        })
                        .findFirst().orElse(BigDecimal.ZERO);
                valueMap.put(userName, value);
            });
            List<String> sortedUsers = new ArrayList<>(valueMap.keySet());
            sortedUsers.sort((u1, u2) -> {
                BigDecimal val1 = valueMap.get(u1);
                BigDecimal val2 = valueMap.get(u2);
                return dto.getAscending() ? val1.compareTo(val2) : val2.compareTo(val1);
            });
            List<TargetCompleteVo> typeSortResult = new ArrayList<>();
            for (String user : sortedUsers) {
                typeSortResult.addAll(monthSortedGroupByUser.get(user));
            }
            return typeSortResult;
        } else {
            return monthSortList;
        }


    }

    private Date getQuarterStart(int year, int quarter) {
        String dateTimeStr = switch (quarter) {
            case 1 -> year + "-01-01 00:00:00";
            case 2 -> year + "-04-01 00:00:00";
            case 3 -> year + "-07-01 00:00:00";
            default -> year + "-10:-01 00:00:00";
        };
        return DateUtil.parse(dateTimeStr, DatePattern.NORM_DATETIME_FORMAT);
    }

    private Date getQuarterEnd(int year, int quarter) {
        String dateTimeStr = switch (quarter) {
            case 1 -> year + "-03-31 23:59:59";
            case 2 -> year + "-06-30 23:59:59";
            case 3 -> year + "-09-30 23:59:59";
            default -> year + "-12-31 23:59:59";
        };
        return DateUtil.parse(dateTimeStr, DatePattern.NORM_DATETIME_FORMAT);
    }

    private String getPercent(BigDecimal value1, BigDecimal value2) {
        return BigDecimal.ZERO.compareTo(value2) != 0 ? value1.divide(value2, 2, RoundingMode.HALF_UP).multiply(PERCENT_100).toPlainString() + "%" : "-";
    }

    private BigDecimal getMillion(BigDecimal value) {
        return value.divide(MILLION, 2, RoundingMode.HALF_UP);
    }

    private BigDecimal getGoalValue(List<PersonalMonthlyGoal> personalMonthlyGoalList, String type) {
        List<BigDecimal> valueList = personalMonthlyGoalList.stream().filter(item -> type.equals(item.getType()))
                .map(PersonalMonthlyGoal::getValue)
                .filter(Objects::nonNull)
                .toList();
        if (CollectionUtil.isEmpty(valueList)) return NEGATIVE;
        return valueList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getReceviablesTotal(List<ReceivedAmount> actualReceivablesList) {
        return actualReceivablesList.stream()
                .map(item -> Optional.ofNullable(item.getActualPayment()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private List<Contract> getUserContractList(List<Contract> contractList, String userName) {
        return contractList.stream().filter(item -> userName.equals(item.getResponsiblePerson())
                        || userName.equals(item.getCollaboratorsName1())
                        || userName.equals(item.getCollaboratorsName2())
                        || userName.equals(item.getCollaboratorsName3())
                        || userName.equals(item.getCollaboratorsName4())
                        || userName.equals(item.getCollaboratorsName5())
                        || userName.equals(item.getCollaboratorsName6())
                        || userName.equals(item.getCollaboratorsName7())
                        || userName.equals(item.getCollaboratorsName8())
                        || userName.equals(item.getCollaboratorsName9())
                        || userName.equals(item.getCollaboratorsName10())
                        || userName.equals(item.getCollaboratorsName11())
                        || userName.equals(item.getCollaboratorsName12())
                        || userName.equals(item.getCollaboratorsName13())
                        || userName.equals(item.getCollaboratorsName14())
                        || userName.equals(item.getCollaboratorsName15())
                        || userName.equals(item.getCollaboratorsName16())
                )
                .toList();
    }

    private BigDecimal getContractTotal(String userName, List<Contract> contractList) {
        BigDecimal contractTotal = BigDecimal.ZERO;
        for (Contract contract : contractList) {
            BigDecimal contractAmount = Optional.ofNullable(contract.getContractAmount()).orElse(BigDecimal.ZERO);
            // 计算占比
            BigDecimal userPercent = BigDecimal.ZERO;
            // 协作人
            if (userName.equals(contract.getCollaboratorsName1())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent1()).orElse(BigDecimal.ZERO);
            } else if (userName.equals(contract.getCollaboratorsName2())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent2()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName3())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent3()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName4())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent4()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName5())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent5()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName6())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent6()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName7())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent7()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName8())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent8()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName9())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent9()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName10())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent10()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName11())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent11()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName12())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent12()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName13())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent13()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName14())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent14()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName15())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent15()).orElse(BigDecimal.ZERO);
            }
            else if (userName.equals(contract.getCollaboratorsName16())) {
                userPercent = Optional.ofNullable(contract.getCollaboratorsPercent16()).orElse(BigDecimal.ZERO);
            }

            else if (userName.equals(contract.getResponsiblePerson())) {
                // 责任人
                userPercent = PERCENT_100.subtract(Optional.ofNullable(contract.getCollaboratorsPercent1()).orElse(BigDecimal.ZERO))
                        .subtract(Optional.ofNullable(contract.getCollaboratorsPercent2()).orElse(BigDecimal.ZERO))
                        .subtract(Optional.ofNullable(contract.getCollaboratorsPercent3()).orElse(BigDecimal.ZERO))
                        .subtract(Optional.ofNullable(contract.getCollaboratorsPercent4()).orElse(BigDecimal.ZERO));
            }
            contractTotal = contractTotal.add(contractAmount.multiply(userPercent).divide(PERCENT_100, 4, RoundingMode.HALF_UP));
        }
        return contractTotal;
    }
}

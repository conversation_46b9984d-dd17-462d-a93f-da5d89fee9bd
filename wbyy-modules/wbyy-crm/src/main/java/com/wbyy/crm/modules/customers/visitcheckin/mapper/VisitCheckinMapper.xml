<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.crm.modules.customers.visitcheckin.mapper.VisitCheckinMapper">
    <sql id="tableName">
        visit_checkin
    </sql>

    <sql id="baseColumn">
        id,visit_plan_id,contact_id,contact_name,visit_content,checkin_time,visit_status,checkin_address,owner_name,owner_id,updated_at,created_at,updated_by,created_by
    </sql>

</mapper>
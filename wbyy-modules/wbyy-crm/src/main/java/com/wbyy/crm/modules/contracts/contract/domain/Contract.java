package com.wbyy.crm.modules.contracts.contract.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.math.BigDecimal;
import java.util.Date;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 合同基本信息表(contract)实体类
 *
 * <AUTHOR>
 * @since 2025-03-26 11:03:37
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("contract")
public class Contract extends BaseDomain {

    /**
     * 合同id
     */


    /**
     * 所属部门
     */
    @CrmMapping("refer_object_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String department;

    /**
     * 所属部门id
     */
    @CrmMapping("refer_object_id_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String departmentId;

    /**
     * 合同编号
     */
    @CrmMapping("input_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractNo;

    /**
     * 合同名称
     */
    @CrmMapping("input_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractName;

    /**
     * 客户名称
     */
    @CrmMapping("refer_object_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customerName;

    /**
     * 关联客户id
     */
    @CrmMapping("refer_object_id_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customerId;

    /**
     * 项目编号（数据编号）
     */
    @CrmMapping("input_2")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String projectNo;

    /**
     * 合同金额
     */
    @CrmMapping("amount_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal contractAmount;

    /**
     * 业务类型
     */
    @CrmMapping("input_3")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String businessType;

    /**
     * 注册分类
     */
    @CrmMapping("input_4")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String registrationType;

    /**
     * 合同类型
     */
    @CrmMapping("input_5")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractType;

    /**
     * 单例价格（万元）
     */
    @CrmMapping("amount_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal unitPrice;

    /**
     * 关联销售机会
     */
    @CrmMapping("refer_object_2")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String opportunityName;

    /**
     * 销售机会id
     */
    @CrmMapping("refer_object_id_2")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String opportunityId;

    /**
     * 销售机会编号
     */
    @CrmMapping("refer_field_txt_3")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String opportunityNo;

    /**
     * 合同订单编号
     */
    @CrmMapping("relation_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String internalContractNo;

    /**
     * 负责人
     */
    @CrmMapping("charger_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String responsiblePerson;

    /**
     * 负责人id
     */
    @CrmMapping("charger_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String responsiblePersonId;

    /**
     * 负责人所在部门
     */
    @CrmMapping("charger_department_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerDepartment;

    /**
     * 负责人所在部门id
     */
    @CrmMapping("charger_department_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerDepartmentId;

    /**
     * 协作人
     */
    @CrmMapping("collaborator_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName;

    /**
     * 协作人id
     */
    @CrmMapping("collaborator_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsId;

    /**
     * 创建人id
     */
    @CrmMapping("creator_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String createdById;

    /**
     * 最后修改人id
     */
    @CrmMapping("changer_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String updatedById;

    /**
     * 协作人1
     */
    @CrmMapping("input_7")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName1;

    /**
     * 协作人贡献百分比1
     */
    @CrmMapping("percent_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent1;

    /**
     * 协作人2
     */
    @CrmMapping("input_8")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName2;

    /**
     * 协作人贡献百分比2
     */
    @CrmMapping("percent_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent2;

    /**
     * 协作人3
     */
    @CrmMapping("input_9")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName3;

    /**
     * 协作人贡献百分比3
     */
    @CrmMapping("percent_2")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent3;

    /**
     * 协作人4
     */
    @CrmMapping("input_10")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName4;

    /**
     * 协作人贡献百分比4
     */
    @CrmMapping("percent_3")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent4;

    /**
     * 协作人5
     */
    @CrmMapping("input_11")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName5;

    /**
     * 协作人贡献百分比5
     */
    @CrmMapping("percent_4")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent5;

    /**
     * 协作人6
     */
    @CrmMapping("input_12")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName6;

    /**
     * 协作人贡献百分比6
     */
    @CrmMapping("percent_5")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent6;

    /**
     * 协作人7
     */
    @CrmMapping("input_13")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName7;

    /**
     * 协作人贡献百分比7
     */
    @CrmMapping("percent_6")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent7;

    /**
     * 协作人8
     */
    @CrmMapping("input_14")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName8;

    /**
     * 协作人贡献百分比8
     */
    @CrmMapping("percent_7")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent8;

    /**
     * 协作人9
     */
    @CrmMapping("input_15")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName9;

    /**
     * 协作人贡献百分比9
     */
    @CrmMapping("percent_8")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent9;

    /**
     * 协作人10
     */
    @CrmMapping("input_16")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName10;

    /**
     * 协作人贡献百分比10
     */
    @CrmMapping("percent_9")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent10;

    /**
     * 协作人11
     */
    @CrmMapping("input_17")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName11;

    /**
     * 协作人贡献百分比11
     */
    @CrmMapping("percent_10")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent11;

    /**
     * 协作人12
     */
    @CrmMapping("input_18")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName12;

    /**
     * 协作人贡献百分比12
     */
    @CrmMapping("percent_11")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent12;

    /**
     * 协作人13
     */
    @CrmMapping("input_19")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName13;

    /**
     * 协作人贡献百分比13
     */
    @CrmMapping("percent_12")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent13;

    /**
     * 协作人14
     */
    @CrmMapping("input_20")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName14;

    /**
     * 协作人贡献百分比14
     */
    @CrmMapping("percent_13")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent14;

    /**
     * 协作人15
     */
    @CrmMapping("input_21")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName15;

    /**
     * 协作人贡献百分比15
     */
    @CrmMapping("percent_14")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent15;

    /**
     * 协作人16
     */
    @CrmMapping("input_22")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName16;

    /**
     * 协作人贡献百分比16
     */
    @CrmMapping("percent_15")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal collaboratorsPercent16;

    @CrmMapping("date_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date contractDate;

    private String bizType;
}
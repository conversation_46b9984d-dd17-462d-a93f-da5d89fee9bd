package com.wbyy.crm.modules.api.controller;

import com.wbyy.common.core.web.domain.AjaxResult;
import com.wbyy.crm.modules.api.model.dto.TargetCompleteQueryDto;
import com.wbyy.crm.modules.api.model.vo.TargetCompleteVo;
import com.wbyy.crm.modules.api.service.ApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("api")
public class ApiController {
    @Autowired
    private ApiService apiService;


    @GetMapping("target-complete")
    public AjaxResult targetComplete(TargetCompleteQueryDto dto){
        return AjaxResult.success(apiService.targetComplete(dto));
    }
}

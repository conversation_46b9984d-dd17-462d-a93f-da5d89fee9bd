package com.wbyy.crm.common.menu;

import com.wbyy.crm.common.base.BaseDomain;
import com.wbyy.crm.modules.business.quotation.domain.Quotation;
import com.wbyy.crm.modules.business.salesopportunity.domain.SalesOpportunity;
import com.wbyy.crm.modules.contracts.actualreceivables.domain.ActualReceivables;
import com.wbyy.crm.modules.contracts.contract.domain.Contract;
import com.wbyy.crm.modules.contracts.plannedreceivables.domain.PlannedReceivables;
import com.wbyy.crm.modules.contracts.receivedamount.domain.ReceivedAmount;
import com.wbyy.crm.modules.customers.contact.domain.Contacts;
import com.wbyy.crm.modules.customers.customer.domain.Customer;
import com.wbyy.crm.modules.customers.followup.domain.FollowUp;
import com.wbyy.crm.modules.customers.visitcheckin.domain.VisitCheckin;
import com.wbyy.crm.modules.customers.visitplan.domain.VisitPlan;
import com.wbyy.crm.modules.customers.visitsummary.domain.VisitSummary;
import com.wbyy.crm.modules.salescenter.annualgoalscompany.domain.AnnualGoalsCompany;
import com.wbyy.crm.modules.salescenter.monthlygoalscompany.domain.MonthlyGoalsCompany;
import com.wbyy.crm.modules.salescenter.personalmonthlygoal.domain.PersonalMonthlyGoal;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum DomainCrmMappingEnum {

    CUSTOMER(Customer.class,"customer","customerService"),
    CONTACTS(Contacts.class,"contactor","contactsService"),
    FOLLOWUP(FollowUp.class,"keep_record","followUpService"),
    VISITCHECKIN(VisitCheckin.class,"visit_record","visitCheckinService"),
    VISITSUMMARY(VisitSummary.class,"cus_form_4","visitSummaryService"),
    CONTRACT(Contract.class,"cus_form_9","contractService"),
    PLANNEDRECEIVABLES(PlannedReceivables.class,"plan_payment","plannedReceivablesService"),
    ACTUALRECEIVABLES(ActualReceivables.class,"real_payment","actualReceivablesService"),
    ANNUALGOALSCOMPANY(AnnualGoalsCompany.class,"cus_form_3","annualGoalsCompanyService"),
    MONTHLYGOALSCOMPANY(MonthlyGoalsCompany.class,"cus_form_14","monthlyGoalsCompanyService"),
    PERSONALMONTHLYGOAL(PersonalMonthlyGoal.class,"cus_form_12","personalMonthlyGoalService"),
    SALESOPPORTUNITY(SalesOpportunity.class,"opportunity","salesOpportunityService"),
    QUOTATION(Quotation.class,"quotation","quotationService"),
    RECEIVEDAMOUNT(ReceivedAmount.class,"cus_form_15","receivedAmountService"),
    VisitPlan(VisitPlan.class,"visit_plan","visitPlanService")
    ;


    private final Class<? extends BaseDomain> domainClass;
    private final String crmDeployId;
    private final String router;
}

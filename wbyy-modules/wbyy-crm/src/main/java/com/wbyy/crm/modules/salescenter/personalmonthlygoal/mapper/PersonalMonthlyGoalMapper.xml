<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.crm.modules.salescenter.personalmonthlygoal.mapper.PersonalMonthlyGoalMapper">
    <sql id="tableName">
        personal_monthly_goal
    </sql>

    <sql id="baseColumn">
        id,month,title,year,type,value,created_at,updated_at,created_by,updated_by,crm_id,sub_crm_id
    </sql>

    <select id="userNames" resultType="java.lang.String">
        select responsible_person from personal_monthly_goal where year=#{year} group by responsible_person
    </select>

</mapper>
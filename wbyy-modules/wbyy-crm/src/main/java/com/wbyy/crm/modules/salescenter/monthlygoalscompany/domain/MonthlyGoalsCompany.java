package com.wbyy.crm.modules.salescenter.monthlygoalscompany.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;

import java.math.BigDecimal;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 公司月度目标表(monthly_goals_company)实体类
 *
 * <AUTHOR>
 * @since 2025-03-26 15:40:45
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("monthly_goals_company")
public class MonthlyGoalsCompany extends BaseDomain {

    /**
     * id
     */


    /**
     * 月份
     */
    @CrmMapping("select_6")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String month;

    /**
     * 目标标题
     */
    @CrmMapping("relation_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String title;

    /**
     * 年
     */
    @CrmMapping("select_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String year;

    /**
     * 目标类型
     */
    @CrmMapping("select_5")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String type;

    /**
     * 目标值
     */
    @CrmMapping("amount_3")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal value;
}
package com.wbyy.crm.modules.api.controller;

import com.wbyy.common.core.web.domain.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 健康检查接口
 */
@RestController
@RequestMapping
public class CheckAliveServerController {
    @GetMapping("checkAliveServer")
    public AjaxResult checkAliveServer() {
        return AjaxResult.success();
    }
}

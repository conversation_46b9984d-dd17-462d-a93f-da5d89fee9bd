package com.wbyy.crm.modules.contracts.receivedamount.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.crm.modules.contracts.receivedamount.domain.ReceivedAmount;

import java.util.Date;
import java.util.List;

/**
 * 实收金额表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-01 16:53:31
 * @description 
 */
public interface ReceivedAmountService extends IService<ReceivedAmount> {

    List<ReceivedAmount> listByPaymentTimeRange(Date startTime,Date endTime);
}

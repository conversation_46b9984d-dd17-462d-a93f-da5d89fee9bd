package com.wbyy.crm.modules.dataasync;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.menu.DomainCrmMappingEnum;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.common.base.BaseDomain;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
@Slf4j
public class AsyncService {
    @Autowired
    private TokenHelper tokenHelper;
    @Autowired
    private Map<String, BaseService> asyncServiceMap;
    @Value("${crm.corp-id}")
    private String corpId;
    @Value("${crm.get-list}")
    private String getList;
    @Value("${crm.get-detail}")
    private String getDetail;
    @Resource(name = "asyncExecutor")
    private Executor asyncExecutor;
    @Resource(name = "asyncListExecutor")
    private Executor asyncListExecutor;

    // JSON解析工具
    private static final ObjectMapper mapper = new ObjectMapper();
    // HTTP客户端
    private static final OkHttpClient httpClient = new OkHttpClient();

    @XxlJob(value = "crmDataAsyncJobHandler")
    public ReturnT<String> doAsync(){
        DomainCrmMappingEnum[] mappingEnums = DomainCrmMappingEnum.values();
        CountDownLatch count = new CountDownLatch(mappingEnums.length);
        AtomicBoolean atomicBoolean = new AtomicBoolean(true);
        for (DomainCrmMappingEnum anEnum : mappingEnums) {
            asyncExecutor.execute(()->{
                try {
                    String router = anEnum.getRouter();
                    String crmDeployId = anEnum.getCrmDeployId();
                    Class<? extends BaseDomain> domainClass = anEnum.getDomainClass();
                    log.info("开始处理【{}】",router);
                    Field[] fields = ReflectUtil.getFields(domainClass);
                    List<Object> list = Collections.synchronizedList(new ArrayList<>());
                    long page = 1;
                    boolean hasMore = true;
                    while (hasMore) {
                        ListResponseData.ListData data;
                        try {
                            data = getListData(crmDeployId, page);
                        } catch (Exception e) {
                            log.error("获取crm数据失败【crmDeployId:{}】,【page:{}】",crmDeployId,page,e);
                            throw new ServiceException("获取crm数据失败");
                        }
                        // 安全处理空数据

                        if (data == null || data.list == null) {
                            break;
                        }
                        List<JSONObject> dataList = data.getList();
                        List<List<JSONObject>> split = CollectionUtil.split(dataList, 100);
                        CountDownLatch listCount = new CountDownLatch(split.size());
                        for (List<JSONObject> objectList : split) {
                            asyncListExecutor.execute(()->{
                                try {
                                    for (JSONObject object : objectList) {
                                        BaseDomain instance = ReflectUtil.newInstance(domainClass);
                                        for (Field field : fields) {
                                            CrmMapping mapping = field.getAnnotation(CrmMapping.class);
                                            if (null==mapping) continue;
                                            String fieldName = field.getName();
                                            String crmField = mapping.value();
                                            Object value = object.get(crmField);
                                            try {
                                                if (null!=value&&field.getType().equals(BigDecimal.class)){
                                                     value = value.toString()
                                                             .replaceAll("¥","")
                                                             .replaceAll("%","")
                                                             .trim();;
                                                }
                                                ReflectUtil.setFieldValue(instance,fieldName,value);
                                            }catch (Exception e){
                                                log.error("原始字段【{}】字段【{}】赋值【{}】失败",crmField,fieldName,value,e);
                                                throw new RuntimeException(e);
                                            }
                                        }
                                        list.add(instance);
                                    }
                                }catch (Exception e){
                                    log.error("【{}】异步执行失败",router,e);
                                    throw new RuntimeException(e);
                                }finally {
                                    listCount.countDown();
                                }
                            });
                        }
                        try {
                            listCount.await();
                        } catch (InterruptedException e) {
                            log.error("线程等待中断异常",e);
                            throw new ServiceException("执行失败");
                        }
                        // 终止条件判断
                        hasMore = shouldContinue(data, page);
                        page++;
                    }
                    asyncServiceMap.get(router).loadData(list);
                }catch (Exception e){
                    atomicBoolean.compareAndExchange(true,false);
                    log.error("【{}】处理失败",anEnum.getRouter(),e);
                }finally {
                    count.countDown();
                }
            });
        }
        try {
            count.await();
        } catch (InterruptedException e) {
            log.error("任务等待中断异常",e);
            return ReturnT.FAIL;
        }
        log.info("CRM数据同步任务结束");
        return atomicBoolean.get()?ReturnT.SUCCESS:ReturnT.FAIL;
    }

    private boolean shouldContinue(ListResponseData.ListData result, long page) {
        // 双重判断条件更健壮
        return result.getList().size() >= 1000L  // 当前页满载
                && (result.getCount() == null || 1000L * page < result.getCount());
    }

    private ListResponseData.ListData getListData(String deployId, Long page) throws Exception {
        MediaType mediaType=MediaType.Companion.parse("application/json;charset=utf-8");
        String bodyString = String.format("{\"corpId\":\"%s\",\"corpAccessToken\":\"%s\",\"deployId\":\"%s\",\"userId\":\"%s\",\"formated\":\"%b\",\"page\":\"%d\",\"pageSize\":\"%d\"}",
                corpId, tokenHelper.getToken(), deployId,"woR4g7DAAAaRMretHPi7jl63Lz7aQkXw",true, page, 1000L);
        RequestBody stringBody=RequestBody.Companion
                .create(bodyString, mediaType);
        Request request = new Request.Builder()
                .url(getList)
                .post(stringBody)
                .build();
        ListResponseData ListResponseData = null;
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);

            if (response.body() != null) {
                ListResponseData = mapper.readValue(response.body().bytes(), ListResponseData.class);
            }

            if (ListResponseData != null && ListResponseData.success) {
                return ListResponseData.data;
            }else throw new ServiceException("【"+deployId+"】接口请求失败："+(null!=ListResponseData?JSON.toJSONString(ListResponseData):"返回体为空"));
        }
    }



    // 响应数据结构
    @Data
    private static class ListResponseData {
        boolean success;
        ListData data;
        String message;
        String apiName;
        String ip;
        Integer result;
        String traceId;

        @lombok.Data
        static class ListData {
            @JsonProperty("count")
            Long count;
            @JsonProperty("list")
            List<JSONObject> list;
            @JsonProperty("errorMsg")
            String errorMsg;
            @JsonProperty("success")
            Boolean success;

        }
    }
}

package com.wbyy.crm.modules.customers.contact.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 联系人信息(contacts)实体类
 *
 * <AUTHOR>
 * @since 2025-03-26 08:57:06
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("contacts")
public class Contacts extends BaseDomain {
    /**
     * 联系人 ID
     */


    /**
     * 关联客户 ID
     */
    @CrmMapping("customer_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long customerId;

    /**
     * 客户名称
     */
    @CrmMapping("custom_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customerName;

    /**
     * 联系人姓名
     */
    @CrmMapping("contactor_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String name;

    /**
     * 类型（0：内线KI；1：教练KU；2：决策者KDM）
     */
    @CrmMapping("contactor_check_box_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String type;

    /**
     * 职位
     */
    @CrmMapping("contactor_position")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String position;

    /**
     * 联系电话
     */
    @CrmMapping("contactor_mobile")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String phone;

    /**
     * 联系人邮箱
     */
    @CrmMapping("contactor_email")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String email;

    /**
     * 微信号
     */
    @CrmMapping("contactor_wechat")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String wechat;
}
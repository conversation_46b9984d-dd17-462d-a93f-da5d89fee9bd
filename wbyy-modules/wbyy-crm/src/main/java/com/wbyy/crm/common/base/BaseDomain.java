package com.wbyy.crm.common.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.wbyy.crm.common.annotation.CrmMapping;
import lombok.Data;

import java.util.Date;

@Data
public class BaseDomain {
    @CrmMapping("id")
    @TableId(value = "id",type = IdType.INPUT)
    private Long id;
    /**
     * 最后修改时间
     */
    @CrmMapping("modified")
    private Date updatedAt;
    /**
     * 创建时间
     */
    @CrmMapping("created")
    private Date createdAt;
    /**
     * 更新人
     */
    @CrmMapping("changer_name")
    private String updatedBy;
    /**
     * 创建人
     */
    @CrmMapping("creator_name")
    private String createdBy;
    /**
     * crm_id
     */
    @CrmMapping("id")
    private Long crmId;
}

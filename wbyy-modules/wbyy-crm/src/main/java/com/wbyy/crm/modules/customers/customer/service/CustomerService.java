package com.wbyy.crm.modules.customers.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.crm.modules.customers.customer.domain.Customer;

import java.util.Date;
import java.util.List;

public interface CustomerService extends IService<Customer> {
    List<Customer> listByCreatedAtRange(Date startTime,Date endTime);

    List<Customer> listNewCustomers();

    void handleNewCustomer();
}

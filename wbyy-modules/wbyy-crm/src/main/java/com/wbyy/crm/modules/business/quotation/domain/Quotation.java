package com.wbyy.crm.modules.business.quotation.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.math.BigDecimal;
import java.util.Date;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 报价单基本信息(quotation)实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 09:58:43
 * @description 
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("quotation")
public class Quotation extends BaseDomain {

    /**
     * 报价单id
     */

    /**
     * 报价单标题
     */
    @CrmMapping("quotation_title")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String quotationName;

    /**
     * 报价单编号
     */
    @CrmMapping("quotation_number")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String quotationNo;

    /**
     * 客户id
     */
    @CrmMapping("customer_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long customerId;

    /**
     * 客户名称
     */
    @CrmMapping("custom_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customerName;

    /**
     * 货币名称
     */
    @CrmMapping("order_amount_unit")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String currencyName;

    /**
     * 报价金额
     */
    @CrmMapping("quotation_amount")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal totalAmount;

    /**
     * 销售机会名称（项目）
     */
    @CrmMapping("opportunity_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String salesOpportunityName;

    /**
     * 销售机会id
     */
    @CrmMapping("opportunity_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long salesOpportunityId;

    /**
     * 联系人姓名
     */
    @CrmMapping("contactor_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contactName;

    /**
     * 联系人id
     */
    @CrmMapping("contactor_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contactId;

    /**
     * 报价日期
     */
    @CrmMapping("quotation_date")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date quoteDate;

    /**
     * 有效日期
     */
    @CrmMapping("active_date")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date validUntil;

    /**
     * 报价状态
     */
    @CrmMapping("quotation_status")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String status;

    /**
     * 负责人名称
     */
    @CrmMapping("charger_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerName;

    /**
     * 负责人id
     */
    @CrmMapping("charger_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerId;

    /**
     * 负责人所在部门
     */
    @CrmMapping("charger_department_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerDept;

    /**
     * 协作人名称
     */
    @CrmMapping("collaborator_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorName;

    /**
     * 协作人id
     */
    @CrmMapping("collaborator_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorIds;

    /**
     * 是否转换为合同（0-否，1-是）
     */
    @CrmMapping("convert_order_status")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String isConverted;



    /**
     * 负责人所在部门id
     */
    @CrmMapping("charger_department_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerDeptId;


}
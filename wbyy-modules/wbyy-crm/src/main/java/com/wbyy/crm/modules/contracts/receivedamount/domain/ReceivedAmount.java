package com.wbyy.crm.modules.contracts.receivedamount.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.math.BigDecimal;
import java.util.Date;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 实收金额表(received_amount)实体类
 *
 * <AUTHOR>
 * @since 2025-04-01 16:53:31
 * @description 
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("received_amount")
public class ReceivedAmount extends BaseDomain {


    /**
     * 协作人
     */
    @CrmMapping("collaborator_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorName;
    /**
     * 负责人
     */
    @CrmMapping("charger_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String chargerName;
    /**
     * 序列号
     */
    @CrmMapping("relation_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String relationName;
    /**
     * 收款方式select_1
     */
    @CrmMapping("select_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String paymentWay;
    /**
     * 开票时间input_12
     */
    @CrmMapping("input_12")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date invoceTime;
    /**
     * 开票金额input_11
     */
    @CrmMapping("input_11")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal invoiceAmount;
    /**
     * 开具发票input_10
     */
    @CrmMapping("input_10")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String hasInvoice;
    /**
     * 回款时间input_9
     */
    @CrmMapping("input_9")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date paymentTime;
    /**
     * 是否回款select_0
     */
    @CrmMapping("select_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String hasPayment;
    /**
     * 回款节点input_8
     */
    @CrmMapping("input_8")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String paymentNode;
    /**
     * 计划回款期数input_7
     */
    @CrmMapping("input_7")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String planPaymentTimes;
    /**
     * 实际回款input_6
     */
    @CrmMapping("input_6")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal actualPayment;
    /**
     * 理论回款额input_5
     */
    @CrmMapping("input_5")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal planPayment;
    /**
     * 项目名称input_3
     */
    @CrmMapping("input_3")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String projectName;
    /**
     * 合同名称input_1
     */
    @CrmMapping("input_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractName;
    /**
     * 公司合同编号input_0
     */
    @CrmMapping("input_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String companyContractNo;
    /**
     * 委托方refer_object_1
     */
    @CrmMapping("refer_object_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String entruster;
    /**
     * 数据归属refer_object_0
     */
    @CrmMapping("refer_object_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String dataBelong;

    @CrmMapping("refer_object_multi_4")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String paymentChargerName;

}
package com.wbyy.crm.modules.dataasync;

import com.wbyy.common.core.web.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("async")
public class AsyncController {
    @Autowired
    private AsyncService asyncService;

    @GetMapping("do-async")
    public AjaxResult doAsync(){
        asyncService.doAsync();
        return AjaxResult.success();
    }
}

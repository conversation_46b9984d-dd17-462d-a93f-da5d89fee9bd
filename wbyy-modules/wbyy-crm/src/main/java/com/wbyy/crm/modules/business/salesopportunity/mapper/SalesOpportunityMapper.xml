<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.crm.modules.business.salesopportunity.mapper.SalesOpportunityMapper">
    <sql id="tableName">
        sales_opportunity
    </sql>

    <sql id="baseColumn">
        id,name,source,status_id,status_name,amount,success_rate,opportunity_type,business_type_name,business_type_id,is_bidding,is_first_contract,sales_process,drug_type,registration_type,proposal_due_date,win_rate,project_contact_name,project_contact_id,project_contact_position,expansion_contact_name,expansion_contact_id,opportunity_identifier_name,tech_lead_name,tech_lead_id,other_staff,collaborators_name1,collaborators_id1,collaborators_name2,collaborators_id2,collaborators_name3,collaborators_id3,collaborators_name4,collaborators_id4,owner_name,owner_id,customer_id,customer_name,updated_at,updated_by,created_at,created_by,opportunity_status,loss_reason,loss_type,loss_description,crm_id
    </sql>

</mapper>
package com.wbyy.crm.modules.salescenter.personalmonthlygoal.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;

import java.math.BigDecimal;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 个人月度目标表(personal_monthly_goal)实体类
 *
 * <AUTHOR>
 * @since 2025-03-26 16:49:10
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("personal_monthly_goal")
public class PersonalMonthlyGoal extends BaseDomain {

    /**
     * id
     */


    /**
     * 月份
     */
    @CrmMapping("select_4")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String month;

    /**
     * 目标标题
     */
    @CrmMapping("relation_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String title;

    /**
     * 年
     */
    @CrmMapping("select_2")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String year;

    /**
     * 目标类型
     */
    @CrmMapping("select_3")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String type;

    /**
     * 目标值
     */
    @CrmMapping("input_10")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal value;

    /**
     * 负责人
     */
    @CrmMapping("charger_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String responsiblePerson;
}
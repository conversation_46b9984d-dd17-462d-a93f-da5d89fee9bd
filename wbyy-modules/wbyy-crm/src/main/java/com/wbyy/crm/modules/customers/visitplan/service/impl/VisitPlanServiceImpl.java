package com.wbyy.crm.modules.customers.visitplan.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.customers.visitplan.domain.VisitPlan;
import com.wbyy.crm.modules.customers.visitplan.mapper.VisitPlanMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.crm.modules.customers.visitplan.service.VisitPlanService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 拜访计划服务接口实现
 *
 * <AUTHOR>
 * @since 2025-04-08 10:13:47
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("visitPlanService")
public class VisitPlanServiceImpl extends ServiceImpl<VisitPlanMapper, VisitPlan> implements VisitPlanService, BaseService {
    private final VisitPlanMapper visitPlanMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<VisitPlan> remoteDataList = list.stream().map(VisitPlan.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(VisitPlan::getId).collect(Collectors.toSet());
        List<VisitPlan> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(VisitPlan::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【VisitPlan】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【VisitPlan】落库数据数：{}",remoteDataList.size());
    }
}
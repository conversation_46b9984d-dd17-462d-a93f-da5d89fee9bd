package com.wbyy.crm.modules.salescenter.personalmonthlygoal.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.contracts.contract.domain.Contract;
import com.wbyy.crm.modules.salescenter.personalmonthlygoal.domain.PersonalMonthlyGoal;
import com.wbyy.crm.modules.salescenter.personalmonthlygoal.mapper.PersonalMonthlyGoalMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.crm.modules.salescenter.personalmonthlygoal.service.PersonalMonthlyGoalService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 个人月度目标表服务接口实现
 *
 * <AUTHOR>
 * @since 2025-03-26 16:49:10
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("personalMonthlyGoalService")
public class PersonalMonthlyGoalServiceImpl extends ServiceImpl<PersonalMonthlyGoalMapper, PersonalMonthlyGoal> implements PersonalMonthlyGoalService, BaseService {
    private final PersonalMonthlyGoalMapper personalMonthlyGoalMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<PersonalMonthlyGoal> remoteDataList = list.stream().map(PersonalMonthlyGoal.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(PersonalMonthlyGoal::getId).collect(Collectors.toSet());
        List<PersonalMonthlyGoal> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(PersonalMonthlyGoal::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【PersonalMonthlyGoal】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【PersonalMonthlyGoal】落库数据数：{}",remoteDataList.size());
    }

    @Override
    public List<PersonalMonthlyGoal> listByYear(String year) {
        LambdaQueryWrapper<PersonalMonthlyGoal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonalMonthlyGoal::getYear,year);
        return list(queryWrapper);
    }

    @Override
    public Set<String> userNames(String year) {
        return this.personalMonthlyGoalMapper.userNames(year);
    }
}
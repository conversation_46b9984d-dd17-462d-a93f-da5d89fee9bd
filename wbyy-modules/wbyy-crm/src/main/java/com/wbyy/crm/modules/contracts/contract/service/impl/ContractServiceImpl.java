package com.wbyy.crm.modules.contracts.contract.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.contracts.contract.domain.Contract;
import com.wbyy.crm.modules.contracts.contract.mapper.ContractMapper;
import com.wbyy.crm.modules.contracts.plannedreceivables.domain.PlannedReceivables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.crm.modules.contracts.contract.service.ContractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 合同基本信息表服务接口实现
 *
 * <AUTHOR>
 * @since 2025-03-26 11:03:37
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("contractService")
public class ContractServiceImpl extends ServiceImpl<ContractMapper, Contract> implements ContractService, BaseService {
    private final ContractMapper contractMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<Contract> remoteDataList = list.stream().map(Contract.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(Contract::getId).collect(Collectors.toSet());
        List<Contract> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(Contract::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【Contract】删除数据数：{}",toRemoveIdSet.size());
        remoteDataList.forEach(item->{
            if (StrUtil.isNotBlank(item.getBusinessType())){
                String[] split = item.getBusinessType().split("-");
                if (split.length>=1){
                    String s = split[0];
                    String bizType = switch (s) {
                        case "创新药I期", "传统BE", "新业务BE" -> "创新药I期、传统BE、新业务BE";
                        case "特殊食品", "保健食品","其他业务" -> "特殊食品、保健食品等其他业务";
                        case "药学","验证性临床","医疗器械"->s;
                        default -> "";
                    };
                    item.setBizType(bizType);
                    item.setBusinessType(split.length>1?split[1]:split[0]);
                }
            }
        });
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【Contract】落库数据数：{}",remoteDataList.size());
    }


    @Override
    public Set<String> userNames() {
        return contractMapper.selectUserNames();
    }

    @Override
    public List<Contract> listByContractDateRange(Date startTime, Date endTime) {
        LambdaQueryWrapper<Contract> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(Contract::getContractDate,startTime);
        queryWrapper.le(Contract::getContractDate,endTime);
        return list(queryWrapper);
    }
}
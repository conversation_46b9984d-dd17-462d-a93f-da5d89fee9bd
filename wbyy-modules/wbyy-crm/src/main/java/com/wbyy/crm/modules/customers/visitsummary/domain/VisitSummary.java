package com.wbyy.crm.modules.customers.visitsummary.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;

import java.util.Date;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 拜访总结表(visit_summary)实体类
 *
 * <AUTHOR>
 * @since 2025-03-26 10:48:26
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("visit_summary")
public class VisitSummary extends BaseDomain {

    /**
     * 主键id
     */


    /**
     * 客户名称
     */
    @CrmMapping("refer_object_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customerName;

    /**
     * 客户id
     */
    @CrmMapping("refer_object_id_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long customerId;

    /**
     * 拜访计划id(关联)
     */
    @CrmMapping("refer_object_id_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String visitPlanId;

    /**
     * 拜访活动类型（现场、电话等）
     */
    @CrmMapping("select_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String visitType;

    /**
     * 拜访总结
     */
    @CrmMapping("textarea_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String visitSummary;

    /**
     * 负责人名称
     */
    @CrmMapping("charger_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerName;

    /**
     * 负责人id
     */
    @CrmMapping("charger_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerId;

    /**
     * 协作人
     */
    @CrmMapping("collaborator_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName;

    /**
     * 协作人id
     */
    @CrmMapping("collaborator_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsId;
}
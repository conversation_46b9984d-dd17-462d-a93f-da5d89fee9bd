package com.wbyy.crm.modules.customers.followup.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.crm.modules.customers.followup.domain.FollowUp;

import java.util.Date;
import java.util.List;

/**
 * 跟进记录服务接口
 *
 * <AUTHOR>
 * @since 2025-03-26 09:47:49
 * @description 
 */
public interface FollowUpService extends IService<FollowUp> {
    List<FollowUp> listByCreatedRange(Date startTime,Date endTime);
}

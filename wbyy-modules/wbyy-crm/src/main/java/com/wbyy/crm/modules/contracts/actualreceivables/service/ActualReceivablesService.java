package com.wbyy.crm.modules.contracts.actualreceivables.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.crm.modules.contracts.actualreceivables.domain.ActualReceivables;

import java.util.Date;
import java.util.List;

/**
 * 实际回款服务接口
 *
 * <AUTHOR>
 * @since 2025-03-26 14:03:27
 * @description 
 */
public interface ActualReceivablesService extends IService<ActualReceivables> {

    List<ActualReceivables> listByCreatedAtRange(Date startTime,Date endTime);

}

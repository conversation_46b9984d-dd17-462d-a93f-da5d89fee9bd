package com.wbyy.crm.modules.customers.visitcheckin.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.crm.modules.customers.visitcheckin.domain.VisitCheckin;

import java.util.Date;
import java.util.List;

/**
 * 拜访签到服务接口
 *
 * <AUTHOR>
 * @since 2025-03-26 10:30:38
 * @description 
 */
public interface VisitCheckinService extends IService<VisitCheckin> {
    List<VisitCheckin> listByCheckinTimeRange(Date startTime,Date endTime);
}

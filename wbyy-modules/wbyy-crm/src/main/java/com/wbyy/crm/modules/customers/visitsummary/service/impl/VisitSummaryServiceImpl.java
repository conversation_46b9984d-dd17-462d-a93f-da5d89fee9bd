package com.wbyy.crm.modules.customers.visitsummary.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.customers.visitsummary.domain.VisitSummary;
import com.wbyy.crm.modules.customers.visitsummary.mapper.VisitSummaryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.crm.modules.customers.visitsummary.service.VisitSummaryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 拜访总结表服务接口实现
 *
 * <AUTHOR>
 * @since 2025-03-26 10:48:26
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("visitSummaryService")
public class VisitSummaryServiceImpl extends ServiceImpl<VisitSummaryMapper, VisitSummary> implements VisitSummaryService, BaseService {
    private final VisitSummaryMapper visitSummaryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<VisitSummary> remoteDataList = list.stream().map(VisitSummary.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(VisitSummary::getId).collect(Collectors.toSet());
        List<VisitSummary> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(VisitSummary::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【VisitSummary】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【VisitSummary】落库数据数：{}",remoteDataList.size());
    }
}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.crm.modules.contracts.plannedreceivables.mapper.PlannedReceivablesMapper">
    <sql id="tableName">
        planned_receivables
    </sql>

    <sql id="baseColumn">
        id,customer_name,customer_id,department,department_id,contract_name,contract_id,receiving_department,responsible_department,serial_number,contract_data_no,payment_milestone,contract_order_no,contract_order_title,installment_number,currency_name,invoiced_amount,uninvoiced_amount,invoice_status,planned_payment_amount,planned_payment_date,actual_payment_amount,payment_difference,actual_payment_date,unpaid_amount,current_payment_status,contract_order_amount,responsible_person,responsible_person_id,owner_department,owner_department_id,collaborators_name,collaborators_id,created_by_name,created_by_id,updated_by_name,updated_by_id,updated_at,created_at
    </sql>

</mapper>
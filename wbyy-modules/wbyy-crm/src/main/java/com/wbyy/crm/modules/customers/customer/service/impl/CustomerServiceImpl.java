package com.wbyy.crm.modules.customers.customer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.business.salesopportunity.domain.SalesOpportunity;
import com.wbyy.crm.modules.customers.customer.domain.Customer;
import com.wbyy.crm.modules.customers.customer.mapper.CustomerMapper;
import com.wbyy.crm.modules.customers.customer.service.CustomerService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("customerService")
@Slf4j
public class CustomerServiceImpl extends ServiceImpl<CustomerMapper, Customer> implements CustomerService, BaseService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<Customer> remoteDataList = list.stream().map(Customer.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(Customer::getId).collect(Collectors.toSet());
        List<Customer> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(Customer::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【Customer】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【Customer】落库数据数：{}",remoteDataList.size());
    }
    @Override
    public List<Customer> listByCreatedAtRange(Date startTime, Date endTime) {
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(Customer::getCreatedAt,startTime);
        queryWrapper.le(Customer::getCreatedAt,endTime);
        return list(queryWrapper);
    }

    @Override
    public List<Customer> listNewCustomers(){
//        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(Customer::getNewCustomer,1);
//        return this.list(queryWrapper);
        return list(new LambdaQueryWrapper<Customer>().in(Customer::getName,List.of("深圳市新阳唯康科技有限公司","江苏济茗医药有限公司","山东益仁堂药业有限公司","扬州奥锐特药业有限公司","江西大生医药科技有限公司","上海鼎雅药物化学科技有限公司","乐声药业石家庄有限公司","古戈尔药业（海南）有限责任公司","湖南醇健制药科技有限公司","湖南千金湘江药业股份有限公司","贵州联盛药业有限公司","三门善祥医药研究有限公司","上海旭东海普药业有限公司","湖南醇康医药科技有限公司","人福普克药业（武汉）有限公司","双鹤天安药业（贵州）股份有限公司","株洲千金药业股份有限公司","重庆世森医药科技有限公司","安徽远望乐桓药业有限公司","新疆特丰药业股份有限公司","江苏神华药业有限公司","华润紫竹药业有限公司","湖南中康元健康产业有限公司","安徽赛乐医药科技有限公司","北京科信必成医药科技发展有限公司","石药集团欧意药业有限公司","安徽四环科宝制药有限公司","江苏艾立康医药科技有限公司")));
    }

    @Override
    @XxlJob(value = "newCustomerAsyncJobHandler")
    public void handleNewCustomer() {
        Set<Long> newCustomerIdSet = this.baseMapper.selectNewCustomerIdSet();
        if (CollectionUtil.isEmpty(newCustomerIdSet)){
            log.warn("没有新客");
            return;
        }
        this.baseMapper.updateNewCustomer(newCustomerIdSet);
        this.baseMapper.updateNotNewCustomer(newCustomerIdSet);
        log.info("新客处理结束,处理【{}】条数据",newCustomerIdSet.size());
    }
}

package com.wbyy.crm;

import com.wbyy.common.core.constant.SecurityConstants;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.wbyy.common.security.annotation.EnableCustomConfig;
import com.wbyy.common.security.annotation.EnableRyFeignClients;

/**
 * CRM
 * 
 * <AUTHOR>
 */
@Slf4j
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
public class WbyyCrmApplication
{
    // TODO 归档合同业务类型是细分，要映射营销计划中的业务类型作为真实的业务类型，具体映射关系问冬哥
    public static void main(String[] args)
    {
        MDC.put(SecurityConstants.TRACE_ID, "crm");
        SpringApplication.run(WbyyCrmApplication.class, args);
        log.info("(♥◠‿◠)ﾉﾞ  CRM模块启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}

package com.wbyy.crm.modules.customers.visitcheckin.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;

import java.util.Date;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 拜访签到(visit_checkin)实体类
 *
 * <AUTHOR>
 * @since 2025-03-26 10:30:38
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("visit_checkin")
public class VisitCheckin extends BaseDomain {

    /**
     * 主键id
     */


    /**
     * 拜访计划id(关联)
     */
    @CrmMapping("id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long visitPlanId;

    /**
     * 联系人id
     */
    @CrmMapping("contactor_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contactId;

    /**
     * 联系人
     */
    @CrmMapping("contactor_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contactName;

    /**
     * 拜访内容
     */
    @CrmMapping("content")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String visitContent;

    /**
     * 签到时间
     */
    @CrmMapping("sign_in_time")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date checkinTime;

    /**
     * 拜访状态
     */
    @CrmMapping("visit_status")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String visitStatus;

    /**
     * 签到地址
     */
    @CrmMapping("sign_address")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String checkinAddress;

    /**
     * 负责人名称
     */
    @CrmMapping("record_creator_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerName;

    /**
     * 负责人id
     */
    @CrmMapping("record_creator_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerId;
}
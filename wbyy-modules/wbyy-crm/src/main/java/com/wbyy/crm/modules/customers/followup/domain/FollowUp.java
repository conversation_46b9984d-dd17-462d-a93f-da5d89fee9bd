package com.wbyy.crm.modules.customers.followup.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;

import java.util.Date;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 跟进记录(follow_up)实体类
 *
 * <AUTHOR>
 * @since 2025-03-26 09:47:49
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("follow_up")
public class FollowUp extends BaseDomain {
    /**
     * 记录id
     */


    /**
     * 关联客户id
     */
    @CrmMapping("customer_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long customerId;

    /**
     * 客户名称
     */
    @CrmMapping("custom_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customerName;

    /**
     * 对象类型
     */
    @CrmMapping("deploy_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String objectType;

    /**
     * 关联对象（商机名称、客户名称等）
     */
    @CrmMapping("relation_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String associationObjects;

    /**
     * 跟进联系人id
     */
    @CrmMapping("contactor_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long contactsId;

    /**
     * 跟进联系人
     */
    @CrmMapping("contactor_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contactsName;

    /**
     * 跟进时间
     */
    @CrmMapping("created")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date followupDate;

    /**
     * 跟进类型('电话', '邮件', '会议', '上门拜访', '微信', '其他')
     */
    @CrmMapping("keep_type")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String followupType;

    /**
     * 跟进内容
     */
    @CrmMapping("keep_content")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String followupContent;

    /**
     * 跟进状态（如 "意向强烈"、"需进一步跟进"、"无需求"）
     */
    @CrmMapping("keep_record_status")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String followupStatus;

    /**
     * 负责人，跟进人
     */
    @CrmMapping("charger_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String responsiblePerson;

    /**
     * 处理时间
     */
    @CrmMapping("created")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date created;
}
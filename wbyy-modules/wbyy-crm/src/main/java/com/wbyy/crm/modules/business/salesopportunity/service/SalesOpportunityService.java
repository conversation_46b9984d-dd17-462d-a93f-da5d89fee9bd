package com.wbyy.crm.modules.business.salesopportunity.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.crm.modules.business.salesopportunity.domain.SalesOpportunity;

import java.util.Date;
import java.util.List;

/**
 * 商机基本信息表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-27 09:05:53
 * @description 
 */
public interface SalesOpportunityService extends IService<SalesOpportunity> {

    List<SalesOpportunity> listByCreatedAtRange(Date startTime,Date endTime);

    List<SalesOpportunity> listWinByStageChangeTimeRange(Date startTime,Date endTime);

}

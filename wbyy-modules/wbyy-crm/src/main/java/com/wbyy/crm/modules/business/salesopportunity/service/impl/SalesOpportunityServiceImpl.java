package com.wbyy.crm.modules.business.salesopportunity.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.business.salesopportunity.domain.SalesOpportunity;
import com.wbyy.crm.modules.business.salesopportunity.mapper.SalesOpportunityMapper;
import com.wbyy.crm.modules.contracts.actualreceivables.domain.ActualReceivables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.crm.modules.business.salesopportunity.service.SalesOpportunityService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商机基本信息表服务接口实现
 *
 * <AUTHOR>
 * @since 2025-03-27 09:05:53
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("salesOpportunityService")
public class SalesOpportunityServiceImpl extends ServiceImpl<SalesOpportunityMapper, SalesOpportunity> implements SalesOpportunityService, BaseService {
    private final SalesOpportunityMapper salesOpportunityMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<SalesOpportunity> remoteDataList = list.stream().map(SalesOpportunity.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(SalesOpportunity::getId).collect(Collectors.toSet());
        List<SalesOpportunity> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(SalesOpportunity::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【SalesOpportunity】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【SalesOpportunity】落库数据数：{}",remoteDataList.size());
    }

    @Override
    public List<SalesOpportunity> listByCreatedAtRange(Date startTime, Date endTime) {
        LambdaQueryWrapper<SalesOpportunity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(SalesOpportunity::getCreatedAt,startTime);
        queryWrapper.le(SalesOpportunity::getCreatedAt,endTime);
        return list(queryWrapper);
    }

    private static final List<String> filterNumbers = Arrays.asList("XSJH0190","XSJH0339","XSJH0327");

    @Override
    public List<SalesOpportunity> listWinByStageChangeTimeRange(Date startTime, Date endTime) {
        LambdaQueryWrapper<SalesOpportunity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SalesOpportunity::getCustomerStage,"赢单");
        queryWrapper.ge(SalesOpportunity::getStageChangeTime,startTime);
        queryWrapper.le(SalesOpportunity::getStageChangeTime,endTime);
        return list(queryWrapper).stream()
                .filter(item->!filterNumbers.contains(item.getOpportunityNumber()))
                .toList();
    }
}
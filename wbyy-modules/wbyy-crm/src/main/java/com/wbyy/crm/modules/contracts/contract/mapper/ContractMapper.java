package com.wbyy.crm.modules.contracts.contract.mapper;

import com.wbyy.crm.modules.contracts.contract.domain.Contract;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.Set;

/**
 * 合同基本信息表(contract)数据Mapper
 *
 * <AUTHOR>
 * @since 2025-03-26 11:03:37
 * @description 
*/
@Mapper
public interface ContractMapper extends BaseMapper<Contract> {
    Set<String> selectUserNames();
}

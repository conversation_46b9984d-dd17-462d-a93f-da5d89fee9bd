package com.wbyy.crm.modules.contracts.actualreceivables.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.math.BigDecimal;
import java.util.Date;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 实际回款(actual_receivables)实体类
 *
 * <AUTHOR>
 * @since 2025-03-26 14:03:27
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("actual_receivables")
public class ActualReceivables extends BaseDomain {

    /**
     * 实际回款id
     */


    /**
     * 流水号
     */
    @CrmMapping("real_payment_input_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String transactionNo;

    /**
     * 序列号
     */
    @CrmMapping("real_payment_input_4")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String serialNumber;

    /**
     * 所属部门（数据归属）
     */
    @CrmMapping("dimension_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String department;

    /**
     * 所属部门id
     */
    @CrmMapping("dimension_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String departmentId;

    /**
     * 合同名称
     */
    @CrmMapping("plan_payment_input_3")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractName;

    /**
     * 合同id
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractId;

    /**
     * 合同数据编号
     */
    @CrmMapping("plan_payment_input_4")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractDataNo;

    /**
     * 项目编码
     */
    @CrmMapping("real_payment_input_5")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String projectCode;

    /**
     * 应付日期
     */
    @CrmMapping("real_payment_date_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date dueDate;

    /**
     * 业务日期
     */
    @CrmMapping("business_date")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date businessDate;

    /**
     * 合同订单编号
     */
    @CrmMapping("order_number")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractOrderNo;

    /**
     * 合同订单标题
     */
    @CrmMapping("order_title")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractOrderTitle;

    /**
     * 合同订单id
     */
    @CrmMapping("sale_order_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractOrderId;

    /**
     * 计划回款期数
     */
    @CrmMapping("period")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String plannedPaymentPeriod;

    /**
     * 理论回款额(元)
     */
    @CrmMapping("real_payment_amount_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal expectedPaymentAmount;

    /**
     * 实际回款金额
     */
    @CrmMapping("real_payment_amount")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal actualPaymentAmount;

    /**
     * 实际回款单号
     */
    @CrmMapping("real_order_number")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String actualPaymentNo;

    /**
     * 发票开具
     */
    @CrmMapping("real_payment_input_8")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String invoiceIssued;

    /**
     * 回款节点
     */
    @CrmMapping("real_payment_input_3")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String paymentMilestone;

    /**
     * 是否回款
     */
    @CrmMapping("real_payment_select_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String isPaymentReceived;

    /**
     * 开票日期
     */
    @CrmMapping("real_payment_date_2")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date invoiceDate;

    /**
     * 开票金额
     */
    @CrmMapping("real_payment_input_9")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal invoiceAmount;

    /**
     * 货币名称
     */
    @CrmMapping("real_payment_unit")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String currencyName;

    /**
     * 折让金额
     */
    @CrmMapping("amount_discount")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal discountAmount;

    /**
     * 收款类型
     */
    @CrmMapping("pay_type")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String paymentType;

    /**
     * 付款方式
     */
    @CrmMapping("real_payment_type")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String paymentMethod;

    /**
     * 是否预收
     */
    @CrmMapping("pay_in_advance")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String isAdvancePayment;

    /**
     * 创建人
     */
    @CrmMapping("creator_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String createdByName;

    /**
     * 最后修改人
     */
    @CrmMapping("changer_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String updatedByName;

    /**
     * 负责人
     */
    @CrmMapping("charger_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String responsiblePerson;
}
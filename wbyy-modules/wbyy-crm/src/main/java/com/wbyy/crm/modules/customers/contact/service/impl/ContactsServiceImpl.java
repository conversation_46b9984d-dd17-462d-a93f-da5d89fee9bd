package com.wbyy.crm.modules.customers.contact.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.contracts.contract.domain.Contract;
import com.wbyy.crm.modules.customers.contact.domain.Contacts;
import com.wbyy.crm.modules.customers.contact.mapper.ContactsMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.crm.modules.customers.contact.service.ContactsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 联系人信息服务接口实现
 *
 * <AUTHOR>
 * @since 2025-03-26 08:57:06
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("contactsService")
public class ContactsServiceImpl extends ServiceImpl<ContactsMapper, Contacts> implements ContactsService, BaseService {
    private final ContactsMapper contactsMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<Contacts> remoteDataList = list.stream().map(Contacts.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(Contacts::getId).collect(Collectors.toSet());
        List<Contacts> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(Contacts::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【Contacts】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【Contacts】落库数据数：{}",remoteDataList.size());
    }
}
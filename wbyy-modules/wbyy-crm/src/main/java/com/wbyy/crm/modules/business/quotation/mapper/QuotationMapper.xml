<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.crm.modules.business.quotation.mapper.QuotationMapper">
    <sql id="tableName">
        quotation
    </sql>

    <sql id="baseColumn">
        id,quotation_name,quotation_no,customer_id,customer_name,currency_name,total_amount,sales_opportunity_name,sales_opportunity_id,contact_name,contact_id,quote_date,valid_until,status,owner_name,owner_id,owner_dept,owner_dept_id,collaborator_name,collaborator_ids,is_converted,attachment_url,updated_at,updated_by,created_by,created_at,crm_id
    </sql>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.crm.modules.customers.customer.mapper.CustomerMapper">
    <sql id="tableName">
        customer
    </sql>

    <sql id="baseColumn">
        id,name,industry,status,company_type,source,region,activity_status,department,level,registered_capital,legal_representative,credit_code,establishment_date,source_info,updated_at,created_at,updated_by,created_by,custom_nick
    </sql>

    <update id="handleNewCustomer">
        UPDATE customer SET new_customer=1 WHERE id in (SELECT DISTINCT customer_id
        FROM contract
        WHERE contract_date = (
        SELECT MIN(contract_date)
        FROM contract c2
        WHERE c2.customer_id = contract.customer_id
        )
        AND contract_date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR));
        UPDATE customer SET new_customer=1 WHERE id not in (SELECT DISTINCT customer_id
        FROM contract
        WHERE contract_date = (
        SELECT MIN(contract_date)
        FROM contract c2
        WHERE c2.customer_id = contract.customer_id
        )
        AND contract_date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR))
    </update>

    <select id="selectNewCustomerIdSet" resultType="java.lang.Long">
        SELECT DISTINCT customer_id
        FROM contract
        WHERE contract_date = (
        SELECT MIN(contract_date)
        FROM contract c2
        WHERE c2.customer_id = contract.customer_id
        )
        AND contract_date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
    </select>

    <update id="updateNewCustomer">
        UPDATE customer SET new_customer=1 WHERE id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="updateNotNewCustomer">
        UPDATE customer SET new_customer=0 WHERE id not in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </update>

</mapper>
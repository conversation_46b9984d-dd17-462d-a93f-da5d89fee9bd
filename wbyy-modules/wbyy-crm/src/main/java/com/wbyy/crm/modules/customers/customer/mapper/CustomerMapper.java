package com.wbyy.crm.modules.customers.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wbyy.crm.modules.customers.customer.domain.Customer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * 客户基本信息(customer)数据Mapper
 *
 * <AUTHOR>
 * @since 2025-03-25 10:11:41
 * @description 由 Mybatisplus Code Generator 创建
*/
@Mapper
public interface CustomerMapper extends BaseMapper<Customer> {
    void handleNewCustomer();

    Set<Long> selectNewCustomerIdSet();

    void updateNewCustomer(@Param("ids")Set<Long> ids);

    void updateNotNewCustomer(@Param("ids")Set<Long> ids);
}

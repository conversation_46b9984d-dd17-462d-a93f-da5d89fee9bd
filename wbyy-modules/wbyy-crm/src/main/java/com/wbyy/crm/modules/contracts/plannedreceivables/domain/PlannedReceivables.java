package com.wbyy.crm.modules.contracts.plannedreceivables.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.math.BigDecimal;
import java.util.Date;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 计划回款表(planned_receivables)实体类
 *
 * <AUTHOR>
 * @since 2025-03-26 11:25:54
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("planned_receivables")
public class PlannedReceivables extends BaseDomain {

    /**
     * 计划回款id
     */


    /**
     * 客户名称(委托方)
     */
    @CrmMapping("custom_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customerName;

    /**
     * 委托方id
     */
    @CrmMapping("customer_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customerId;

    /**
     * 所属部门（数据归属）
     */
    @CrmMapping("dimension_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String department;

    /**
     * 所属部门id
     */
    @CrmMapping("dimension_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String departmentId;

    /**
     * 合同名称
     */
    @CrmMapping("plan_payment_input_3")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractName;

    /**
     * 合同id
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractId;

    /**
     * 承接部门
     */
    @CrmMapping("plan_payment_refer_object_2")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String receivingDepartment;

    /**
     * 责任部门
     */
    @CrmMapping("plan_payment_refer_object_id_2")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String responsibleDepartment;

    /**
     * 序列号
     */
    @CrmMapping("plan_payment_input_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String serialNumber;

    /**
     * 合同数据编号
     */
    @CrmMapping("plan_payment_input_4")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractDataNo;

    /**
     * 回款节点
     */
    @CrmMapping("plan_payment_textarea_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String paymentMilestone;

    /**
     * 合同订单编号
     */
    @CrmMapping("order_number")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractOrderNo;

    /**
     * 合同订单标题
     */
    @CrmMapping("order_title")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String contractOrderTitle;

    /**
     * 期数
     */
    @CrmMapping("period")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String installmentNumber;

    /**
     * 货币名称
     */
    @CrmMapping("plan_payment_unit")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String currencyName;

    /**
     * 已开票金额
     */
    @CrmMapping("invoice_amount")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal invoicedAmount;

    /**
     * 未开票金额
     */
    @CrmMapping("none_invoice_amount")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal uninvoicedAmount;

    /**
     * 开票状态
     */
    @CrmMapping("invoice_status")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String invoiceStatus;

    /**
     * 计划回款金额
     */
    @CrmMapping("plan_payment_amount")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal plannedPaymentAmount;

    /**
     * 计划回款日期
     */
    @CrmMapping("plan_payment_date")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date plannedPaymentDate;

    /**
     * 实际回款金额
     */
    @CrmMapping("real_payment_amount")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal actualPaymentAmount;

    /**
     * 回款差异
     */
    @CrmMapping("plan_payment_amount_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal paymentDifference;

    /**
     * 实际回款时间
     */
    @CrmMapping("real_payment_date")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date actualPaymentDate;

    /**
     * 未回款金额
     */
    @CrmMapping("not_payment_amount")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal unpaidAmount;

    /**
     * 本期回款状态
     */
    @CrmMapping("payment_status")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String currentPaymentStatus;

    /**
     * 合同订单金额
     */
    @CrmMapping("order_amount")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal contractOrderAmount;

    /**
     * 负责人
     */
    @CrmMapping("charger_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String responsiblePerson;

    /**
     * 负责人id
     */
    @CrmMapping("charger_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String responsiblePersonId;

    /**
     * 负责人所在部门
     */
    @CrmMapping("charger_department_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerDepartment;

    /**
     * 负责人所在部门id
     */
    @CrmMapping("charger_department_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerDepartmentId;

    /**
     * 协作人
     */
    @CrmMapping("collaborator_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName;

    /**
     * 协作人id
     */
    @CrmMapping("collaborator_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsId;

    /**
     * 创建人id
     */
    @CrmMapping("creator_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String createdById;

    /**
     * 最后修改人id
     */
    @CrmMapping("changer_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String updatedById;
}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.crm.modules.contracts.receivedamount.mapper.ReceivedAmountMapper">
    <sql id="tableName">
        received_amount
    </sql>

    <sql id="baseColumn">
        id,collaborator_name,charger_name,relation_name,payment_way,invoce_time,invoice_amount,has_invoice,payment_time,has_payment,payment_node,plan_payment_times,actual_payment,plan_payment,project_name,contract_name,company_contract_no,entruster,data_belong,updated_at,created_at,updated_by,created_by,crm_id
    </sql>

</mapper>
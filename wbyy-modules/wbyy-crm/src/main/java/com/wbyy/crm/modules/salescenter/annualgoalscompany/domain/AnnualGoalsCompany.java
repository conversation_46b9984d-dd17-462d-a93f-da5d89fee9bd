package com.wbyy.crm.modules.salescenter.annualgoalscompany.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;

import java.math.BigDecimal;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 公司年度目标表(annual_goals_company)实体类
 *
 * <AUTHOR>
 * @since 2025-03-26 14:58:03
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("annual_goals_company")
public class AnnualGoalsCompany extends BaseDomain {
    /**
     * 目标ID
     */


    /**
     * 年度
     */
    @CrmMapping("select_4")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String year;

    /**
     * 目标标题
     */
    @CrmMapping("relation_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String title;

    /**
     * 负责人名称
     */
    @CrmMapping("charger_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String responsiblePerson;

    /**
     * 负责人id
     */
    @CrmMapping("charger_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String responsiblePersonId;

    /**
     * 上年度目标值
     */
    @CrmMapping("input_10")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal lastYearTarget;

    /**
     * 上年度完成额
     */
    @CrmMapping("input_11")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal lastYearAchievement;

    /**
     * 今年目标值
     */
    @CrmMapping("input_12")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal thisYearTarget;

    /**
     * 业务类型
     */
    @CrmMapping("select_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String businessType;
}
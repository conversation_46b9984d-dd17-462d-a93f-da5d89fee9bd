package com.wbyy.crm.modules.business.quotation.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.business.quotation.domain.Quotation;
import com.wbyy.crm.modules.business.quotation.mapper.QuotationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.crm.modules.business.quotation.service.QuotationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 报价单基本信息服务接口实现
 *
 * <AUTHOR>
 * @since 2025-03-27 09:58:43
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("quotationService")
public class QuotationServiceImpl extends ServiceImpl<QuotationMapper, Quotation> implements QuotationService, BaseService {
    private final QuotationMapper quotationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<Quotation> remoteDataList = list.stream().map(Quotation.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(Quotation::getId).collect(Collectors.toSet());
        List<Quotation> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(Quotation::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【Quotation】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【Quotation】落库数据数：{}",remoteDataList.size());
    }
}
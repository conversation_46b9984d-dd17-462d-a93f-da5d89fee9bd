<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.project.plan.mapper.ProjectPlanReleaseMapper">

    <select id="selectAllParentNodeById"
            resultType="com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease">
        WITH RECURSIVE project_plan_release_cte AS (
            SELECT id,
                   parent_id,
                   NAME,
                   sort,
                   reality_start_date,
                   reality_end_date,
                   reality_progress
            FROM project_plan_release
            WHERE id in
                <foreach collection="idList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            UNION ALL

            SELECT r.id,
                   r.parent_id,
                   r.NAME,
                   r.sort,
                   r.reality_start_date,
                   r.reality_end_date,
                   r.reality_progress
            FROM project_plan_release r
                     JOIN project_plan_release_cte c ON r.id = c.parent_id
        )
        SELECT * FROM project_plan_release_cte
        ORDER BY
            sort desc
    </select>

    <select id="listUserPlan" resultType="com.wbyy.pms.modules.project.plan.domain.vo.UserPlanVo">
        WITH RECURSIVE user_plan AS (
            -- 初始查询：找到用户负责的任务（叶子节点）
            SELECT
            ppr.id AS planId,
            ppr.name AS fullPath, -- 初始化 fullPath 为当前任务名
            pb.id AS projectId,
            pb.name AS projectName,
            pb.number AS projectNumber,
            ppr.plan_end_date AS planEndDate,
            0 AS level,
            ppr.parent_id AS pre_parent_id
            FROM project_plan_release ppr
            LEFT JOIN project_base pb ON pb.id = ppr.project_id
            WHERE EXISTS (
                SELECT 1
                FROM project_plan_release_user ppru
                LEFT JOIN project_plan_release_relate pprr ON ppru.project_plan_id = pprr.project_plan_id
                WHERE ppru.user_id = #{userId}
                AND pprr.project_plan_id IS NULL
                AND ppru.project_plan_id = ppr.id
            )
            <if test="projectId != null">
                and pb.id = #{projectId}
            </if>
            <if test="searchValue !=null and searchValue != ''">
                and ( ppr.name like concat('%',#{searchValue},'%') or pb.name like concat('%',#{searchValue},'%') )
            </if>
            <if test="startTime != null">
                and ppr.plan_end_date &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and ppr.plan_end_date &lt;= #{endTime}
            </if>
            AND pb.del_flag = 0
            UNION ALL
            -- 递归部分：向上查找父节点，并拼接路径
            SELECT
            up.planId, -- 始终保持初始节点的 ID
            CONCAT(ppr2.name, '/', up.fullPath) AS fullPath, -- 向上拼接路径
            up.projectId,
            up.projectName,
            up.projectNumber,
            up.planEndDate,
            up.level + 1 AS level,
            ppr2.parent_id AS pre_parent_id
            FROM project_plan_release ppr2
            JOIN user_plan up ON ppr2.id = up.pre_parent_id
            WHERE up.level &lt; 50 -- 防止无限递归
        )
        -- 最终结果：只取每个初始 planId 的完整路径的全链路
        SELECT
        planId,
        projectId,
        projectName,
        projectNumber,
        MAX(fullPath) AS planName, -- 取层级最深的 fullPath（即完整路径）
        planEndDate
        FROM user_plan
        GROUP BY planId, projectId, projectName,projectNumber, planEndDate
        order by planEndDate
    </select>

    <select id="selectAllByUserIdAndDate" resultType="com.wbyy.pms.modules.project.plan.domain.vo.DutyPlanVO">
        SELECT
            ppr.id,
            ppr.project_id,
            ppr.`name`,
            pb.`name` as projectName,
            ppr.plan_start_date,
            ppr.plan_end_date,
            pb.number,
            pb.group_contract_number,
            ppr.reality_start_date,
            ppr.reality_end_date,
            CASE
            WHEN ppfr_stats.notDoneNum > 0 THEN 0
            WHEN ppfr_stats.notDoneNum = 0 THEN 1
            ELSE NULL
            END AS frontTaskFlag
        FROM
            project_plan_release ppr
            join project_base pb on pb.id = ppr.project_id
            LEFT JOIN (
            SELECT
            ppfr.project_plan_id,
            SUM(IF(ppr.reality_end_date IS NULL, 1, 0)) as notDoneNum
            FROM
            project_plan_front_release ppfr
            JOIN project_plan_release ppr ON ppfr.front_plan_id = ppr.id
            GROUP BY
            ppfr.project_plan_id
            ) ppfr_stats ON ppfr_stats.project_plan_id = ppr.id
        WHERE
            pb.del_flag = 0
            AND EXISTS (
            SELECT 1 FROM project_plan_release_user ppru
            LEFT JOIN project_plan_release_relate pprr ON ppru.project_plan_id = pprr.project_plan_id
            WHERE ppru.user_id = #{userId}
            AND pprr.project_plan_id IS NULL
            AND ppru.project_plan_id = ppr.id
            )
            <if test="dto.name != null and dto.name != ''">
                AND lower(ppr.name) like lower(concat('%', #{dto.name}, '%'))
            </if>
            <if test="dto.isEffective != null and dto.isEffective">
                AND pb.`status` IN ( 0, 1 )
            </if>
            <if test="start != null">
                <choose>
                    <when test="dto.start == 1">
                        and ppr.plan_start_date &lt;= #{start}
                    </when>
                    <otherwise>
                        and ppr.plan_start_date &gt; #{start}
                    </otherwise>
                </choose>
            </if>
            <if test="end != null">
                and ppr.plan_end_date &lt;= #{end}
            </if>
            <if test="dto.isFinish != null">
                <choose>
                    <when test="dto.isFinish == 1">
                        and ppr.reality_end_date is not null
                    </when>
                    <otherwise>
                        and ppr.reality_end_date is null
                    </otherwise>
                </choose>
            </if>
            <if test="dto.projectId != null">
                and ppr.project_id = #{dto.projectId}
            </if>
    </select>

    <select id="selectByRelatedPlanId" resultType="com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease">
        SELECT
            *
        FROM
            project_plan_release ppr
        WHERE
            EXISTS (
                SELECT
                    1
                FROM
                    project_plan_release_relate pprr
                WHERE
                    ppr.id = pprr.project_plan_id
                  AND pprr.project_plan_relate_id = #{relatedPlanId}
            )
    </select>


    <update id="updateBatchWithNull">
        UPDATE project_plan_release
        SET
        <foreach collection="list" item="item" separator=",">
            reality_progress = CASE WHEN id = #{item.id} THEN #{item.realityProgress} ELSE reality_progress END,
            reality_start_date = CASE WHEN id = #{item.id} THEN #{item.realityStartDate} ELSE reality_start_date END,
            reality_end_date = CASE WHEN id = #{item.id} THEN #{item.realityEndDate} ELSE reality_end_date END,
            reality_construction_period = CASE WHEN id = #{item.id} THEN #{item.realityConstructionPeriod} ELSE reality_construction_period END
        </foreach>
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>


    <select id="getFullPath" resultType="java.lang.String">
        WITH RECURSIVE plan_tree AS (
        SELECT
        id,
        parent_id,
        NAME as full_path,
        0 as level
        FROM
        project_plan_release
        WHERE
        id = #{id}

        UNION ALL
        SELECT
        ppr.id,
        ppr.parent_id,
        concat(ppr.name,'/',tree.full_path) as full_path,
        tree.level+1
        FROM
        project_plan_release ppr
        JOIN plan_tree tree ON ppr.id = tree.parent_id
        WHERE tree.level &lt; 50  -- 防止循环引用导致无限递归
        )
        SELECT full_path
        FROM
        plan_tree order by level desc limit 1;
    </select>
    <select id="selectOptions" resultType="com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease">
        SELECT
            t1.id,
            t1.project_id,
            t1.name,
            t1.sort,
            t1.wbs,
            t1.parent_id,
            if (t2.id is null, false, true) as has_contract_node
        FROM
            project_plan_release t1
                LEFT JOIN contract_node_plan t2 ON t1.project_id = t2.project_id
                AND t1.id = t2.plan_id
                and t2.contract_node_id = #{contractNodeId}
            and t2.del_flag=0

        where
            t1.project_id = #{projectId}

        ORDER BY t1.sort
    </select>

    <select id="findByPlanIds" resultType="com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease">
        SELECT
            ppr.id,
            ppr.`name`,
            ppr.reality_progress,
            ppr.plan_start_date,
            ppr.plan_end_date,
            group_concat( DISTINCT ppru.real_name ) projectPlanUserNames,
            ppr.reality_start_date,
            ppr.reality_end_date,
            ppr.project_id
        FROM
            project_plan_release_user ppru
                JOIN project_plan_release ppr ON ppru.project_plan_id = ppr.id
            <if test="planIds != null and planIds.size() > 0">
                and ppr.id IN
                <foreach collection="planIds" item="planId" open="(" close=")" separator=",">
                    #{planId}
                </foreach>
            </if>
        GROUP BY
            ppr.id
    </select>

    <select id="findByUserIdsAndProjectIdAndTime"
            resultType="com.wbyy.pms.modules.project.plan.domain.vo.DutyPlanDetailVO">
        SELECT ppr.id,
               ppr.`name`,
               ppr.plan_start_date,
               ppr.plan_end_date,
               ppr.reality_progress,
               ppr.reality_work_hours,
               ppr.reality_end_date,
               pb.id     AS projectId,
               pb.`name` AS projectName,
               pb.number,
               ppru.user_id,
               ppru.work_number,
               ppru.real_name
        FROM
        project_plan_release_user ppru
            JOIN project_plan_release ppr ON ppru.project_plan_id = ppr.id
        <if test="startTime != null and endTime != null">
            AND ppr.plan_end_date &gt;= #{startTime}
                AND ppr.plan_start_date &lt;= #{endTime}
        </if>
        JOIN project_base pb ON ppr.project_id = pb.id
        <if test="projectId != null">
            AND pb.id = #{projectId}
        </if>
        AND pb.del_flag = 0
        WHERE 1 = 1
        <if test="userIds != null and userIds.size() > 0">
            AND ppru.user_id IN
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        AND NOT EXISTS (SELECT 1
                        FROM project_plan_release_relate pprr
                        WHERE pprr.project_plan_id = ppru.project_plan_id)
        order by ppru.user_id asc
    </select>

    <select id="selectByUserIdByProjectIdByTimeRange" resultType="com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease">
        select ppr.id,
               ppr.project_id,
               ppr.sort,
               ppr.wbs,
               ppr.name,
               ppr.parent_id,
               ppr.milestone_flag,
               ppr.grade,
               ppr.income_ratio,
               ppr.plan_start_date,
               ppr.plan_end_date,
               ppr.plan_construction_period,
               ppr.reality_start_date,
               ppr.reality_end_date,
               ppr.reality_construction_period,
               ppr.reality_progress,
               ppr.duty_rule,
               ppr.expected_docs,
               ppr.reality_work_hours,
               ppr.description,
               ppr.create_by,
               ppr.create_name_by,
               ppr.create_time,
               ppr.update_by,
               ppr.update_name_by,
               ppr.update_time,
            pb.name as projectName,
            pb.number as projectNumber
        from project_plan_release ppr
                 left join project_plan_release_user ppru on ppr.id = ppru.project_plan_id
                 left join project_base pb on ppr.project_id = pb.id
        LEFT JOIN project_plan_release_relate pprr ON ppr.id = pprr.project_plan_id
        AND pprr.project_plan_id IS NULL
        where ppru.user_id = #{userId}
        <if test="null != projectId">
            and ppr.project_id = #{projectId}
        </if>
        and ppr.plan_start_date &lt;= #{endTime}
        and ppr.plan_end_date &gt;= #{startTime}
        and pb.del_flag = 0
    </select>

    <select id="selectPlanVoByIds" resultType="com.wbyy.pms.modules.project.plan.domain.vo.ProjectPlanDetailVO">
        select ppr.id,
        ppr.project_id,
        ppr.sort,
        ppr.wbs,
        ppr.name,
        ppr.parent_id,
        ppr.milestone_flag,
        ppr.grade,
        ppr.income_ratio,
        ppr.plan_start_date,
        ppr.plan_end_date,
        ppr.plan_construction_period,
        ppr.reality_start_date,
        ppr.reality_end_date,
        ppr.reality_construction_period,
        ppr.reality_progress,
        ppr.duty_rule,
        ppr.expected_docs,
        ppr.reality_work_hours,
        ppr.description,
        ppr.create_by,
        ppr.create_name_by,
        ppr.create_time,
        ppr.update_by,
        ppr.update_name_by,
        ppr.update_time,
        pb.name as projectName,
        pb.number as projectNumber,
        ppru.work_hour as workHour
        from project_plan_release ppr
        left join project_plan_release_user ppru on ppr.id = ppru.project_plan_id
        left join project_base pb on ppr.project_id = pb.id
        where ppr.id in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
        and pb.del_flag = 0
    </select>

    <select id="findByUserIdsAndTime" resultType="com.wbyy.pms.modules.project.plan.domain.vo.DutyPlanDailyVO">
        SELECT ppru.user_id,
               ppr.plan_start_date,
               ppr.plan_end_date
        FROM project_plan_release ppr
                 JOIN project_plan_release_user ppru ON ppr.id = ppru.project_plan_id
        LEFT JOIN project_base pb ON ppr.project_id = pb.id
                 LEFT JOIN project_plan_release_relate pprr ON ppr.id = pprr.project_plan_id
            AND pprr.project_plan_id IS NULL
        WHERE
        ppru.user_id IN
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        <if test="projectId != null and projectType == 1">
            AND ppru.project_id = #{projectId}
            AND ppr.project_id = #{projectId}
        </if>
        AND ppr.plan_start_date &lt;= #{endDate}
        AND ppr.plan_end_date &gt;= #{startDate}
        AND pb.del_flag = 0
        group by ppru.project_plan_id, ppru.user_id
    </select>

    <select id="listLatestFeedbackPlan" resultType="com.wbyy.pms.modules.project.plan.domain.vo.UserPlanVo">
        WITH RECURSIVE user_plan AS (
        -- 初始查询：找到用户负责的任务（叶子节点）
        SELECT
        ppr.id AS planId,
        ppr.name AS fullPath, -- 初始化 fullPath 为当前任务名
        pb.id AS projectId,
        pb.name AS projectName,
        pb.number AS projectNumber,
        ppr.plan_end_date AS planEndDate,
        0 AS level,
        ppr.parent_id AS pre_parent_id
        FROM project_plan_release ppr
        LEFT JOIN project_base pb ON pb.id = ppr.project_id
        WHERE ppr.id in
            <foreach collection="planIds" open="(" close=")" item="planId" separator=",">
                #{planId}
            </foreach>
        AND pb.del_flag = 0
        UNION ALL
        -- 递归部分：向上查找父节点，并拼接路径
        SELECT
        up.planId, -- 始终保持初始节点的 ID
        CONCAT(ppr2.name, '/', up.fullPath) AS fullPath, -- 向上拼接路径
        up.projectId,
        up.projectName,
        up.projectNumber,
        up.planEndDate,
        up.level + 1 AS level,
        ppr2.parent_id AS pre_parent_id
        FROM project_plan_release ppr2
        JOIN user_plan up ON ppr2.id = up.pre_parent_id
        WHERE up.level &lt; 50 -- 防止无限递归
        )
        -- 最终结果：只取每个初始 planId 的完整路径的全链路
        SELECT
        planId,
        projectId,
        projectName,
        projectNumber,
        MAX(fullPath) AS planName, -- 取层级最深的 fullPath（即完整路径）
        planEndDate
        FROM user_plan
        GROUP BY planId, projectId, projectName,projectNumber, planEndDate
        order by planEndDate
    </select>
</mapper>
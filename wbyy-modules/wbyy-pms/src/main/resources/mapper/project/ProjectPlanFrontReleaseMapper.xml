<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.project.planfront.mapper.ProjectPlanFrontReleaseMapper">
    <select id="selectFrontPlanByPlanIdAndProjectId" resultType="com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease">
        SELECT
            ppr.id,
            ppr.`name`,
            pb.id AS projectId,
            pb.`name` AS projectName,
            ppr.plan_construction_period,
            ppr.plan_start_date,
            ppr.plan_end_date,
            ppr.reality_start_date,
            ppr.reality_end_date,
            ppr.reality_progress
        FROM
            project_plan_release ppr
                JOIN project_base pb ON ppr.project_id = pb.id
                AND pb.del_flag = 0
        WHERE
            EXISTS (
                SELECT
                    1
                FROM
                    project_plan_front_release ppfr
                WHERE
                    ppfr.project_plan_id = #{planId}
                  AND ppfr.project_id = #{projectId}
                  AND ppfr.front_plan_id = ppr.id)
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.project.teamperson.mapper.ProjectTeamPersonMapper">
    <select id="listPmByProjectIds" resultType="com.wbyy.pms.modules.project.teamperson.domain.ProjectTeamPersonPO">
        select ptp.id, ptp.project_id as projectId, ptp.project_role_id as projectRoleId, ptp.user_id as userId,
        ptp.real_name,ptp.work_number
        from project_team_person ptp
        where  ptp.project_id in
        <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
            #{projectId}
        </foreach>
        and ptp.project_role_id in
        <foreach collection="projectRoleIds" item="projectRoleId" open="(" close=")" separator=",">
            #{projectRoleId}
        </foreach>
        and ptp.del_flag=0
        GROUP BY ptp.project_id  ,ptp.project_role_id,ptp.id,  ptp.user_id
    </select>

    <select id="listProjectIdsByUserId" resultType="java.lang.Long">
        select project_id from project_team_person where del_flag=0
        <if test="userId != null and userId != ''">
            and user_id=#{userId}
        </if>
    </select>

    <select id="listProjectRoleId" resultType="java.lang.Long">
        select role_dept_id from project_base_role where
        project_id in
        <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
            #{projectId}
        </foreach>
        and del_flag=0  and name_en='PM'
    </select>

    <select id="listPmProjectIdsByUserId" resultType="java.lang.Long">
        SELECT
            distinct(ptp.project_id)
        FROM
            project_team_person ptp
        WHERE
            ptp.user_id = #{userId}
          AND EXISTS ( SELECT 1 FROM project_base_role pbr WHERE pbr.del_flag = 0 AND pbr.name_en = 'PM' AND pbr.role_dept_id = ptp.project_role_id )
          AND ptp.del_flag = 0
    </select>

    <select id="findPmByNumbers" resultType="java.lang.Long">
        SELECT
            ptp.user_id
        FROM
            project_team_person ptp
                JOIN project_base pb ON ptp.project_id = pb.id
                AND pb.del_flag = 0
                AND pb.group_contract_number = #{groupContractNumber}
                AND pb.company_contract_number = #{companyContractNumber}
        WHERE
            ptp.del_flag = 0
          AND EXISTS (
            SELECT
                1
            FROM
                project_base_role pbr
            WHERE
                pbr.del_flag = 0
              AND pbr.name_en = 'PM'
              AND pbr.role_dept_id = ptp.project_role_id
        )
    </select>
</mapper>
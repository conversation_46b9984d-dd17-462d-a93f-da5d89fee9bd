<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.project.disclosure.mapper.ProjectDisclosureMapper">

    <select id="findList" resultType="com.wbyy.pms.modules.project.disclosure.domain.ProjectDisclosure">
        SELECT
            t1.*
        FROM
            project_disclosure t1
            -- 不再限制一个交底只能建一个项目
 --               LEFT JOIN project_base t2 ON t1.company_contract_number = t2.company_contract_number
  --              AND t2.del_flag = 0
        WHERE
            t1.del_flag = 0
    --      and t2.id is null
          and t1.pm_user_id = #{pmUserId}
    </select>

    <select id="getByNumber" resultType="com.wbyy.pms.modules.project.disclosure.domain.ProjectDisclosure">
        select id,
               oa_workflow_id,
               oa_workflow_number,
               oa_workflow_name,
               group_contract_number,
               company_contract_number,
               oa_workflow_create_time,
               pm_user_id,
               oa_workflow_pm_name,
               del_flag,
               create_by,
               create_name_by,
               create_time,
               update_by,
               update_name_by,
               update_time
        from project_disclosure
        where group_contract_number = #{groupContractNumber}
          and company_contract_number = #{companyContractNumber}
          and del_flag = 0
    </select>
</mapper>
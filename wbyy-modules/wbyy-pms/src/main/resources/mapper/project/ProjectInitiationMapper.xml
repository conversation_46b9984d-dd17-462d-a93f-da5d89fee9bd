<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.project.initiation.mapper.ProjectInitiationMapper">

    <select id="findList" resultType="com.wbyy.pms.modules.project.initiation.domain.ProjectInitiation">
        SELECT
            t1.*
        FROM
            project_initiation t1
                LEFT JOIN project_base t2 ON t1.oa_workflow_id = t2.oa_workflow_id
                AND t2.del_flag = 0
        WHERE
            t1.del_flag = 0
          and t2.id is null
          and t1.pm_user_id = #{pmUserId}

    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.configuration.projecttemplate.plan.mapper.ProjectTemplatePlanMapper">

    <select id="selectAllParentNodeById"
            resultType="com.wbyy.pms.modules.configuration.projecttemplate.plan.domain.ProjectTemplatePlan">
        WITH RECURSIVE project_template_plan_cte AS (
            SELECT id,
                   parent_id,
                   NAME,
                   sort,
                   wbs,
                   construction_period
            FROM project_template_plan
            WHERE id = #{id} AND del_flag = 0

            UNION ALL

            SELECT r.id,
                   r.parent_id,
                   r.NAME,
                   r.sort,
                   r.wbs,
                   r.construction_period
            FROM project_template_plan r
                     JOIN project_template_plan_cte c ON r.id = c.parent_id
            where project_template_id = #{projectTemplateId} AND del_flag = 0
        )
        SELECT * FROM project_template_plan_cte
        ORDER BY
            sort
    </select>
    <select id="selectAllChildrenNodeById"
            resultType="com.wbyy.pms.modules.configuration.projecttemplate.plan.domain.ProjectTemplatePlan">
        WITH RECURSIVE project_template_plan_cte AS (
            SELECT id,
                   parent_id,
                   NAME,
                   sort,
                   wbs,
                   construction_period
            FROM project_template_plan
            WHERE parent_id = #{id} AND del_flag = 0

            UNION ALL

            SELECT r.id,
                   r.parent_id,
                   r.NAME,
                   r.sort,
                   r.wbs,
                   r.construction_period
            FROM project_template_plan r
                     JOIN project_template_plan_cte c ON c.id = r.parent_id
            where project_template_id = #{projectTemplateId} AND del_flag = 0
        )
        SELECT * FROM project_template_plan_cte
        ORDER BY
            sort
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.configuration.basicparameter.type.mapper.BusinessTypeMapper">
    <select id="getMaxSort" resultType="java.lang.Integer">
        select max(sort) from business_type where del_flag=0
    </select>

    <select id="listByParentId" resultType="com.wbyy.pms.modules.configuration.basicparameter.type.domain.BusinessTypePO">
        WITH RECURSIVE TypeHierarchy AS (select id,
                                                parent_id,
                                                name,
                                                disable,
                                                sort,
                                                del_flag,
                                                create_by,
                                                create_time,
                                                update_by,
                                                update_time,
                                                create_name_by,
                                                update_name_by
                                         from business_type
                                         where parent_id = #{parentId}
                                         union all
                                         select pt.*
                                         from business_type pt
                                                  inner join TypeHierarchy th on pt.parent_id = th.id)
        select id,
               parent_id,
               name,
               disable,
               sort,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               create_name_by,
               update_name_by
        from TypeHierarchy
        order by sort asc
    </select>

    <delete id="removeByParentId">
        WITH RECURSIVE DeptHierarchy AS (
            -- 初始选择：从指定的父部门集合开始
            SELECT id
            FROM business_type
            WHERE parent_id IN 
            <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
                #{parentId}
            </foreach>

            UNION ALL

            -- 递归部分：连接自身以找到所有下级部门
            SELECT d.id
            FROM business_type d
                     INNER JOIN DeptHierarchy dh ON d.parent_id = dh.id
        )
-- 直接删除所有找到的dept_id对应的记录
        DELETE FROM business_type WHERE id IN (
            SELECT DeptHierarchy.id FROM DeptHierarchy
        );
    </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.configuration.basicparameter.type.mapper.BusinessTypeObjectMapper">

    <select id="selectByProjectTypeIds"
            resultType="com.wbyy.pms.modules.configuration.basicparameter.type.domain.BusinessTypeObjectPO">
        select pto.id, pto.project_type_id, pto.object_info_id, pto.del_flag, pto.create_by, pto.create_name_by,
        pto.create_time, pto.update_by, pto.update_name_by, pto.update_time,oi.name as objectInfoName
        from business_type_object pto
        left join object_info oi on pto.object_info_id = oi.id
        where pto.project_type_id in
        <foreach collection="projectTypeIds" separator="," open="(" close=")" item="projectTypeId">
            #{projectTypeId}
        </foreach>
        and pto.del_flag=0 and oi.del_flag=0
    </select>

    <select id="selectObjectGroupRelationsByProjectTypeId"
            resultType="com.wbyy.pms.modules.configuration.basicparameter.type.domain.vo.ObjectGroupRelationVo">
        select oi.id as objectInfoId,og.group_key as groupKey
        from business_type_object pto
        left join object_info oi on pto.object_info_id = oi.id
        left join object_group og on oi.object_group_id = og.id
        where pto.project_type_id=#{projectTypeId}
        and pto.del_flag=0 and oi.del_flag=0 and og.del_flag=0
    </select>

    <select id="selectObjectInfoIdByProjectTypeIdByGroupKey" resultType="java.lang.Long">
        select oi.id
        from business_type_object pto
                 left join object_info oi on pto.object_info_id = oi.id
                 left join object_group og on oi.object_group_id = og.id
        where pto.project_type_id = #{projectTypeId}
          and og.group_key = #{groupKey}
          and pto.del_flag = 0
          and oi.del_flag = 0
          and og.del_flag = 0
        limit 1
    </select>
</mapper>
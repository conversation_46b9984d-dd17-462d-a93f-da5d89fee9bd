<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.project.base.mapper.ProjectBaseMapper">

    <select id="selectAuthList" resultType="com.wbyy.pms.modules.project.base.domain.ProjectBase">
        select pb.id, pb.number, pb.name, pb.type_id, pb.type_name, pb.center_dept_id, pb.center_dept_code, pb.dept_id,
        pb.status, pb.del_flag, pb.create_by, pb.create_name_by, pb.create_time, pb.update_by, pb.update_name_by,
        pb.update_time, pb.del_reason
        from project_base pb where pb.del_flag=0
        <if test="searchValue != null and searchValue != ''">
            and (pb.name like concat('%',#{searchValue},'%') or pb.number like concat('%',#{searchValue},'%'))
        </if>
        <if test="admin != null and admin==false">
            and (
            <choose>
                <when test="userInProjectIds != null and userInProjectIds.size()>0">
                    pb.id in
                    <foreach collection="userInProjectIds" separator="," open="(" close=")" item="userInProjectId">
                        #{userInProjectId}
                    </foreach>
                </when>
                <otherwise>
                    1=2
                </otherwise>
            </choose>
            or
            <choose>
                <when test="userManageDepts != null and userManageDepts.size()>0">
                    pb.dept_id in
                    <foreach collection="userManageDepts" item="userManageDept" open="(" close=")" separator=",">
                        #{userManageDept}
                    </foreach>
                </when>
                <otherwise>
                    1=2
                </otherwise>
            </choose>
            )
        </if>
        order by pb.create_time desc
    </select>

    <select id="getDeptIdById" resultType="java.lang.Long">
        select dept_id from project_base where id=#{id} and del_flag=0
    </select>

    <select id="selectDutyProjectsByUserId" resultType="com.wbyy.pms.modules.project.base.domain.vo.DutyProjectVO">
        SELECT
            pb.id,
            pb.`name`,
            pb.number,
            COALESCE ( pia.customer, pib.customer, pic.sponsor, pid.sponsor ) AS customer
        FROM
            project_base pb
                LEFT JOIN project_info_act pia ON pia.id = pb.id
                AND pb.center_dept_code = 'act' AND pia.del_flag = 0
                LEFT JOIN project_info_benao pib ON pib.id = pb.id
                AND pb.center_dept_code = 'benao' AND pib.del_flag = 0
                LEFT JOIN project_info_crcgdbe pic ON pic.id = pb.id
                AND pb.center_dept_code = 'crcgdbe' AND pic.del_flag = 0
                LEFT JOIN project_info_drdc pid ON pid.id = pb.id
                AND pb.center_dept_code = 'drdc' AND pid.del_flag = 0
        WHERE
            pb.del_flag = 0
        AND EXISTS (
        SELECT 1 FROM project_team_person ptp
        WHERE ptp.user_id = #{userId}
        AND ptp.project_id = pb.id
        and ptp.del_flag = 0
        )
        <if test="searchValue != null and searchValue != ''">
            AND ( lower(pb.number) like lower(concat('%', #{searchValue}, '%')) or lower(pb.name) like lower(concat('%', #{searchValue}, '%')) )
        </if>
        <if test="isEffective != null and isEffective">
            AND pb.`status` IN ( 0, 1 )
        </if>
        order by pb.create_time asc
    </select>

    <select id="selectDutyProjectBaseByUserId" resultType="com.wbyy.pms.modules.project.base.domain.ProjectBase">
        select pb.id, pb.number, pb.name, pb.type_id, pb.type_name, pb.center_dept_id, pb.center_dept_code, pb.dept_id,
        pb.status, pb.del_flag, pb.create_by, pb.create_name_by, pb.create_time, pb.update_by, pb.update_name_by,
        pb.update_time, pb.del_reason
        FROM
        project_base pb
        WHERE
            pb.del_flag = 0
        and exists (select 1 from project_team_person ptp where ptp.project_id = pb.id AND ptp.user_id = #{userId} AND ptp.del_flag = 0)
        <if test="searchValue != null and searchValue != ''">
            AND ( lower(pb.number) like lower(concat('%', #{searchValue}, '%')) or lower(pb.name) like lower(concat('%', #{searchValue}, '%')) )
        </if>
        <if test="isEffective != null and isEffective">
            AND pb.`status` IN ( 0, 1 )
        </if>
        order by pb.create_time asc
    </select>

    <select id="selectUserProject" resultType="com.wbyy.pms.modules.project.base.domain.vo.DutyProjectVO">
        SELECT
        pb.id,
        pb.`name`,
        pb.number
        FROM
        project_base pb
        WHERE
        pb.del_flag = 0
        AND  EXISTS (
        SELECT 1 FROM project_plan_release_user ppru
        LEFT JOIN project_plan_release_relate pprr ON ppru.project_plan_id = pprr.project_plan_id
        WHERE ppru.user_id = #{userId}
        AND pprr.project_plan_id IS NULL
        AND ppru.project_id = pb.id
        )
        <if test="searchValue != null and searchValue != ''">
            AND ( pb.number like concat('%', #{searchValue}, '%') or pb.name like concat('%', #{searchValue}, '%') )
        </if>
        <if test="isEffective != null and isEffective">
            AND pb.`status` IN ( 0, 1 )
        </if>
        order by pb.create_time asc
    </select>

    <select id="selectById" resultType="com.wbyy.pms.modules.project.base.domain.ProjectBase">
        select id,
               number,
               name,
               type_id,
               type_name,
               center_dept_id,
               center_dept_code,
               dept_id,
               status,
               del_flag,
               create_by,
               create_name_by,
               create_time,
               update_by,
               update_name_by,
               update_time,
               del_reason,
               template_id,
               group_contract_number,
               company_contract_number,
               contract_number,
               plan_start_date,
               oa_workflow_id,
               source
        from project_base
        where id = #{id}
          and del_flag = 0
    </select>

    <select id="findByUserIdsAndProjectIdsAndTypeId" resultType="com.wbyy.pms.modules.team.taskstatistics.domain.vo.TaskStatisticsVO">
        SELECT
            pb.id AS projectId,
            pb.`name` AS projectName,
            pb.number,
            pb.type_id,
            pb.type_name,
            pb.dept_id,
            ppr.id,
            ppr.`name`,
            ppr.reality_progress,
            ppr.plan_start_date,
            ppr.plan_end_date,
            ppr.milestone_flag
        FROM
            project_base pb
                JOIN project_plan_release_user ppru ON pb.id = ppru.project_id
            <if test="userIds != null and userIds.size() > 0">
                AND ppru.user_id IN
                <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
                join project_plan_release ppr on ppru.project_plan_id = ppr.id
        WHERE
            pb.del_flag = 0
        <if test="projectId != null">
            AND pb.id = #{projectId}
        </if>
        <if test="typeId != null">
            AND pb.type_id = #{typeId}
        </if>
        <if test="searchValue != null and searchValue != ''">
            AND ( pb.number like concat('%', #{searchValue}, '%') or pb.name like concat('%', #{searchValue}, '%') )
        </if>
        group by ppru.project_plan_id
    </select>

    <select id="findTaskByUserIdsAndProjectIdsAndTypeId"
            resultType="com.wbyy.pms.modules.team.taskstatistics.domain.vo.TaskStatisticsTotalVO">
        SELECT COUNT(DISTINCT ppr.id)                  AS total_count,
               COUNT(DISTINCT CASE
                                  WHEN ppr.reality_progress = 100 AND ppr.reality_end_date &lt;= ppr.plan_end_date
                                      THEN ppr.id END) AS normalCount,
               COUNT(DISTINCT CASE
                                  WHEN ppr.reality_progress = 100 AND ppr.reality_end_date &gt; ppr.plan_end_date
                                      THEN ppr.id END) AS delayCount
        FROM
        project_plan_release ppr
            join project_base pb on ppr.project_id = pb.id and pb.del_flag = 0
        <if test="projectId != null">
            AND pb.id = #{projectId}
        </if>
        <if test="typeId != null">
            AND pb.type_id = #{typeId}
        </if>
        <if test="searchValue != null and searchValue != ''">
            AND (pb.number like concat('%', #{searchValue}, '%') or pb.name like concat('%', #{searchValue}, '%'))
        </if>
        WHERE
        EXISTS ( SELECT 1
                 FROM project_plan_release_user ppru WHERE ppru.project_plan_id = ppr.id
        <if test="userIds != null and userIds.size() > 0">
            AND ppru.user_id IN
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        )
    </select>

    <select id="selectUserProjectByCondition" resultType="com.wbyy.pms.modules.project.base.domain.vo.DutyProjectVO">
        SELECT
        pb.id,
        pb.`name`,
        pb.number
        FROM
        project_base pb
        WHERE
        pb.del_flag = 0
        AND  EXISTS (
        SELECT 1 FROM project_plan_release_user ppru
        <if test="startDate != null and endDate != null">
            join project_plan_release ppr on ppru.project_plan_id = ppr.id
        </if>
        LEFT JOIN project_plan_release_relate pprr ON ppru.project_plan_id = pprr.project_plan_id
        WHERE pprr.project_plan_id IS NULL
        AND ppru.project_id = pb.id
        <if test="startDate != null and endDate != null">
            and NOT (ppr.plan_end_date &lt; #{startDate} OR ppr.plan_start_date &gt; #{endDate})
        </if>
        <if test="userIds != null and userIds.size() > 0">
            AND ppru.user_id IN
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        )
        <if test="dto.searchValue != null and dto.searchValue != ''">
            AND ( pb.number like concat('%', #{dto.searchValue}, '%') or pb.name like concat('%', #{dto.searchValue}, '%') )
        </if>
        <if test="dto.isEffective != null and dto.isEffective">
            AND pb.`status` IN ( 0, 1 )
        </if>
        order by pb.create_time asc
    </select>

    <select id="selectIgnoreDeleteByIds" resultType="com.wbyy.pms.modules.project.base.domain.ProjectBase">
        select id, number, name, type_id, type_name, center_dept_id, center_dept_code, dept_id, status, del_flag,
        create_by, create_name_by, create_time, update_by, update_name_by, update_time, del_reason, template_id,
        group_contract_number, company_contract_number, contract_number, plan_start_date, oa_workflow_id, source,
        oa_form_data_id from project_base where id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
    </select>

    <select id="selectDynamicTableList" resultType="map">
        select pb.id as id, pb.number as number, pb.name as name, pb.type_id as typeId, pb.type_name as typeName,
               pb.center_dept_id as centerDeptId, pb.center_dept_code as centerDeptCode, pb.dept_id as deptId,
               pb.status as status, pb.create_by as createBy, pb.create_name_by as createNameBy,
               pb.create_time as createTime, pb.update_by as updateBy, pb.update_name_by as updateNameBy,
               pb.update_time as updateTime, pb.oa_workflow_id as oaWorkflowId, pb.oa_form_data_id as oaFormDataId,
               pb.group_contract_number as groupContractNumber, pb.company_contract_number as companyContractNumber,
               pb.contract_number as contractNumber, pb.plan_start_date as planStartDate, pb.experiment_type as experimentType,
               pb.customer_contacts as customerContacts, pb.customer_contacts_tel as customerContactsTel,
               pb.summary as summary
        <if test="request.selectCustomFields != null and request.selectCustomFields.size() > 0">
            , ${@com.wbyy.biz.common.form.common.query.QueryConditionBuilder@buildSelectColumns("dt", request.selectCustomFields)}
        </if>
        from project_base pb
        inner join base_object_data bod on pb.id = bod.base_id and bod.group_key = 'project' and bod.del_flag = 0 and bod.object_info_id = #{request.objectInfoId}
        left join ${request.dynamicTable} dt on bod.object_data_id = dt.id and dt.del_flag = 0
        where pb.del_flag=0
        <if test="request.admin != null and request.admin==false">
            and (
            <choose>
                <when test="request.userInProjectIds != null and request.userInProjectIds.size()>0">
                    pb.id in
                    <foreach collection="request.userInProjectIds" separator="," open="(" close=")" item="userInProjectId">
                        #{userInProjectId}
                    </foreach>
                </when>
                <otherwise>
                    1=2
                </otherwise>
            </choose>
            or
            <choose>
                <when test="request.userManageDepts != null and request.userManageDepts.size()>0">
                    pb.dept_id in
                    <foreach collection="request.userManageDepts" item="userManageDept" open="(" close=")" separator=",">
                        #{userManageDept}
                    </foreach>
                </when>
                <otherwise>
                    1=2
                </otherwise>
            </choose>
            )
        </if>
        <if test="request.searchSystemFields != null and request.searchSystemFields.size() > 0">
            and
            <foreach collection="request.searchSystemFields" item="field" separator="AND">
                <if test="field.value != null">
                    ${@com.wbyy.biz.common.form.common.query.QueryConditionBuilder@buildSearchCondition("pb", field)}
                </if>
            </foreach>
        </if>
        <if test="request.searchCustomFields != null and request.searchCustomFields.size() > 0">
            and
            <foreach collection="request.searchCustomFields" item="field" separator="AND">
                <if test="field.value != null">
                    ${@com.wbyy.biz.common.form.common.query.QueryConditionBuilder@buildSearchCondition("dt", field)}
                </if>
            </foreach>
        </if>
        order by pb.create_time desc
    </select>
</mapper>
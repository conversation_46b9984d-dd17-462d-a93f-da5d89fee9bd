<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.contract.nodeplan.mapper.ContractNodePlanMapper">
    <select id="selectPlanIdsByNodeId" resultType="com.wbyy.pms.modules.contract.nodeplan.domain.ContractNodePlan">
        select id,
               contract_id,
               contract_node_id,
               project_id,
               project_name,
               project_number,
               plan_id,
               plan_name,
               plan_user_real_names,
               plan_end_date,
               plan_delete,
               del_flag,
               create_by,
               create_name_by,
               create_time,
               update_by,
               update_name_by,
               update_time
        from contract_node_plan
        where contract_node_id = #{contractNodeId}
          and del_flag = 0
    </select>

    <select id="listByIds" resultType="com.wbyy.pms.modules.contract.nodeplan.domain.ContractNodePlan">
        select cnp.id, cnp.contract_id, cnp.contract_node_id, cnp.project_id, cnp.project_name, cnp.project_number,
        cnp.plan_id, cnp.plan_name, cnp.plan_user_real_names, cnp.plan_end_date, cnp.plan_delete, cnp.del_flag,
        cnp.create_by, cnp.create_name_by, cnp.create_time, cnp.update_by, cnp.update_name_by, cnp.update_time,
        cn.name as contractNodeName, c.name as contractName
        from contract_node_plan cnp
        left join contract_node cn on cnp.contract_node_id = cn.id
        left join contract_base c on c.id=cn.contract_id
        where cnp.id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        and cnp.del_flag=0 and cn.del_flag=0 and c.del_flag=0
    </select>

    <select id="selectContractNodeRelationByIds" resultType="com.wbyy.pms.modules.contract.nodeplan.domain.ContractNodePlan">
        SELECT cnp.id, cnp.contract_id, cnp.contract_node_id, cnp.project_id, cnp.project_name, cnp.project_number,
        cnp.plan_id, cnp.plan_name, cnp.plan_user_real_names, cnp.plan_end_date, cnp.plan_delete, cnp.del_flag,
        cnp.create_by, cnp.create_name_by, cnp.create_time, cnp.update_by, cnp.update_name_by, cnp.update_time
        FROM contract_node_plan cnp
        WHERE cnp.contract_node_id IN
        (select scnp.contract_node_id from contract_node_plan scnp where scnp.id in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
        and scnp.del_flag=0)
        AND cnp.del_flag = 0
    </select>

    <select id="selectByContractId" resultType="com.wbyy.pms.modules.contract.nodeplan.domain.ContractNodePlan">
        select cnp.id,
               cnp.contract_id,
               cnp.contract_node_id,
               cnp.project_id,
               cnp.project_name,
               cnp.project_number,
               cnp.plan_id,
               cnp.plan_name,
               cnp.plan_user_real_names,
               cnp.plan_end_date,
               cnp.plan_delete,
               cnp.del_flag,
               cnp.create_by,
               cnp.create_name_by,
               cnp.create_time,
               cnp.update_by,
               cnp.update_name_by,
               cnp.update_time
        from contract_node_plan cnp
                 left join project_base pb on cnp.project_id = pb.id
        where cnp.contract_id = #{contractId}
          and cnp.del_flag = 0
          and pb.del_flag = 0
    </select>
</mapper>
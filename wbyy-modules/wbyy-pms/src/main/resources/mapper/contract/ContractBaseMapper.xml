<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.contract.base.mapper.ContractBaseMapper">

    <select id="selectByStatusAndType" resultType="com.wbyy.pms.modules.contract.base.domain.ContractBase">
        select *
        from (
        SELECT cb.id,
               cb.name,
               cb.group_contract_number,
               cb.company_contract_number,
               cb.contract_amount,
               cb.customer_name,
               cb.business_type_name,
               cb.`status`,
               cb.signing_date,
               cb.registration_date,
               cb.dept_id,
               pd.pm_user_id as user_id
        FROM contract_base cb
                 left join project_disclosure pd on pd.group_contract_number = cb.group_contract_number
            and pd.company_contract_number = cb.company_contract_number
            and pd.del_flag = 0
        WHERE cb.del_flag = 0
        <if test="dto.businessTypeIds != null and dto.businessTypeIds.size() > 0">
            AND cb.business_type_id IN
            <foreach collection="dto.businessTypeIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="dto.statusList != null and dto.statusList.size() > 0">
            AND cb.`status` IN
            <foreach collection="dto.statusList" open="(" close=")" item="status" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="dto.searchValue != null and dto.searchValue != ''">
            and (cb.`name` like concat('%', #{dto.searchValue}, '%')
                or cb.group_contract_number like concat('%', #{dto.searchValue}, '%')
                or cb.company_contract_number like concat('%', #{dto.searchValue}, '%'))
        </if>
        <if test="companyContractNumbers != null and companyContractNumbers.size() > 0">
            AND cb.company_contract_number IN
            <foreach collection="companyContractNumbers" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        ) temp
        where 1 = 1
        <!-- 数据范围过滤 -->
        ${dto.params.dataScope}
        order by temp.registration_date desc
    </select>

    <select id="baseTotal" resultType="com.wbyy.pms.modules.contract.base.domain.vo.BaseTotalVO">
        SELECT SUM(temp.contract_amount)                                    AS totalAmount,
               COALESCE(SUM(cn.paid_amount), 0)                             AS paidAmount,
               SUM(temp.contract_amount) - COALESCE(SUM(cn.paid_amount), 0) AS unpaidAmount
        from (
        SELECT cb.id,
               cb.contract_amount,
               cb.dept_id,
               pd.pm_user_id as user_id
        FROM contract_base cb
                 left join project_disclosure pd on pd.group_contract_number = cb.group_contract_number
            and pd.company_contract_number = cb.company_contract_number
            and pd.del_flag = 0
        WHERE cb.del_flag = 0
        <if test="dto.businessTypeIds != null and dto.businessTypeIds.size() > 0">
            AND cb.business_type_id IN
            <foreach collection="dto.businessTypeIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="dto.statusList != null and dto.statusList.size() > 0">
            AND cb.`status` IN
            <foreach collection="dto.statusList" open="(" close=")" item="status" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="dto.searchValue != null and dto.searchValue != ''">
            and (cb.`name` like concat('%', #{dto.searchValue}, '%')
                or cb.group_contract_number like concat('%', #{dto.searchValue}, '%')
                or cb.company_contract_number like concat('%', #{dto.searchValue}, '%'))
        </if>
        <if test="companyContractNumbers != null and companyContractNumbers.size() > 0">
            AND cb.company_contract_number IN
            <foreach collection="companyContractNumbers" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        ) temp
            LEFT JOIN (SELECT contract_id,
                              SUM(paid_amount) AS paid_amount
                       FROM contract_node
                       WHERE del_flag = 0
                       GROUP BY contract_id) cn ON temp.id = cn.contract_id
        where 1 = 1
        <!-- 数据范围过滤 -->
        ${dto.params.dataScope}
    </select>
</mapper>
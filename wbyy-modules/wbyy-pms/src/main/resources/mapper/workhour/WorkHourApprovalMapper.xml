<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.workhour.approval.mapper.WorkHourApprovalMapper">

    <sql id="commonResult">
        id,work_hour_detail_id,project_id,project_name,project_number,plan_id,entry_name,status,remark,work_hour,fill_date,
          description,real_name,work_number,approval_user_id,approval_real_name,approval_work_number,approval_time,create_by,create_name_by,create_time
    </sql>

    <select id="selectListByObject" resultType="com.wbyy.pms.modules.workhour.approval.domain.WorkHourApproval">
        SELECT
            <include refid="commonResult"></include>
        FROM
            work_hour_approval
        WHERE
            del_flag = 0
            and status in
            <foreach collection="statusList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="projectIds != null and projectIds.size() > 0">
                AND project_id IN
                <foreach collection="projectIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="dto.projectIdList != null and dto.projectIdList.size() > 0">
                AND project_id IN
                <foreach collection="dto.projectIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="createBy != null">
                and create_by = #{createBy}
            </if>
            <if test="approvalUserId != null">
                and approval_user_id = #{approvalUserId}
            </if>
            <if test="dto.searchValue != null and dto.searchValue != ''">
                and ( real_name like concat('%',#{dto.searchValue},'%') or work_number like concat('%',#{dto.searchValue},'%'))
            </if>
    </select>

    <select id="selectProjectByObject" resultType="com.wbyy.pms.modules.workhour.approval.domain.vo.ApprovalProjectVO">
        SELECT project_id     as id,
               project_name   as name,
               project_number as number
        FROM work_hour_approval
        WHERE del_flag = 0
          and status in
        <foreach collection="statusList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="projectIds != null and projectIds.size() > 0">
            AND project_id IN
            <foreach collection="projectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="approvalUserId != null">
            and approval_user_id = #{approvalUserId}
        </if>
        <if test="dto.searchValue != null and dto.searchValue != ''">
            and (project_number like concat('%', #{dto.searchValue}, '%') or
                 project_name like concat('%', #{dto.searchValue}, '%'))
        </if>
        GROUP BY project_id
    </select>
</mapper>
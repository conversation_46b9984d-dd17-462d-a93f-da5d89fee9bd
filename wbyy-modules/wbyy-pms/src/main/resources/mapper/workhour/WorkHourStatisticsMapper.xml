<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.workhour.statistics.mapper.WorkHourStatisticsMapper">

    <select id="byDept" resultType="com.wbyy.pms.modules.workhour.statistics.domain.vo.ByDeptVo">
        SELECT
            dept_id,
            dept_name,
            dept_sort,
            dept_parent_id,
            count( 1 ) AS fill_count,
            sum( standard_hour ) AS standard_hour,
            sum( fill_project_hour ) AS fill_project_hour,
            sum( fill_common_hour ) AS fill_common_hour,
            sum( fill_project_hour ) + sum( fill_common_hour ) AS fill_hour,
            sum( fill_project_hour )/(sum( fill_project_hour ) + sum( fill_common_hour )) AS project_hour_percent
        FROM (
            SELECT
                user_id,
                real_name,
                dept_id,
                dept_name,
                dept_sort,
                dept_parent_id,
                max( standard_hour ) AS standard_hour,-- 标准工时
                sum( fill_project_hour ) AS fill_project_hour,-- 填报项目总工时
                sum( fill_common_hour ) AS fill_common_hour -- 填报公共条目总工时
            FROM (
                SELECT
                    user_id,
                    real_name,
                    dept_id,
                    dept_name,
                    dept_sort,
                    dept_parent_id,
                    COALESCE ( max( standard_hour ), 0 ) AS standard_hour,-- 标准工时
                    0 AS fill_project_hour,-- 填报项目总工时
                    COALESCE ( sum( finish_hour ), 0 ) AS fill_common_hour -- 填报公共条目总工时
                FROM
                    work_hour_statistics
                WHERE
                    del_flag = 0
                    AND project_id IS NULL
                    <if test="deptId != null">
                        AND dept_ancestors like concat('%', #{deptId}, '%')
                    </if>
                    <if test="monthNumber != null">
                        AND month_number = #{monthNumber}
                    </if>
                    <if test="yearNumber != null">
                        AND year_number = #{yearNumber}
                    </if>
                    <!-- 数据范围过滤 -->
                    ${params.dataScope}
                GROUP BY
                    user_id,
                    real_name,
                    dept_id,
                    dept_name,
                    dept_sort,
                    dept_parent_id
                UNION ALL
                SELECT
                    user_id,
                    real_name,
                    dept_id,
                    dept_name,
                    dept_sort,
                    dept_parent_id,
                    COALESCE ( max( standard_hour ), 0 ) AS standard_hour,-- 标准工时
                    COALESCE ( sum( finish_hour ), 0 ) AS fill_project_hour,-- 填报项目总工时
                    0 AS fill_common_hour -- 填报公共条目总工时
                FROM
                    work_hour_statistics
                WHERE
                    del_flag = 0
                    AND project_id IS NOT NULL
                    <if test="deptId != null">
                        AND dept_ancestors like concat('%', #{deptId}, '%')
                    </if>
                    <if test="monthNumber != null">
                        AND month_number = #{monthNumber}
                    </if>
                    <if test="yearNumber != null">
                        AND year_number = #{yearNumber}
                    </if>
                    <!-- 数据范围过滤 -->
                    ${params.dataScope}
                GROUP BY
                    user_id,
                    real_name,
                    dept_id,
                    dept_name,
                    dept_sort,
                    dept_parent_id
            ) t
            GROUP BY
                user_id,
                dept_id,
                dept_name,
                dept_sort,
                dept_parent_id
        ) tt
        GROUP BY
            dept_id,
            dept_name,
            dept_sort,
            dept_parent_id
        order by dept_sort

    </select>

    <select id="byDeptDetail" resultType="com.wbyy.pms.modules.workhour.statistics.domain.vo.ByDeptDetailVo">
        SELECT
            project_id,
            project_name,
            project_number,
            project_group_contract_number,
            COALESCE ( sum( finish_hour ), 0 ) AS project_hour -- 填报项目总工时
        FROM
            work_hour_statistics
        WHERE
            del_flag = 0
            AND project_id IS NOT NULL
            <if test="deptId != null">
                AND dept_id = #{deptId}
            </if>
            <if test="yearNumber != null">
                AND year_number = #{yearNumber}
            </if>
            <if test="monthNumber != null">
                AND month_number = #{monthNumber}
            </if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        GROUP BY
            project_id,
            project_name,
            project_number,
            project_group_contract_number
    </select>

    <select id="byUser" resultType="com.wbyy.pms.modules.workhour.statistics.domain.vo.ByUserVo">
        SELECT
            user_id,
            real_name,
            work_number,
            post_name,
            dept_name,
            dept_sort,
            dept_center_name,
            plan_finish_hour as finishHour,
            standard_hour,
            fill_project_hour,
            fill_common_hour,
            fill_project_hour + fill_common_hour AS fill_hour

        FROM
            (
                SELECT
                    user_id,
                    real_name,
                    work_number,
                    post_name,
                    dept_id,
                    dept_name,
                    dept_sort,
                    dept_center_id,
                    dept_center_name,
                    standard_hour,
                    sum( plan_finish_hour ) AS plan_finish_hour,
                    sum( fill_project_hour ) AS fill_project_hour,
                    sum( fill_common_hour ) AS fill_common_hour
                FROM
                    (
                        SELECT
                            user_id,
                            real_name,
                            work_number,
                            post_name,
                            dept_id,
                            dept_name,
                            dept_sort,
                            dept_center_id,
                            dept_center_name,
                            standard_hour,
                            COALESCE ( sum( plan_finish_hour ), 0 ) AS plan_finish_hour,-- 完成任务工时
                            0 AS fill_project_hour,-- 填报项目总工时
                            COALESCE ( sum( finish_hour ), 0 ) AS fill_common_hour -- 填报公共条目总工时

                        FROM
                            work_hour_statistics
                        WHERE
                            del_flag = 0
                          AND project_id IS NULL
                         <if test="deptId != null">
                             AND dept_ancestors like concat('%', #{deptId}, '%')
                         </if>
                         <if test="yearNumber != null">
                             AND year_number = #{yearNumber}
                         </if>
                         <if test="monthNumber != null">
                             AND month_number = #{monthNumber}
                         </if>
                         <if test="realName != null">
                             AND real_name like concat('%', #{realName}, '%')
                         </if>
                         <!-- 数据范围过滤 -->
                         ${params.dataScope}

                        GROUP BY
                            user_id,
                            real_name,
                            work_number,
                            post_name,
                            dept_id,
                            dept_name,
                            dept_sort,
                            dept_center_id,
                            dept_center_name

                        UNION ALL


                        SELECT
                            user_id,
                            real_name,
                            work_number,
                            post_name,
                            dept_id,
                            dept_name,
                            dept_sort,
                            dept_center_id,
                            dept_center_name,
                            standard_hour,
                            COALESCE ( sum( plan_finish_hour ), 0 ) AS plan_finish_hour,-- 完成任务工时
                            COALESCE ( sum( finish_hour ), 0 ) AS fill_project_hour,-- 填报项目总工时
                            0 AS fill_common_hour -- 填报公共条目总工时

                        FROM
                            work_hour_statistics
                        WHERE
                            del_flag = 0
                          AND project_id IS NOT NULL
                          <if test="deptId != null">
                              AND dept_ancestors like concat('%', #{deptId}, '%')
                          </if>
                          <if test="yearNumber != null">
                              AND year_number = #{yearNumber}
                          </if>
                          <if test="monthNumber != null">
                              AND month_number = #{monthNumber}
                          </if>
                          <if test="realName != null">
                              AND real_name like concat('%', #{realName}, '%')
                          </if>
                          <!-- 数据范围过滤 -->
                          ${params.dataScope}

                        GROUP BY
                            user_id,
                            real_name,
                            work_number,
                            post_name,
                            dept_id,
                            dept_name,
                            dept_sort,
                            dept_center_id,
                            dept_center_name
                    ) t
                GROUP BY
                    user_id,
                    real_name,
                    work_number,
                    post_name,
                    dept_id,
                    dept_name,
                    dept_sort,
                    dept_center_id,
                    dept_center_name
            ) tt

        ORDER BY dept_id, dept_sort
    </select>

    <select id="byUserDetail" resultType="com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail">
        select
            t1.fill_date,
            t1.create_name_by,
            t1.work_hour,
            t2.entry_name,
            t2.project_name,
            t1.description
        from
            work_hour_detail t1
        left join work_hour_entry t2 on t1.entry_id = t2.id
        where
            t1.del_flag = 0
            and t2.del_flag = 0
            and t1.`status` in (1,4)
            <if test="yearNumber != null">
                and t1.year_number = #{yearNumber}
            </if>
            <if test="fillDateStart != null">
                and t1.fill_date <![CDATA[ >= ]]> #{fillDateStart}
            </if>
            <if test="fillDateEnd != null">
                and t1.fill_date <![CDATA[ <= ]]> #{fillDateEnd}
            </if>
            <if test="userId != null">
                and t1.create_by = #{userId}
            </if>

        order by t1.fill_date desc
    </select>

    <select id="selectRecordByParams" resultType="com.wbyy.pms.modules.workhour.statistics.domain.vo.FillRecordVO">
        SELECT
        real_name,
        dept_name,
        dept_center_name,
        should_fill_day,
        no_fill_day
        FROM
        work_hour_statistics whs
        WHERE
        whs.del_flag = 0
        <if test="dto.yearNumber != null and dto.yearNumber != ''">
            and whs.year_number = #{dto.yearNumber}
        </if>
        <if test="dto.monthNumber != null">
            AND whs.month_number = #{dto.monthNumber}
        </if>
        <if test="dto.realName != null and dto.realName != ''">
            AND whs.real_name LIKE concat( '%', #{dto.realName}, '%' )
        </if>
        <if test="dto.deptId != null">
            AND whs.dept_ancestors like concat('%', #{dto.deptId}, '%')
        </if>
        <!-- 数据范围过滤 -->
        ${dto.params.dataScope}
        group by user_id
    </select>

    <select id="byProject" resultType="com.wbyy.pms.modules.workhour.statistics.domain.vo.ByProjectVo">
        SELECT project_id,
               project_name,
               project_number,
               project_group_contract_number,
               project_dept_name,
               project_dept_center_name,
               sum(plan_finish_hour)     as finishHour,
               sum(finish_hour)          as fillHour,
               count(distinct (user_id)) as fillCount
        FROM work_hour_statistics whs
        WHERE whs.del_flag = 0
          AND whs.year_number = #{yearNumber}
          AND whs.month_number = #{monthNumber}
            and whs.project_id is not null
        <if test="projectIds != null and projectIds.size() > 0">
            AND whs.project_id in
            <foreach collection="projectIds" open="(" close=")" item="projectId" separator=",">
                #{projectId}
            </foreach>
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        GROUP BY whs.project_id
    </select>



</mapper>
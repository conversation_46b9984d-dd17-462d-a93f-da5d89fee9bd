<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.configuration.basicparameter.projectstage.mapper.ProjectStageMapper">

    <select id="selectMaxSortByBusinessId" resultType="java.lang.Integer">
        select sort
        from project_stage
        where business_type_id = #{businessId}
        and del_flag = 0
        order by sort desc
        limit 1
    </select>
</mapper>
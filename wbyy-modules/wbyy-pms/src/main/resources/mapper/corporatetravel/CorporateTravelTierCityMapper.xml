<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.configuration.basicparameter.corporatetravel.mapper.CorporateTravelTierCityMapper">

    <select id="listByTierId"
            resultType="com.wbyy.pms.modules.configuration.basicparameter.corporatetravel.domain.vo.CorporateTravelTierCityVO">
        select t1.id id,
               t1.tier_id tierId,
               t2.id regionId,
               t1.region_code regionCode,
               t2.name regionName,
               t2.full_name regionFullName,
               t1.sort sort
        from corporate_travel_tier_city t1
        left join region t2 on t1.region_code = t2.code
        where t1.del_flag = 0 and t1.tier_id = #{tierId} and t2.del_flag = 0
        order by t1.sort asc
    </select>
</mapper>
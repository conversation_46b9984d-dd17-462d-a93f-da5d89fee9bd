<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.configuration.basicparameter.typedept.mapper.BusinessTypeDeptMapper">

    <select id="list" resultType="com.wbyy.pms.modules.configuration.basicparameter.typedept.domain.BusinessTypeDept">
        SELECT
            distinct
            ptd.id,
            ptd.dept_id deptId,
            ptd.project_type_id projectTypeId,
            ptd.create_time createTime,
            ptd.create_name_by createNameBy,
            pt.NAME projectTypeName,
            pt.short_name projectTypeShortName,
            pt.DISABLE projectTypeDisable
        <if test="objectInfoIds != null and objectInfoIds.size() > 0">
            ,bto.object_info_id objectInfoId
        </if>
        FROM
            business_type_dept ptd
        JOIN business_type pt ON ptd.project_type_id = pt.id
        <if test="objectInfoIds != null and objectInfoIds.size() > 0">
            join business_type_object bto on  bto.project_type_id = pt.id
        </if>
        where ptd.del_flag = 0
        <if test="objectInfoIds != null and objectInfoIds.size() > 0">
            and bto.del_flag = 0
        </if>
            and pt.del_flag = 0
        <if test="deptId != null">
            and ptd.dept_id=#{deptId}
        </if>
        <if test="projectTypeDisable != null">
            and pt.disable=#{projectTypeDisable}
        </if>
        <if test="objectInfoIds != null and objectInfoIds.size() > 0">
            and bto.object_info_id in
            <foreach collection="objectInfoIds" open="(" close=")" separator="," item="objectInfoId">
                #{objectInfoId}
            </foreach>
        </if>
        order by pt.sort
    </select>

    <select id="listByTypeIds" resultType="com.wbyy.pms.modules.configuration.basicparameter.typedept.domain.BusinessTypeDept">
        select * from business_type_dept where del_flag=0 and project_type_id in
            <foreach collection="typeIds" open="(" close=")" separator="," item="typeId">
                #{typeId}
            </foreach>
    </select>
</mapper>
package com.wbyy.pms.api.project;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.TreeUtil;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.pms.api.BusinessTypeApi;
import com.wbyy.pms.api.domain.ApiBusinessType;
import com.wbyy.pms.modules.configuration.basicparameter.type.service.IBusinessTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/28 18:16
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/business/type")
@InnerAuth
public class BusinessTypeApiImpl implements BusinessTypeApi {

    private final IBusinessTypeService projectTypeService;

    @GetMapping("/simple-tree")
    @Override
    public R<List<ApiBusinessType>> simpleTree(String source) {
        List<ApiBusinessType> list = projectTypeService.listByDisable(0).stream()
                .map(item -> {
                    ApiBusinessType type = new ApiBusinessType();
                    type.setId(item.getId());
                    type.setParentId(item.getParentId());
                    type.setName(item.getName());
                    return type;
                }).toList();
        return R.ok(list);
    }
}

package com.wbyy.pms.modules.log.content.project;

import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.project.base.domain.ProjectBase;

/**
 * @author: 王金都
 * @date: 2025/7/4 9:44
 */
public class AddProjectContentBuilder implements IContentBuilder<ProjectBase> {
    @Override
    public String buildContent(LogRecord logRecord, ProjectBase originalData, ProjectBase newData) {
        String userName = logRecord.getUserName()+"("+logRecord.getWorkNumber()+")";
        return "【"+userName+"】新创建了项目【"+newData.getName()+"】";
    }
}

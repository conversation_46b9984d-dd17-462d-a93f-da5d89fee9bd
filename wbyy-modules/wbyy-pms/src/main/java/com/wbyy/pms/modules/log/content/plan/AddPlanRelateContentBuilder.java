package com.wbyy.pms.modules.log.content.plan;

import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.project.planrelate.domain.ProjectPlanSnapshotRelate;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: 王金都
 * @date: 2025/7/8 9:19
 */
@Slf4j
public class AddPlanRelateContentBuilder implements IContentBuilder<ProjectPlanSnapshotRelate> {
    @Override
    public String buildContent(LogRecord logRecord, ProjectPlanSnapshotRelate originalData, ProjectPlanSnapshotRelate newData) {
        if (null==newData){
            log.warn("添加任务关联生成日志时，新数据为空");
            return "";
        }
        // 日志对象是任务
        logRecord.setDataModule(newData.getProjectPlanName());
        logRecord.setDataModuleId(newData.getProjectPlanId().toString());
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        return userName+"关联任务【"+newData.getProjectPlanRelateName()+"】";
    }
}

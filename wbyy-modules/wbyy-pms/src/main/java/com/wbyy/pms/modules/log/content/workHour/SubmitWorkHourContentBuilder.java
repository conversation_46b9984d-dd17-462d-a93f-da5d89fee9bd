package com.wbyy.pms.modules.log.content.workHour;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wbyy.common.core.utils.SpringUtils;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.log.sender.LogSender;
import com.wbyy.pms.modules.workhour.entry.domain.WorkHourEntry;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/10 15:56
 */
@Slf4j
public class SubmitWorkHourContentBuilder implements IContentBuilder<List<WorkHourEntry>> {
    @Override
    public String buildContent(LogRecord logRecord, List<WorkHourEntry> originalData, List<WorkHourEntry> newData) {
        if (CollectionUtil.isEmpty(newData)){
            log.warn("提交工时时，工时条目为空");
            return "";
        }
        Map<Long, List<WorkHourEntry>> entryGroupByProjectId = newData.stream()
                .filter(item->null!=item.getPlanId()&&null!=item.getProjectId())
                .collect(Collectors.groupingBy(WorkHourEntry::getProjectId));
        if (CollectionUtil.isEmpty(entryGroupByProjectId)){
            log.warn("提交工时时，工时条目为空");
            return "";
        }
        LogSender logSender = SpringUtils.getBean(LogSender.class);
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        WorkHourEntry hourEntry = newData.get(0);
        String yearNumber = hourEntry.getYearNumber();
        Integer weekNumber = hourEntry.getWeekNumber();
        for (Long projectId : entryGroupByProjectId.keySet()) {
            List<WorkHourEntry> findEntries = entryGroupByProjectId.get(projectId);
            if (CollectionUtil.isEmpty(findEntries)) continue;
            WorkHourEntry entry = findEntries.get(0);
            LogRecord record = new LogRecord();
            BeanUtil.copyProperties(logRecord,record);
            record.setBizId(projectId.toString());
            record.setBizCode(entry.getProjectName());
            record.setBizName(entry.getProjectName());
            record.setDataModule(findEntries.stream().map(WorkHourEntry::getEntryName)
                    .collect(Collectors.joining(",")));
            record.setDataModuleId(findEntries.stream().map(WorkHourEntry::getPlanId)
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
            record.setContent(userName+"提交了【"+yearNumber+"年"+weekNumber+"周】工时");
            logSender.send(record);
        }
        return "";
    }
}

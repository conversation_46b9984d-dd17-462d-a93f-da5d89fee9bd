package com.wbyy.pms.modules.log.content.plan;

import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.project.planversioninfo.domain.ProjectPlanVersionInfo;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: 王金都
 * @date: 2025/7/7 14:29
 */
@Slf4j
public class EffectivePlanContentBuilder implements IContentBuilder<ProjectPlanVersionInfo> {
    @Override
    public String buildContent(LogRecord logRecord, ProjectPlanVersionInfo originalData, ProjectPlanVersionInfo newData) {
        if (null==newData){
            log.warn("计划生效生成日志时，新数据为空");
            return "";
        }
        // 日志对象是项目
        logRecord.setDataModule(logRecord.getBizName());
        logRecord.setDataModuleId(logRecord.getBizId());
        Integer version = newData.getVersion();
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        String content = userName+"完成计划编制并生效【V"+version+"】计划";
        if (version>1){
            content+="。变更原因：【"+newData.getDescription()+"】";
        }
        return content;
    }
}

package com.wbyy.pms.common.converter;

import com.wbyy.pms.modules.project.planfront.domain.ProjectPlanFrontRelease;
import com.wbyy.pms.modules.project.planfront.domain.ProjectPlanFrontSnapshot;
import com.wbyy.pms.modules.project.planfront.domain.ProjectPlanFrontVersion;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProjectPlanFrontConverter {
    ProjectPlanFrontConverter INSTANCT = Mappers.getMapper(ProjectPlanFrontConverter.class);

    ProjectPlanFrontRelease snapshot2Release(ProjectPlanFrontSnapshot model);

    List<ProjectPlanFrontRelease> snapshot2Release(List<ProjectPlanFrontSnapshot> model);

    ProjectPlanFrontVersion snapshot2Version(ProjectPlanFrontSnapshot model);

    List<ProjectPlanFrontVersion> snapshot2Version(List<ProjectPlanFrontSnapshot> model);
}

package com.wbyy.pms.modules.log.content.workHour;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.wbyy.common.core.utils.SpringUtils;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.log.sender.LogSender;
import com.wbyy.pms.modules.workhour.approval.domain.WorkHourApproval;
import com.wbyy.pms.modules.workhour.approval.domain.dto.ApprovalDTO;
import com.wbyy.pms.modules.workhour.entry.domain.WorkHourEntry;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/10 17:20
 */
@Slf4j
public class ApprovalWorkHourContentBuilder implements IContentBuilder<ApprovalDTO> {
    @Override
    public String buildContent(LogRecord logRecord, ApprovalDTO originalData, ApprovalDTO newData) {
        if (null==newData){
            log.warn("工时审批时，新数据为空");
            return "";
        }
        List<WorkHourApproval> approvals = newData.getApprovals();
        if (CollectionUtil.isEmpty(approvals)){
            log.warn("工时审批时，审批数据为空");
            return "";
        }
        Integer status = newData.getStatus();
        String remark = newData.getRemark();
        if (3==status){
            logRecord.setOperationType("驳回工时");
        }
        // 同意1 驳回3
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        String statusStr = 1==status?"通过了":"驳回了";
        LogSender logSender = SpringUtils.getBean(LogSender.class);
        if (approvals.size()==1){
            WorkHourApproval approval = approvals.get(0);
            logRecord.setBizName(approval.getProjectName());
            logRecord.setBizCode(approval.getProjectNumber());
            logRecord.setBizId(approval.getProjectId().toString());
            logRecord.setDataModule(approval.getEntryName());
            logRecord.setDataModuleId(approval.getPlanId().toString());
            String fillName = "【" + approval.getRealName() + "(" + approval.getWorkNumber() + ")】";
            String content = userName + statusStr + "1条工时，" + fillName + "【" + DateUtil.formatDate(approval.getFillDate()) + "】【" +
                    approval.getWorkHour().toPlainString() + "】h";
            if (3==status){
                content+="驳回意见：【"+remark+"】";
            }
            logRecord.setContent(content);
            logSender.send(logRecord);
        }else {
            Map<Long, List<WorkHourApproval>> groupByProjectId = approvals.stream()
                    .collect(Collectors.groupingBy(WorkHourApproval::getProjectId));
            for (Long projectId : groupByProjectId.keySet()) {
                List<WorkHourApproval> findApprovals = groupByProjectId.get(projectId);
                if (CollectionUtil.isEmpty(findApprovals)) continue;
                WorkHourApproval entry = findApprovals.get(0);
                LogRecord record = new LogRecord();
                BeanUtil.copyProperties(logRecord,record);
                record.setBizId(projectId.toString());
                record.setBizCode(entry.getProjectName());
                record.setBizName(entry.getProjectName());
                record.setDataModule(findApprovals.stream().map(WorkHourApproval::getEntryName)
                        .collect(Collectors.joining(",")));
                record.setDataModuleId(findApprovals.stream().map(WorkHourApproval::getPlanId)
                        .filter(Objects::nonNull)
                        .map(String::valueOf)
                        .collect(Collectors.joining(",")));
                String fillName = "【" + entry.getRealName() + "(" + entry.getWorkNumber() + ")】";
                String content = findApprovals.size() > 1 ? (userName + statusStr + findApprovals.size() + "条工时") :
                        (userName + statusStr + "1条工时，" + fillName + "【" + DateUtil.formatDate(entry.getFillDate()) + "】【" +
                                entry.getWorkHour().toPlainString() + "】h");
                if (3==status){
                    content+="驳回意见：【"+remark+"】";
                }
                record.setContent(content);
                logSender.send(record);
            }
        }
        return "";
    }
}

package com.wbyy.pms.modules.log.content.plan;

import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.project.planrelate.domain.ProjectPlanSnapshotRelate;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: 王金都
 * @date: 2025/7/8 13:45
 */
@Slf4j
public class CancelPlanRelateContentBuilder implements IContentBuilder<ProjectPlanSnapshotRelate> {
    @Override
    public String buildContent(LogRecord logRecord, ProjectPlanSnapshotRelate originalData, ProjectPlanSnapshotRelate newData) {
        if (null==originalData){
            log.warn("添加任务关联生成日志时，原始数据为空");
            return "";
        }
        // 日志对象是任务
        logRecord.setDataModule(originalData.getProjectPlanName());
        logRecord.setDataModuleId(originalData.getProjectPlanId().toString());
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        return userName+"取消关联任务【"+originalData.getProjectPlanRelateName()+"】";
    }
}

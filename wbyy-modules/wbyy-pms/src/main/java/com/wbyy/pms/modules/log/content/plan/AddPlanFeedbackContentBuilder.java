package com.wbyy.pms.modules.log.content.plan;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.wbyy.log.annotation.LogField;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.FieldChange;
import com.wbyy.log.model.LogRecord;
import com.wbyy.log.util.FieldUtil;
import com.wbyy.pms.modules.project.planfeedback.domain.ProjectPlanFeedback;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/8 20:11
 */
@Slf4j
public class AddPlanFeedbackContentBuilder implements IContentBuilder<ProjectPlanFeedback> {
    @Override
    public String buildContent(LogRecord logRecord, ProjectPlanFeedback originalData, ProjectPlanFeedback newData) {
        if (null==newData){
            log.warn("任务反馈生成日志时，新数据为空");
            return "";
        }
        // 日志对象是任务
        logRecord.setDataModule(newData.getProjectPlanName());
        logRecord.setDataModuleId(newData.getProjectPlanId().toString());
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        if (null==originalData){
            Field[] fields = ReflectUtil.getFields(ProjectPlanFeedback.class);
            StringBuilder content = new StringBuilder();
            for (Field field : fields) {
                if (null==field) continue;
                LogField logField = field.getAnnotation(LogField.class);
                if (null==logField) continue;
                try {
                    Object value = field.get(newData);
                    if (null==value) continue;
                    String fieldName = StringUtils.hasText(logField.value()) ? logField.value() : logField.name();
                    if (StrUtil.isNotBlank(content)){
                        content.append("，");
                    }
                    content.append(fieldName)
                            .append("为【")
                            .append(FieldUtil.getStringValue(value, logField))
                            .append("进度".equals(fieldName)?"%":"")
                            .append("】");
                } catch (IllegalAccessException e) {
                    log.error("[任务.反馈日志] 获取字段值失败", e);
                }
            }
            if (StrUtil.isNotBlank(newData.getFileNames())){
                content.append(StrUtil.isNotBlank(content)?"，":"")
                        .append("文档为：【")
                        .append(newData.getFileNames())
                        .append("】");
            }
            if (StrUtil.isBlank(content)){
                log.warn("第一次反馈，反馈内容为空");
                return "";
            }
            return userName+"反馈任务："+content;
        }
        List<FieldChange> changes = new ArrayList<>();
        FieldUtil.buildChanges(ProjectPlanFeedback.class,originalData, newData, changes);
        if (CollectionUtil.isEmpty(changes)){
            log.warn("任务反馈生成日志时，没有变动信息");
            return "";
        }

        StringBuilder content = new StringBuilder();
        for (FieldChange change : changes) {
            if (StrUtil.isNotBlank(content)){
                content.append("，");
            }
            content.append(change.getFieldLabel())
                    .append("为【")
                    .append(change.getNewValue())
                    .append("进度".equals(change.getFieldLabel())?"%":"")
                    .append("】");
        }
        if (StrUtil.isNotBlank(newData.getFileNames())){
            content.append(StrUtil.isNotBlank(content)?"，":"")
                    .append("文档为：【")
                    .append(newData.getFileNames())
                    .append("】");
        }
        return userName+"反馈任务："+content;
    }
}

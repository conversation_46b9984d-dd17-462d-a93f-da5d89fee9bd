package com.wbyy.pms.common.utils;

import com.wbyy.common.core.utils.SpringUtils;
import com.wbyy.common.redis.service.RedisService;
import com.wbyy.pms.common.constant.ProjectCodeConst;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import static com.wbyy.biz.common.form.common.constant.RedisConstants.TABLE_NAME_SUFFIX_KEY;

/**
 * 项目编码相关 工具类
 *
 * <AUTHOR>
 * @date 2025/8/5 14:13
 */
@Component
public class ProjectCodeUtils implements InitializingBean {


    private static RedisService redisService;
    @Override
    public void afterPropertiesSet() throws Exception {
        redisService= SpringUtils.getBean(RedisService.class);
    }

    /**
     * 採番
     *
     * @param keyPrefix key前缀
     * @return Long
     * <AUTHOR>
     * @date 2025/7/11 15:01
     */
    public static long increment(String keyPrefix) {
        Long cacheMapValue = redisService.getCacheMapValue(ProjectCodeConst.PROJECT_CODE_BASE_KEY, keyPrefix);
        if (cacheMapValue == null) {
            cacheMapValue = 0L;
        }
        cacheMapValue += 1;
        redisService.setCacheMapValue(ProjectCodeConst.PROJECT_CODE_BASE_KEY, keyPrefix, cacheMapValue);
        return cacheMapValue;
    }
}
package com.wbyy.pms.modules.log.content.workHour;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.workhour.entry.domain.WorkHourEntry;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/9 11:46
 */
@Slf4j
public class AddWorkHourEntryContentBuilder implements IContentBuilder<List<WorkHourEntry>> {
    @Override
    public String buildContent(LogRecord logRecord, List<WorkHourEntry> originalData, List<WorkHourEntry> newData) {
        if (CollectionUtil.isEmpty(newData)){
            log.warn("添加工时条目生成日志时，新数据为空");
            return "";
        }
        String entryNames = newData.stream()
                .map(WorkHourEntry::getEntryName)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining(","));
        if (StrUtil.isBlank(entryNames)){
            log.warn("添加工时条目生成日志时，工时条目名称为空");
            return "";
        }
        // 日志对象是任务
        WorkHourEntry entry = newData.get(0);
        logRecord.setDataModule(entry.getEntryName());
        logRecord.setDataModuleId(StrUtil.toStringOrNull(entry.getPlanId()));
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        return userName+"新增【"+entryNames+"】工时条目";
    }
}

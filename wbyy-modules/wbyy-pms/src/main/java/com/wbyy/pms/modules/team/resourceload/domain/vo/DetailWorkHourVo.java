package com.wbyy.pms.modules.team.resourceload.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wbyy.pms.modules.project.plan.domain.vo.StatusVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: 王金都
 * @date: 2025/7/5 19:18
 */
@Data
public class DetailWorkHourVo {

    @Schema(description = "状态,可能为空,虽然填了工时，但是任务被删除的话，就是空的")
    private StatusVO status;

    @Schema(description = "条目名称")
    private String entryName;

    @Schema(description = "任务id")
    private Long planId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目编码")
    private String projectNumber;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "进度,可能为空,虽然填了工时，但是任务被删除的话，就是空的")
    private Integer progress;

    @Schema(description = "计划工时,可能为空,虽然填了工时，但是任务被删除的话，就是空的")
    private BigDecimal planWorkHour;

    @Schema(description = "填报工时")
    private BigDecimal fillWorkHour;
}

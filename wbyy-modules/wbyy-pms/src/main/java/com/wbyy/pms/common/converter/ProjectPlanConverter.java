package com.wbyy.pms.common.converter;

import com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanSnapshot;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanVersion;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProjectPlanConverter {
    ProjectPlanConverter INSTANCT = Mappers.getMapper(ProjectPlanConverter.class);

    ProjectPlanRelease snapshot2Release(ProjectPlanSnapshot model);

    List<ProjectPlanRelease> snapshot2Release(List<ProjectPlanSnapshot> model);

    ProjectPlanVersion snapshot2Version(ProjectPlanSnapshot model);

    List<ProjectPlanVersion> snapshot2Version(List<ProjectPlanSnapshot> model);
}

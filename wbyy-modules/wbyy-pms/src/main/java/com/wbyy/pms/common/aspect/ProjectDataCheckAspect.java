package com.wbyy.pms.common.aspect;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.SpelUtils;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.pms.common.annotaion.ProjectDataCheck;
import com.wbyy.pms.modules.project.base.service.IProjectBaseService;
import com.wbyy.pms.modules.project.teamperson.service.IProjectTeamPersonService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Set;

/**
 * @author: 王金都
 * @date: 2025/5/21 16:27
 */
@Component
@Aspect
@Slf4j
@AllArgsConstructor
public class ProjectDataCheckAspect {
    private final IProjectTeamPersonService projectTeamPersonService;
    private final IProjectBaseService projectBaseService;

    @Around("@annotation(projectDataCheck)")
    public Object around(ProceedingJoinPoint joinPoint, ProjectDataCheck projectDataCheck) throws Throwable {
        String projectIdStr = projectDataCheck.projectId();
        if (StrUtil.isNotBlank(projectIdStr)){
            if (SecurityUtils.isAdmin())  return joinPoint.proceed();
            Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
            Object[] args = joinPoint.getArgs();
            Long projectId = SpelUtils.evaluate(projectIdStr,method,args);
            Long userId = SecurityUtils.getUserId();
            // 是否参与项目
            Boolean inProject = projectTeamPersonService.inProjectCheck(projectId, userId);
            // 是否部门负责人且项目属于负责部门
            Set<Long> manageDepts = SecurityUtils.getUserManageDepts();
            Boolean manageDeptProject = false;
            if (CollectionUtil.isNotEmpty(manageDepts)){
                Long projectDeptId = projectBaseService.getDeptIdById(projectId);
                manageDeptProject = manageDepts.contains(projectDeptId);
            }
            if (!(inProject||manageDeptProject)){
                throw new ServiceException("没有项目数据访问权限");
            }
        }
        return joinPoint.proceed();
    }
}

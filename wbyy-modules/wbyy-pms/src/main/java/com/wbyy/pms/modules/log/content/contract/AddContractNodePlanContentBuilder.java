package com.wbyy.pms.modules.log.content.contract;

import cn.hutool.core.collection.CollectionUtil;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.contract.nodeplan.domain.ContractNodePlan;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: 王金都
 * @date: 2025/7/4 17:18
 */
public class AddContractNodePlanContentBuilder implements IContentBuilder<List<ContractNodePlan>> {
    @Override
    public String buildContent(LogRecord logRecord, List<ContractNodePlan> originalData, List<ContractNodePlan> newData) {
        if (CollectionUtil.isEmpty(newData)) return "";
        ContractNodePlan nodePlan = newData.get(0);
        Long contractNodeId = nodePlan.getContractNodeId();
        String contractNodeName = nodePlan.getContractNodeName();
        logRecord.setDataModule(contractNodeName);
        logRecord.setDataModuleId(contractNodeId.toString());
        String planNames = newData.stream().map(ContractNodePlan::getPlanName).collect(Collectors.joining("、"));
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        return userName+"将合同节点【"+contractNodeName+"】关联了任务【"+planNames+"】";
    }
}

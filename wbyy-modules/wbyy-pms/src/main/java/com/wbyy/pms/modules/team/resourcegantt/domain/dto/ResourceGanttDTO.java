package com.wbyy.pms.modules.team.resourcegantt.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/3  9:41
 * @description 资源甘特图入参
 */
@Data
public class ResourceGanttDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "组织id")
    @NotNull(message = "组织不能为空")
    private Long deptId;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "年份")
    private String yearNumber;
}

package com.wbyy.pms.modules.team.resourceload.service;

import com.wbyy.pms.modules.team.resourceload.domain.dto.DetailDto;
import com.wbyy.pms.modules.team.resourceload.domain.dto.ResourceLoadDTO;
import com.wbyy.pms.modules.team.resourceload.domain.vo.DetailPlanVo;
import com.wbyy.pms.modules.team.resourceload.domain.vo.DetailWorkHourVo;
import com.wbyy.pms.modules.team.resourceload.domain.vo.ResourceLoadVO;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025/7/4  11:40
 * @description 资源负荷服务
 */
public interface IResourceLoadService {

    /**
     * 用户资源符合按任务数统计
     * @param dto
     * @return List<DetailPlanVo>
     * <AUTHOR>
     * @date 2025/7/5 19:26
     */
    List<DetailPlanVo> listDetailPlan(DetailDto dto);

    /**
     * 用户按工时数统计详情
     * @param dto
     * @return List<DetailWorkHourVo>
     * <AUTHOR>
     * @date 2025/7/5 20:20
     */
    List<DetailWorkHourVo> listDetailWorkHour(DetailDto dto);

    /**
     * 资源负荷列表
     * @param dto 入参
     * @return 结果
     */
    List<ResourceLoadVO> selectList(ResourceLoadDTO dto, AtomicInteger total);
}

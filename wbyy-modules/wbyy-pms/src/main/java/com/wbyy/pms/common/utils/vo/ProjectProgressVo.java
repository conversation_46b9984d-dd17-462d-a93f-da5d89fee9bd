package com.wbyy.pms.common.utils.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: 王金都
 * @date: 2025/5/14 17:31
 */
@Data
@Schema(title = "项目进度实体类")
@NoArgsConstructor
@AllArgsConstructor
public class ProjectProgressVo {

    @Schema(description = "实际进度")
    private Integer realityProgress;

    @Schema(description = "计划进度")
    private Integer planProgress;
}

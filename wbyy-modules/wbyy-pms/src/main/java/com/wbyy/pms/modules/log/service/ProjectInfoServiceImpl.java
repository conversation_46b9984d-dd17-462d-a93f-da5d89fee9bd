package com.wbyy.pms.modules.log.service;

import com.wbyy.log.model.BizInfo;
import com.wbyy.log.service.BizInfoService;
import com.wbyy.pms.modules.project.base.domain.ProjectBase;
import com.wbyy.pms.modules.project.base.service.IProjectBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ProjectInfoServiceImpl implements BizInfoService {
    @Autowired
    private IProjectBaseService projectBaseService;

    @Override
    public BizInfo getBizInfo(String bizId) {
        ProjectBase byId = projectBaseService.getById(bizId);
       if (null==byId) return null;
       BizInfo info = new BizInfo();
       info.setBizId(bizId);
       info.setBizName(byId.getName());
       info.setBizCode(byId.getNumber());
       return info;
    }
}

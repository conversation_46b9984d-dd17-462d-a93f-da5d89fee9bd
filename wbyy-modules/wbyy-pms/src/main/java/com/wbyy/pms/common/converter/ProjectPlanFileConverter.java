package com.wbyy.pms.common.converter;

import com.wbyy.pms.modules.project.planfile.domain.ProjectPlanFile;
import com.wbyy.pms.modules.project.planfile.domain.ProjectPlanVersionFile;
import com.wbyy.pms.modules.project.planfile.domain.vo.ProjectPlanFileVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProjectPlanFileConverter {
    ProjectPlanFileConverter INSTANCT = Mappers.getMapper(ProjectPlanFileConverter.class);

    ProjectPlanVersionFile release2Version(ProjectPlanFile model);

    List<ProjectPlanVersionFile> release2Version(List<ProjectPlanFile> model);


    ProjectPlanFileVO release2Vo(ProjectPlanFile model);

    List<ProjectPlanFileVO> release2Vo(List<ProjectPlanFile> model);

}

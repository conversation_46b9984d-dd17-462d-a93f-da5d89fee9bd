package com.wbyy.pms.common.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/5/30  9:13
 * @description 工时状态
 */
@Getter
public enum WorkHourStatusEnum {

    STAGED(0, "暂存"),
    PASS(1, "通过"),
    WAIT_APPROVAL(2, "待审核"),
    REJECTED(3, "驳回"),
    ARCHIVE(4, "归档");

    private final Integer code;
    private final String description;

    WorkHourStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * @Description: 根据Code获取描述
     * @param code
     * @return: String
     * @author: 王金都
     * @Date: 2025/6/17 14:50
     */
    public static String getDescriptionByCode(Integer code){
        return Arrays.stream(WorkHourStatusEnum.values())
                .filter(item->item.getCode().equals(code))
                .map(WorkHourStatusEnum::getDescription)
                .findFirst().orElse("");
    }
}

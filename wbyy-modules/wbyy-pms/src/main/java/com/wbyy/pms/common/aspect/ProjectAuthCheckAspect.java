package com.wbyy.pms.common.aspect;

import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.exception.auth.NotPermissionException;
import com.wbyy.common.core.utils.SpelUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.security.annotation.Logical;
import com.wbyy.common.security.auth.AuthUtil;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.pms.common.annotaion.ProjectAuthCheck;
import com.wbyy.pms.modules.project.baserole.service.IProjectBaseRoleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Set;


/**
 * @author: 王金都
 * @date: 2025/5/20 13:53
 */
@Component
@Aspect
@Slf4j
@AllArgsConstructor
public class ProjectAuthCheckAspect {

    private final IProjectBaseRoleService projectBaseRoleService;

    private static final String ALL_PERMISSION = "*:*:*";

    @Around("@annotation(projectAuthCheck)")
    public Object around(ProceedingJoinPoint joinPoint, ProjectAuthCheck projectAuthCheck) throws Throwable {
        String projectIdStr = projectAuthCheck.projectId();
        if (StrUtil.isNotBlank(projectIdStr)){
            if (SecurityUtils.isAdmin()) return joinPoint.proceed();
            Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
            Object[] args = joinPoint.getArgs();
            // 先校验系统权限
            boolean sysPermCheck = false;
            if (projectAuthCheck.logical().equals(Logical.AND)){
                sysPermCheck = AuthUtil.authLogic.checkPermAndWithBoolean(projectAuthCheck.permCode());
            }else if (projectAuthCheck.logical().equals(Logical.OR)){
                sysPermCheck = AuthUtil.authLogic.checkPermOrWithBoolean(projectAuthCheck.permCode());
            }
            // 在校验项目角色权限
            Long projectId = SpelUtils.evaluate(projectIdStr,method,args);
            Long userId = SecurityUtils.getUserId();
            // 获取用户在项目中的权限字
            Set<String> userProjectPermCodes = projectBaseRoleService.userProjectPermCodes(projectId, userId);
            boolean projectPermCheck = false;
            if (projectAuthCheck.logical().equals(Logical.AND)){
                projectPermCheck = checkPermAndWithBoolean(projectAuthCheck.permCode(),userProjectPermCodes);
            }else if (projectAuthCheck.logical().equals(Logical.OR)){
                projectPermCheck = checkPermOrWithBoolean(projectAuthCheck.permCode(),userProjectPermCodes);
            }
            if (!(sysPermCheck||projectPermCheck)){
                throw new NotPermissionException("没有操作权限");
            }
        }


        return joinPoint.proceed();
    }

    private boolean checkPermAndWithBoolean(String[] toCheckPermCodes,Set<String> userPermCodes)
    {
        for (String permission : toCheckPermCodes)
        {
            boolean match = userPermCodes.stream().filter(StringUtils::hasText)
                    .anyMatch(x -> ALL_PERMISSION.equals(x) || permission.equals(x));
            if (!match) return false;
        }
        return true;
    }

    private boolean checkPermOrWithBoolean(String[] toCheckPermCodes,Set<String> userPermCodes)
    {
        for (String permission : toCheckPermCodes)
        {
            boolean match = userPermCodes.stream().filter(StringUtils::hasText)
                    .anyMatch(x -> ALL_PERMISSION.equals(x) || permission.equals(x));
            if (match) return true;
        }
        return toCheckPermCodes.length <= 0;
    }
}

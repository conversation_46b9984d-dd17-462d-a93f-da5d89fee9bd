package com.wbyy.pms.common.utils.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: 王金都
 * @date: 2025/5/16 10:54
 */
@Data
public class ProjectCycleVo {

    @Schema(description = "计划周期")
    private Integer planCycle;

    @Schema(description = "实际周期")
    private Integer realCycle;

    @Schema(description = "百分比")
    private BigDecimal percent;

    @Schema(description = "起止")
    private String startEnd;

    @Schema(description = "剩余或超出")
    private String residue;
}

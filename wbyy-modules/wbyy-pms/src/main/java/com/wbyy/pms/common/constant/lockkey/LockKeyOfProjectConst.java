package com.wbyy.pms.common.constant.lockkey;

/**
 * 常量类 - 锁相关 key
 *
 * <AUTHOR>
 */
public interface LockKeyOfProjectConst {


    /**
     * 项目角色
     */
    String LOCK_PROJECT_ROLE = "LOCK:PROJECT:ROLE";

    /**
     * 项目模板计划
     */
    String LOCK_PROJECT_TEMPLATE_PLAN = "LOCK:PROJECT:TEMPLATE:PLAN:{}";

    /**
     * 项目计划
     */
    String LOCK_PROJECT_PLAN = "LOCK:PROJECT:PLAN:";
    String LOCK_PROJECT_PLAN_DEL = "LOCK:PROJECT:PLAN:DEL";

    /**
     * 项目类型
     */
    String PROJECT_TYPE_LOCK = "LOCK:PROJECT:TYPE";

    /**
     * 项目基础信息
     */
    String PROJECT_BASE_LOCK = "LOCK:PROJECT:BASE";
}

package com.wbyy.pms.modules.log.content.team;

import cn.hutool.core.collection.CollUtil;
import com.wbyy.common.core.utils.SpringUtils;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.project.baserole.domain.ProjectBaseRole;
import com.wbyy.pms.modules.project.baserole.service.IProjectBaseRoleService;
import com.wbyy.pms.modules.project.teamperson.domain.ProjectTeamPersonPO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/8  15:16
 * @description 添加项目成员日志生成器
 */
public class AddTeamPersonContentBuilder implements IContentBuilder<List<ProjectTeamPersonPO>> {

    /**
     * 构建日志内容
     *
     * @param logRecord    日志记录对象，包含用户信息、操作信息等
     * @param originalData 原始数据（修改前的数据）
     * @param newData      新数据（修改后的数据）
     * @return String 生成的日志内容
     * <AUTHOR>
     * @date 2025/7/8  15:16
     */
    @Override
    public String buildContent(LogRecord logRecord, List<ProjectTeamPersonPO> originalData, List<ProjectTeamPersonPO> newData) {
        if (CollUtil.isEmpty(originalData) && CollUtil.isEmpty(newData)) return "";
        // 日志对象是项目，设置项目名称
        logRecord.setDataModule(logRecord.getBizName());
        // 获取项目角色名称
        Long projectRoleId = CollUtil.isEmpty(originalData) ? newData.get(0).getProjectRoleId() : originalData.get(0).getProjectRoleId();
        IProjectBaseRoleService baseRoleService = SpringUtils.getBean(IProjectBaseRoleService.class);
        ProjectBaseRole baseRole = baseRoleService.getRoleInfoByProjectIdAndRoleDeptId(Long.parseLong(logRecord.getBizId()), projectRoleId);
        // 操作用户信息
        String content = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】将";
        // 拼接日志内容
        if (CollUtil.isEmpty(originalData)) { // 原来成员空，新增
            String newPersonStr = this.getPersonStr(newData);
            content += "【" + newPersonStr + "】添加到项目组，角色是【" + baseRole.getName() + "】";
        } else if (CollUtil.isEmpty(newData)) { // 原来有成员，清空
            content += baseRole.getName() + "项目角色的成员清空";
        } else { // 修改
            String newPersonStr = this.getPersonStr(newData);
            String oldPersonStr = this.getPersonStr(originalData);
            content += baseRole.getName() + "项目角色中" + "【" + oldPersonStr + "】变更为【" + newPersonStr + "】";
        }
        return content;
    }

    /**
     * 获取人员拼接内容
     *
     * @param personList 人员
     * @return 拼接内容
     */
    private String getPersonStr(List<ProjectTeamPersonPO> personList) {
        return personList.stream().map(p -> p.getRealName() + "（" + p.getWorkNumber() + "）").collect(Collectors.joining("，"));
    }
}

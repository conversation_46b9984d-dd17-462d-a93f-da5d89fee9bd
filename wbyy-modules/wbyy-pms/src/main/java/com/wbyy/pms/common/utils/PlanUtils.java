package com.wbyy.pms.common.utils;


import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.utils.DateUtils;
import com.wbyy.pms.common.constant.ProjectPlanConst;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanSnapshot;
import com.wbyy.pms.modules.project.plan.domain.vo.StatusVO;
import com.wbyy.pms.modules.project.plan.enums.StatusColorEnum;
import com.wbyy.pms.modules.project.plan.enums.StatusTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

import static com.wbyy.pms.modules.project.plan.constant.StatusConstant.*;

/**
 * 计划工具类
 */
@Slf4j
public class PlanUtils {


    /**
     * 根据 计划工期、计划开始时间、计划结束时间 的其二 计算得出第三者
     *
     * @param planConstructionPeriod 计划工期
     * @param planStartDate          计划开始
     * @param planEndDate            计划结束
     * @param planSnapshot           数据对象
     */
    public static void calculationPlanConstructionPeriodAndStartAndEnd(Integer planConstructionPeriod,
                                                                 Date planStartDate,
                                                                 Date planEndDate,
                                                                 ProjectPlanSnapshot planSnapshot) {
        /*
         需求：
             自然日，计划结束-计划开始=工期
             修改计划开始时，计划结束自动变化，工期不变
             修改计划结束时，工期变化，计划开始不变
             修改工期时，计划结束变化，计划开始不变
             父任务的计划开始和计划结束，不可编辑。取其下叶子任务的最早计划开始和最晚计划结束
             父任务工期不可编辑，取父任务的计划结束-计划开始
             任务完成时，计划时间不允许修改。
         */
        if (planConstructionPeriod != null) {
            // 修改工期时，计划开始不变，计划结束变化
            if (planSnapshot.getPlanStartDate() == null) return;
            planSnapshot.setPlanEndDate(DateUtils.addDays(planSnapshot.getPlanStartDate(), planConstructionPeriod - 1));
            return;
        }

        if (planStartDate != null) {
            // 修改计划开始时，工期不变，计划结束自动变化
            if (planSnapshot.getPlanConstructionPeriod() == null) return;
            planSnapshot.setPlanEndDate(DateUtils.addDays(planStartDate, planSnapshot.getPlanConstructionPeriod() - 1));
            return;
        }
        if (planEndDate != null) {
            // 修改计划结束时，计划开始不变，工期变化
            if (planSnapshot.getPlanStartDate() == null) return;
            long days = DateUtils.dayDistance(planSnapshot.getPlanStartDate(), planEndDate);
            planSnapshot.setPlanConstructionPeriod(Integer.parseInt(days + ""));
        }
    }

    /**
     * <p>计算任务红路灯</p>
     * <ul>
     *     <li>进行中，未延期，绿圆； -- 距离计划结束还有 ? 天</li>
     *     <li>进行中，已延期，红圆； -- 与计划相比延误 ? 天</li>
     *     <li>已完成，未延期，绿√；  -- 实际完成时间与计划完成时间相比未延误</li>
     *     <li>已完成，已延期，红√；  -- 与计划相比延误 ? 天</li>
     *     <li>临期3天显示为黄灯。    -- 激励计划结束还有 ? 天，小于等于黄灯设置的提前天数 ? 天</li>
     * </ul>
     *
     * @param plan 计划任务
     * @return 结果
     */
    public static StatusVO mackStatus(ProjectPlanRelease plan) {
        Date planStartDate = plan.getPlanStartDate();
        Date planEndDate = plan.getPlanEndDate();
        Date realityStartDate = plan.getRealityStartDate();
        Date realityEndDate = plan.getRealityEndDate();


        StatusVO.StatusVOBuilder builder = StatusVO.builder();

        // 当前系统时间，天
        Date nowDate = DateUtils.getNowDateByDayStart();
        log.debug("项目ID：{}，任务ID：{}，任务名称：{}，计划开始：{}，计划结束：{}，实际开始：{}，实际结束：{}。",
                plan.getProjectId(), plan.getId(), plan.getName(),
                DateUtils.parseToStr(planStartDate), DateUtils.parseToStr(planEndDate),
                DateUtils.parseToStr(realityStartDate), DateUtils.parseToStr(realityEndDate));

        // 系统时间 - 计划结束时间
        long distance = DateUtils.dayDistance(planEndDate, nowDate, false);

        // 完成的就是勾  图标搞定
        if (realityEndDate != null) {
            builder.type(StatusTypeEnum.TICK);
        } else {
            builder.type(StatusTypeEnum.ROUND);
        }

        // 处理颜色
        // 黄色 - 临期并且没完成
        if (distance > -ProjectPlanConst.PLAN_ABOUT_EXPIRE && distance <= 0 && realityEndDate == null) {
            builder.color(StatusColorEnum.YELLOW)
                    .msg(StrUtil.format(WARN_MSG, Math.abs(distance) + 1, ProjectPlanConst.PLAN_ABOUT_EXPIRE));
        }
        // 红色 - 1、完成了 2、还没开始
        if ((realityEndDate != null && realityEndDate.getTime() > planEndDate.getTime())
                || (realityEndDate == null && distance > 0)) {
            builder.color(StatusColorEnum.RED);
            if (realityEndDate != null) {
                long days = DateUtils.dayDistance(planEndDate, realityEndDate, false);
                // 完成了
                builder.msg(StrUtil.format(ERROR_MSG, Math.abs(days)));
            } else {
                // 还没开始
                builder.msg(StrUtil.format(ERROR_MSG, Math.abs(distance)));
            }


        }

        // 绿色已完成
        if (realityEndDate != null) {
            // 必须是计划内完成
            long days = DateUtils.dayDistance(planEndDate, realityEndDate, false);
            if (days <= 0) {
                builder.color(StatusColorEnum.GREEN);
                builder.msg(SUCCESS_MSG);
            }
        }
        // 绿色 - 未完成、进行中
        if (distance <= -ProjectPlanConst.PLAN_ABOUT_EXPIRE) {
            builder.color(StatusColorEnum.GREEN);
            if (realityEndDate != null) {
                builder.msg(SUCCESS_MSG);
            } else {
                builder.msg(StrUtil.format(INFO_MSG, Math.abs(distance - 1)));
            }
        }

        StatusVO build = builder.build();
        log.debug("项目ID：{}，任务ID：{}，任务名称：{}，distance：{}，红绿灯：{}",
                plan.getProjectId(), plan.getId(), plan.getName(), distance, build.getType().getLabel() + build.getColor().getLabel());
        return build;
    }
}

package com.wbyy.pms.modules.team.resourceload.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wbyy.pms.modules.project.plan.domain.vo.StatusVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: 王金都
 * @date: 2025/7/5 19:18
 */
@Data
@Builder
@Accessors
public class DetailPlanVo {

    @Schema(description = "状态")
    private StatusVO status;

    @Schema(description = "任务名称")
    private String planName;

    @Schema(description = "任务id")
    private Long planId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目编码")
    private String projectNumber;

    @Schema(description = "项目id")
    private Long projectId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "计划开始时间")
    private Date planStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "计划结束时间")
    private Date planEndDate;

    @Schema(description = "进度")
    private Integer progress;
}

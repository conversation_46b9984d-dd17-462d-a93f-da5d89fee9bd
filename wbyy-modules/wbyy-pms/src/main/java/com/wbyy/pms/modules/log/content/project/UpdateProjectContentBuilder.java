package com.wbyy.pms.modules.log.content.project;

import cn.hutool.core.util.StrUtil;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.FieldChange;
import com.wbyy.log.model.LogRecord;
import com.wbyy.log.util.FieldUtil;
import com.wbyy.pms.common.enums.ProjectInfoRouterEnum;
import com.wbyy.pms.modules.project.base.domain.ProjectBase;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoAct;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoBenao;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoCrcgdbe;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoDrdc;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/7/4 11:48
 */
public class UpdateProjectContentBuilder implements IContentBuilder<ProjectBase> {
    @Override
    public String buildContent(LogRecord logRecord, ProjectBase originalData, ProjectBase newData) {
        List<FieldChange> changes = new ArrayList<>();
        Class<?> objectClass = logRecord.getObjectClass();
        // 主数据变动
        FieldUtil.buildChanges(objectClass,originalData, newData, changes);
        // TODO 通过自定义表单对象判断

        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        String prefix = userName + "将项目【" + logRecord.getDataModule() + "】的";
        StringBuilder content = new StringBuilder();
        for (FieldChange change : changes) {
            content.append("【").append(change.getFieldLabel()).append("】")
                    .append("由【").append(change.getOldValue()).append("】")
                    .append("调整为【").append(change.getNewValue()).append("】");
        }
        if (StrUtil.isBlank(content)) return "";
        return prefix + content;
    }
}

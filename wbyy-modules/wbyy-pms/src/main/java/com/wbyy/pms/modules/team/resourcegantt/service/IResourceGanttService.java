package com.wbyy.pms.modules.team.resourcegantt.service;

import com.wbyy.pms.modules.team.resourcegantt.domain.dto.ResourceGanttDTO;
import com.wbyy.pms.modules.team.resourcegantt.domain.vo.ResourceGanttVO;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025/7/3  8:58
 * @description 资源甘特图service
 */
public interface IResourceGanttService {

    /**
     * 资源甘特图列表数据
     * @param dto 入参
     * @return 列表数据
     */
    List<ResourceGanttVO> selectResourceGantt(ResourceGanttDTO dto, AtomicInteger total);
}

package com.wbyy.pms.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/30  9:13
 * @description 合同状态
 */
@Getter
public enum ContractStatusEnum {

    IN_PROGRESS(1, "进行中"),
    SUSPENDED(2, "已暂停"),
    TERMINATED(3, "已终止"),
    ENDED(0, "已结束");

    private final Integer code;
    private final String description;

    ContractStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}

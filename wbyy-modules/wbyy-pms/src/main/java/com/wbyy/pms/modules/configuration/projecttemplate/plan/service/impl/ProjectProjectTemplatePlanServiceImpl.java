package com.wbyy.pms.modules.configuration.projecttemplate.plan.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.generator.SnowflakeGenerator;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wbyy.common.core.constant.HttpMagConstants;
import com.wbyy.common.core.constant.SqlConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.IdToNameUtils;
import com.wbyy.common.core.utils.SpringUtils;
import com.wbyy.common.redis.annotation.RedisLock;
import com.wbyy.biz.common.drag.service.IBaseVxeHierarchyService;
import com.wbyy.biz.common.drag.service.impl.BaseVxeHierarchyServiceImpl;
import com.wbyy.pms.common.utils.CircularDependencyUtils;
import com.wbyy.pms.modules.configuration.basicparameter.role.service.IProjectRoleService;
import com.wbyy.pms.modules.configuration.projecttemplate.main.domain.dto.ProjectTemplateCopyDTO;
import com.wbyy.pms.modules.configuration.projecttemplate.plan.domain.ProjectTemplatePlan;
import com.wbyy.pms.modules.configuration.projecttemplate.plan.domain.TemplatePlanFront;
import com.wbyy.pms.modules.configuration.projecttemplate.plan.mapper.ProjectTemplatePlanMapper;
import com.wbyy.pms.modules.configuration.projecttemplate.plan.service.IProjectTemplatePlanService;
import com.wbyy.pms.modules.configuration.projecttemplate.plan.service.ITemplatePlanFrontService;
import com.wbyy.pms.modules.configuration.projecttemplate.plan.utils.WbsCleanerUtils;
import com.wbyy.pms.modules.configuration.projecttemplate.planclear.domain.TemplatePlanClear;
import com.wbyy.pms.modules.configuration.projecttemplate.planclear.service.ITemplatePlanClearService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.wbyy.common.core.constant.HttpMagConstants.HAS_CHILD_PLAN;
import static com.wbyy.pms.common.constant.lockkey.LockKeyOfProjectConst.LOCK_PROJECT_TEMPLATE_PLAN;

/**
 * 项目模板计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectProjectTemplatePlanServiceImpl extends BaseVxeHierarchyServiceImpl<ProjectTemplatePlanMapper, ProjectTemplatePlan> implements IProjectTemplatePlanService {
    private final ITemplatePlanFrontService templatePlanFrontService;
    private final RedissonClient redissonClient;
    private final ITemplatePlanClearService templatePlanClearService;
    private final IProjectRoleService projectRoleService;


    @Override
    public IBaseVxeHierarchyService<ProjectTemplatePlan> getProxy() {
        return SpringUtils.getBean(ProjectProjectTemplatePlanServiceImpl.class);
    }

    @Override
    public LambdaQueryWrapper<ProjectTemplatePlan> getWrapper(ProjectTemplatePlan entity) {
        return Wrappers.lambdaQuery(ProjectTemplatePlan.class)
                .eq(ProjectTemplatePlan::getProjectTemplateId, entity.getProjectTemplateId());
    }

    /**
     * 新增计划 before
     *
     * @param po 新增的对象
     */
    @Override
    protected void insertBefore(ProjectTemplatePlan po) {
        super.insertBefore(po);
        log.debug("ProjectProjectTemplatePlanServiceImpl insert before...");

        // 判断当前模板计划 不能超过 300 条
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = getWrapper(po);
        long count = this.count(wrapper);
        if (count >= 300) {
            throw new ServiceException("当前模板计划不能超过 300 条");
        }
        // 1、设置 WMS(1.1 1.2 2 2.3)  并处理相关的模板计划中的Wbs字段
//        calculateWbsAndSort(projectTemplatePlan);
    }

    /**
     * 新增计划 after
     *
     * @param projectTemplatePlan 新增的对象
     * @param modifiedData        涉及到修改的数据
     * @param updateIds           修改的ID
     */
    @Override
    protected void insertAfter(ProjectTemplatePlan projectTemplatePlan, List<ProjectTemplatePlan> modifiedData, Set<Long> updateIds) {
        super.insertAfter(projectTemplatePlan, modifiedData, updateIds);
        log.debug("ProjectProjectTemplatePlanServiceImpl insert after...");

        // 2、处理 sort 和 WBS
        calculateSortAndWbs(projectTemplatePlan);
    }

    @Override
    protected void upOrDownGradeBefore(ProjectTemplatePlan entity) {
        super.upOrDownGradeBefore(entity);
        log.debug("ProjectProjectTemplatePlanServiceImpl upOrDownGrade before...");
    }

    /**
     * 升级降级后置处理
     *
     * @param up
     * @param entity        升级降级的对象（最新数据对象）
     * @param upBrotherNode 升级降级前的 上一个兄弟节点
     * @param updateIds
     */
    @Override
    protected void upOrDownGradeAfter(Boolean up, ProjectTemplatePlan entity, ProjectTemplatePlan upBrotherNode, Set<Long> updateIds) {
        super.upOrDownGradeAfter(up, entity, upBrotherNode, updateIds);
        log.debug("ProjectProjectTemplatePlanServiceImpl upOrDownGrade after...");
        // WBS 字段处理
        // 1、升级，需要处理升级后，对应的同父级下面的（parentId 相等 sort >= 当前节点.sort）
        //                        所有节点和其子节点
        // 2、降级，需要处理降级前，对应的同父级下面的（parentId 相等 sort >= 当前节点.sort）
        //                        所有节点和其子节点
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.<ProjectTemplatePlan>lambdaQuery();
        wrapper.eq(ProjectTemplatePlan::getProjectTemplateId, entity.getProjectTemplateId())
                .orderByAsc(ProjectTemplatePlan::getSort);
        List<ProjectTemplatePlan> nodes = this.list(wrapper);

        if (CollUtil.isEmpty(nodes)) {
            return;
        }
        // 清洗重构 WBS
        WbsCleanerUtils.cleanWbs(nodes);
        this.updateBatchById(nodes);
    }

    @Override
    protected void dragBefore(ProjectTemplatePlan entity) {
        super.dragBefore(entity);
        log.debug("ProjectProjectTemplatePlanServiceImpl drag before...");
    }

    @Override
    @Transactional
    protected void dragAfter(List<ProjectTemplatePlan> list, Set<Long> updateIds) {
        super.dragAfter(list, updateIds);
        log.debug("ProjectProjectTemplatePlanServiceImpl drag after...");
        // 重排wbs
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectTemplatePlan::getProjectTemplateId, list.get(0).getProjectTemplateId());
        List<ProjectTemplatePlan> plans = this.list(wrapper);

        WbsCleanerUtils.cleanWbs(plans);

        this.updateBatchById(plans);

        // 1、处理前置任务

        // 1.1、查询所有的前置任务
        List<Long> ids = plans.stream().map(ProjectTemplatePlan::getId).toList();
        List<TemplatePlanFront> planFronts = templatePlanFrontService.findByProjectTemplatePlanIds(ids);
        if (CollUtil.isEmpty(planFronts)) {
            return;
        }
        // 1.2、修改所有的前置任务
        Map<Long, Integer> projectTemplatePlanSortMap = plans.stream().collect(Collectors.toMap(ProjectTemplatePlan::getId, ProjectTemplatePlan::getSort));
        planFronts.forEach(planFront ->
                planFront.setFrontPlanSort(
                        projectTemplatePlanSortMap.get(planFront.getFrontPlanId())
                )
        );
        // 更新前置任务
        templatePlanFrontService.updateBatchById(planFronts);
    }

    @Override
    protected List<ProjectTemplatePlan> getListBySortInterval(Integer minSort, Integer maxSort,
                                                              ProjectTemplatePlan entity) {
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectTemplatePlan::getProjectTemplateId, entity.getProjectTemplateId())
                .ge(ProjectTemplatePlan::getSort, minSort)
                .le(ProjectTemplatePlan::getSort, maxSort)
                .orderByAsc(ProjectTemplatePlan::getSort);
        return this.list(wrapper);
    }

    /**
     * 查询项目模板计划
     *
     * @param id 项目模板计划主键
     * @return 项目模板计划
     */
    @Override
    public ProjectTemplatePlan selectById(Long id) {
        ProjectTemplatePlan projectTemplatePlan = this.getById(id);
        if (projectTemplatePlan != null) {
            // 查询后置任务
            List<TemplatePlanFront> templatePlanFronts =
                    templatePlanFrontService.findByProjectTemplatePlanId(id);
            projectTemplatePlan.setFrontTasks(templatePlanFronts);
            if (CollUtil.isNotEmpty(templatePlanFronts)) {
                // 构造前置任务，给前端回显
                List<Integer> frontTaskSorts = templatePlanFronts.stream().map(TemplatePlanFront::getFrontPlanSort).toList();
                projectTemplatePlan.setFrontTaskSorts(StrUtil.join(",", frontTaskSorts));
            }
        }
        return projectTemplatePlan;
    }

    /**
     * 查询项目模板计划列表
     *
     * @param projectTemplatePlan 项目模板计划
     * @return 项目模板计划
     */
    @Override
    public List<ProjectTemplatePlan> selectList(ProjectTemplatePlan projectTemplatePlan) {
        Optional.ofNullable(projectTemplatePlan.getProjectTemplateId())
                .orElseThrow(() -> new ServiceException(HttpMagConstants.PARAMS_NOT_NULL));

        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectTemplatePlan::getProjectTemplateId, projectTemplatePlan.getProjectTemplateId());
        wrapper.orderByAsc(ProjectTemplatePlan::getSort);
        List<ProjectTemplatePlan> list = this.list(wrapper);

        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 翻译角色
        List<List<String>> dutyRules = list.stream().map(ProjectTemplatePlan::getDutyRule).toList();
        List<String> dutyRulesSet = new ArrayList<>();
        for (List<String> dutyRule : dutyRules) {
            if (CollUtil.isNotEmpty(dutyRule)) {
                dutyRulesSet.addAll(dutyRule);
            }
        }

        List<Long> ids = list.stream().map(ProjectTemplatePlan::getId).toList();
        Map<Long, Integer> idSortMap = list.stream().collect(Collectors.toMap(ProjectTemplatePlan::getId, ProjectTemplatePlan::getSort));
        // 前置任务
        Map<Long, String> projectPlanFrontMap = templatePlanFrontService.findMapByProjectTemplatePlanIds(ids, idSortMap);
        // 项目角色
        List<Long> dutyRulesList = JSONArray.parseArray(JSON.toJSONString(dutyRulesSet), Long.class);
        Map<Long, String> dutyRuleMap = projectRoleService.findMapByIds(dutyRulesList);

        list.forEach(plan ->
                plan.setDutyRuleName(IdToNameUtils.idsToNames(dutyRuleMap, JSON.toJSONString(plan.getDutyRule())))
                        .setFrontTaskSorts(projectPlanFrontMap.get(plan.getId()))
        );


        return list;
    }


    /**
     * 新增项目模板计划
     *
     * @param projectTemplatePlan 项目模板计划
     * @return 结果
     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public ProjectTemplatePlan insert(ProjectTemplatePlan projectTemplatePlan) {
//
//        RLock lock = redissonClient.getLock(StrUtil.format(LOCK_PROJECT_TEMPLATE_PLAN, projectTemplatePlan.getProjectTemplateId()));
//        try {
//            if (lock.tryLock(1, 5, TimeUnit.SECONDS)) {
//
//                boolean isOk = this.save(projectTemplatePlan);
//                if (!isOk) {
//                    throw new ServiceException("保存【项目模板计划对象】失败，请稍后重试");
//                }
//
//                // 2、处理 sort 和 WBS
//                calculateSortAndWbs(projectTemplatePlan);
//                return projectTemplatePlan;
//            }
//        } catch (InterruptedException e) {
//            log.error("【模板计划】 - 新增失败。projectTemplateId:{}", projectTemplatePlan.getProjectTemplateId(), e);
//            throw new ServiceException(HttpMagConstants.NO_KEY_RESOURCES);
//        } finally {
//            if (lock.isHeldByCurrentThread()) {
//                lock.unlock();
//            }
//        }
//        return null;
//    }

    /**
     * 修改项目模板计划
     *
     * @param projectTemplatePlan 项目模板计划
     * @return 结果
     */
    @Override
    @RedisLock(prefix = LOCK_PROJECT_TEMPLATE_PLAN, key = "#projectTemplatePlan.projectTemplateId")
    @Transactional(rollbackFor = Exception.class)
    public ProjectTemplatePlan update(ProjectTemplatePlan projectTemplatePlan) {

        // 工期检查 , 暂不实现
//        if (projectTemplatePlan.getConstructionPeriod() != null) {
//            // 检查
//            boolean has = hasChildren(projectTemplatePlan.getId());
//            if (has) {
//                throw new ServiceException(HttpMagConstants.NOT_UPDATE);
//            }
//        }

        ProjectTemplatePlan entityByDB = this.getById(projectTemplatePlan.getId());

        String frontTaskSorts = projectTemplatePlan.getFrontTaskSorts();
        if (frontTaskSorts != null) {
            // 删除之前的前置任务
            templatePlanFrontService.deleteByProjectTemplatePlanId(projectTemplatePlan.getId());
            if (StrUtil.isNotEmpty(frontTaskSorts)) {
                // 检查前置任务是否存在
                String[] split = frontTaskSorts.split("，|,");
                List<Integer> frontTaskSortList = new ArrayList<>();
                Arrays.stream(split).forEach(s -> {
                    frontTaskSortList.add(Integer.parseInt(s.trim()));
                });
                List<ProjectTemplatePlan> frontTaskTemplatePlans =
                        checkFrontTaskIsExist(entityByDB, frontTaskSortList);

                // 新增前置任务
                templatePlanFrontService.insertByProjectTemplatePlanIdFrontTask(projectTemplatePlan.getProjectTemplateId(), projectTemplatePlan.getId(), frontTaskTemplatePlans);
            }
        }

        this.updateById(projectTemplatePlan);
        // 处理工期检查，计算所有父级工期 暂不实现
        // 处理逻辑，取子节点的最大工期
//                if (projectTemplatePlan.getConstructionPeriod() != null) {
//                    List<ProjectTemplatePlan> allParentNode = baseMapper.selectAllParentNodeById(projectTemplatePlan.getProjectTemplateId(), projectTemplatePlan.getId());
//                    for (int i = allParentNode.size() - 1; i >= 0; i--) {
//                        ProjectTemplatePlan plan = allParentNode.get(i);
//                        // 查询字节点中 最大的 工期
//                        BigDecimal max = findChildrenMaxConstructionPeriod(plan.getId());
//                        if (max != null) {
//                            plan.setConstructionPeriod(max);
//                            this.updateById(plan);
//                        }
//                    }
//                }
        return this.selectById(projectTemplatePlan.getId());
    }

    /**
     * 查询字节点中 最大的 工期
     *
     * @param id
     * @return
     */
    private Integer findChildrenMaxConstructionPeriod(Long id) {
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.select(ProjectTemplatePlan::getConstructionPeriod)
                .eq(ProjectTemplatePlan::getParentId, id)
                .orderByDesc(ProjectTemplatePlan::getConstructionPeriod)
                .last(SqlConstants.LIMIT_1);

        ProjectTemplatePlan one = this.getOne(wrapper);
        if (one == null) {
            return null;
        }
        return one.getConstructionPeriod();
    }

    /**
     * 检查前置任务是否存在
     *
     * @param entityByDB     当前需要修改的数据，来自数据库
     * @param frontTaskSorts
     * @return 返回前置任务
     */
    private List<ProjectTemplatePlan> checkFrontTaskIsExist(ProjectTemplatePlan entityByDB, List<Integer> frontTaskSorts) {
        // 查询 当前模板中是否 有当前计划
        List<ProjectTemplatePlan> templatePlans = findByProjectTemplateIdAndSorts(entityByDB, frontTaskSorts);

        if (CollUtil.isEmpty(templatePlans)) {
            throw new ServiceException("您选择的前置任务不存在，您可以输入如“1,2”、“1”之类的格式，请检查！");
        }

        if (frontTaskSorts.size() != templatePlans.size()) {
            // 前端选择的前置任务
            List<Integer> selectFrontTaskSorts = templatePlans.stream().map(ProjectTemplatePlan::getSort).toList();
            StringBuffer sb = new StringBuffer();
            selectFrontTaskSorts.forEach(item -> {
                if (!frontTaskSorts.contains(item)) {
                    sb.append(item).append(",");
                }
            });
            if (StrUtil.isEmpty(sb)) {
                throw new ServiceException("您设置的前置任务有重复，请检查！");
            }
            sb.deleteCharAt(sb.length() - 1);
            throw new ServiceException(StrUtil.format("您选择【{}】前置任务不存在", sb));
        }

        if (frontTaskSorts.contains(entityByDB.getSort())) {
            throw new ServiceException(StrUtil.format("您选择【{}】前置任务有误，不能选择当前任务作为前置任务", entityByDB.getSort()));
        }
        // 查询当前节点的所有父节点，所有的父级都不能设置为自己的前置任务
        List<ProjectTemplatePlan> parentTemplatePlans = baseMapper.selectAllParentNodeById(entityByDB.getProjectTemplateId(), entityByDB.getId());
        for (ProjectTemplatePlan projectTemplatePlan : parentTemplatePlans) {
            if (frontTaskSorts.contains(projectTemplatePlan.getSort())) {
                throw new ServiceException(StrUtil.format("您选择【{}】前置任务有误，不能选择当前任务的父级任务作为前置任务", projectTemplatePlan.getSort()));
            }
        }

        // 查询当前节点子节点，不能设置子节点为自己的前置任务
        List<ProjectTemplatePlan> childrenTemplatePlans = baseMapper.selectAllChildrenNodeById(entityByDB.getProjectTemplateId(), entityByDB.getId());
        for (ProjectTemplatePlan projectTemplatePlan : childrenTemplatePlans) {
            if (frontTaskSorts.contains(projectTemplatePlan.getSort())) {
                throw new ServiceException(StrUtil.format("您选择【{}】前置任务有误，不能选择当前任务的子任务作为前置任务", projectTemplatePlan.getSort()));
            }
        }


        // 判断是否是修改前置任务
        // 做相关检查，循环依赖、父任务不能设为前置任务、不能把自己设置为前置任务
        // 数据库查到的前置任务，加上本次修改时添加的前置任务

        // 查询当前模板的所有前置
        List<TemplatePlanFront> templatePlanFronts =
                templatePlanFrontService.findByProjectTemplateId(entityByDB.getProjectTemplateId());
        templatePlans.forEach(templatePlan -> {
            templatePlanFronts.add(TemplatePlanFront.builder()
                    .projectTemplatePlanId(entityByDB.getId())
                    .frontPlanId(templatePlan.getId())
                    .frontPlanSort(templatePlan.getSort()).build());
        });

        // 查询所有计划
        List<ProjectTemplatePlan> plans = baseMapper.selectList(Wrappers.<ProjectTemplatePlan>lambdaQuery()
                .eq(ProjectTemplatePlan::getProjectTemplateId, entityByDB.getProjectTemplateId()));
        log.debug("所有任务: {}", JSON.toJSONString(plans));
        log.debug("所有前置任务: {}", JSON.toJSONString(templatePlanFronts));
        log.debug("idKeyName: {}, pidKeyName:{}", "projectTemplatePlanId", "frontPlanId");
        boolean ok = CircularDependencyUtils.hasCircularDependency(JSON.parseArray(JSON.toJSONString(plans)),
                JSON.parseArray(JSON.toJSONString(templatePlanFronts)),
                "projectTemplatePlanId", "frontPlanId");
        if (ok) {
            throw new ServiceException("当前模板计划前置任务，存在循环依赖问题，请检查模板计划");
        }

        return templatePlans;
    }

    /**
     * 查询 当前模板中是否 有当前计划
     *
     * @param entityByDB     当前需要修改的数据，来自数据库
     * @param frontTaskSorts 前置任务的自增IDs
     * @return
     */
    private List<ProjectTemplatePlan> findByProjectTemplateIdAndSorts(ProjectTemplatePlan entityByDB,
                                                                      List<Integer> frontTaskSorts) {
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectTemplatePlan::getProjectTemplateId, entityByDB.getProjectTemplateId())
                .in(ProjectTemplatePlan::getSort, frontTaskSorts)
                .orderByAsc(ProjectTemplatePlan::getSort);
        return this.list(wrapper);
    }

    /**
     * 批量删除项目模板计划
     *
     * @param ids 需要删除的项目模板计划主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        List<Long> idList = Arrays.asList(ids);

        // 前置检查、检查是否含有子任务
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.select(ProjectTemplatePlan::getId, ProjectTemplatePlan::getSort)
                .in(ProjectTemplatePlan::getParentId, idList);
        List<ProjectTemplatePlan> list = this.list(wrapper);
        if (CollUtil.isNotEmpty(list)) {
            String sorts = CollUtil.join(list.stream().map(ProjectTemplatePlan::getSort).toList(), "、");
            throw new ServiceException(StrUtil.format(HAS_CHILD_PLAN, sorts));
        }
        // 检查当前删除的计划 是否为别计划的前置任务
        List<TemplatePlanFront> planFronts = templatePlanFrontService.findByFrontPlanIds(idList);
        if (CollUtil.isNotEmpty(planFronts)) {
            List<Long> projectTemplatePlanIds = planFronts.stream().map(TemplatePlanFront::getProjectTemplatePlanId).toList();
            wrapper.clear();
            wrapper.select(ProjectTemplatePlan::getId, ProjectTemplatePlan::getSort)
                    .in(ProjectTemplatePlan::getId, projectTemplatePlanIds)
                    .orderByAsc(ProjectTemplatePlan::getSort);
            List<Integer> sorts = this.list(wrapper).stream().map(ProjectTemplatePlan::getSort).toList();
            throw new ServiceException(StrUtil.format(HttpMagConstants.NOT_DEL_PLAN, CollUtil.join(sorts, "、")));
        }

        wrapper.clear();
        wrapper.in(ProjectTemplatePlan::getId, idList)
                .orderByAsc(ProjectTemplatePlan::getSort);
        // 查询其中 序号最小的一条记录
        ProjectTemplatePlan one = this.getOne(wrapper, false);

        Long projectTemplateId = one.getProjectTemplateId();
        wrapper.clear();

        RLock lock = redissonClient.getLock(StrUtil.format(LOCK_PROJECT_TEMPLATE_PLAN, projectTemplateId));
        try {
            if (lock.tryLock(1, 5, TimeUnit.SECONDS)) {

                // 删除前置任务
                templatePlanFrontService.deleteByProjectTemplatePlanIds(Arrays.asList(ids));

                this.removeBatchByIds(Arrays.asList(ids));

                wrapper.eq(ProjectTemplatePlan::getProjectTemplateId, projectTemplateId)
                        .orderByAsc(ProjectTemplatePlan::getSort);
                List<ProjectTemplatePlan> newList = this.list(wrapper);
                // 序号 和 WBS 重排
                WbsCleanerUtils.cleanWbs(newList);
                for (int i = 0; i < newList.size(); i++) {
                    newList.get(i).setSort(i + 1);
                }

                return this.updateBatchById(newList);

                // TODO  工时字段

            }
        } catch (InterruptedException e) {
            log.error("【模板计划】 - 删除失败。projectTemplateId:{}, ids:{}", projectTemplateId, JSON.toJSONString(ids), e);
            throw new ServiceException(HttpMagConstants.NO_KEY_RESOURCES);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return false;
    }


    @Override
    @Transactional
    public Boolean copy(ProjectTemplateCopyDTO dto) {
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectTemplatePlan::getProjectTemplateId, dto.getSourceId());
        List<ProjectTemplatePlan> list = this.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException(HttpMagConstants.TEMPLATE_PLAN_IS_NULL);
        }
        List<Long> ids = list.stream().map(ProjectTemplatePlan::getId).toList();
        SnowflakeGenerator snowflakeGenerator = new SnowflakeGenerator();

        List<TemplatePlanFront> templatePlanFronts = templatePlanFrontService.findByProjectTemplatePlanIds(ids);
        Map<Long, Long> oldAndNewIdMap = new HashMap<>(ids.size());
        // 处理所有ID
        list.forEach(item -> {
            Long oldId = item.getId();
            // 设置新的ID
            item.setProjectTemplateId(dto.getTargetId())
                    .setDelFlag(null)
                    .setId(snowflakeGenerator.next())
                    .setCreateNameBy(null)
                    .setUpdateNameBy(null)
                    .setCreateBy(null)
                    .setCreateTime(null)
                    .setUpdateTime(null)
                    .setUpdateBy(null);
            oldAndNewIdMap.put(oldId, item.getId());
        });
        // 处理所有parenId
        list.forEach(item -> {
            item.setParentId(oldAndNewIdMap.get(item.getParentId()) == null ? 0L : oldAndNewIdMap.get(item.getParentId()));
        });

        // 处理所有的前置任务
        templatePlanFronts.forEach(item -> {
            item.setProjectTemplatePlanId(oldAndNewIdMap.get(item.getProjectTemplatePlanId()))
                    .setFrontPlanId(oldAndNewIdMap.get(item.getFrontPlanId()))
                    .setId(null)
                    .setCreateNameBy(null)
                    .setUpdateNameBy(null)
                    .setCreateBy(null)
                    .setCreateTime(null)
                    .setUpdateTime(null)
                    .setUpdateBy(null);
        });

        // 新增计划、新增前置任务
        this.saveBatch(list);
        return templatePlanFrontService.saveBatch(templatePlanFronts);
    }

    @Nullable
    @Override
    protected ProjectTemplatePlan upOrDownCheckData(Boolean up, ProjectTemplatePlan presentEntity) {
        // 第一级节点不允许升级
        if (presentEntity.getParentId() == 0L && up) {
            throw new ServiceException(HttpMagConstants.NOT_UP);
        }

        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectTemplatePlan::getParentId, presentEntity.getParentId())
                .lt(ProjectTemplatePlan::getSort, presentEntity.getSort())
                .eq(ProjectTemplatePlan::getProjectTemplateId, presentEntity.getProjectTemplateId())
                .orderByDesc(ProjectTemplatePlan::getSort);
        ProjectTemplatePlan upBrotherNode = this.getOne(wrapper, false);

        // 叶子节点，并且前面没有兄弟节点 不允许降级
        if (upBrotherNode == null && !up) {
            throw new ServiceException(HttpMagConstants.NOT_DOWN);
        }
        return upBrotherNode;
    }

    @Override
    public Boolean exist(Long projectTemplateId) {
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectTemplatePlan::getProjectTemplateId, projectTemplateId);
        return this.count(wrapper) > 0;
    }

    @Override
    public Boolean exist(Collection<Long> projectTemplateIds) {
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.in(ProjectTemplatePlan::getProjectTemplateId, projectTemplateIds);
        return this.count(wrapper) > 0;
    }

    @Override
    @Transactional
    public boolean drag(ProjectTemplatePlan entity, Set<Long> updateIds) {

        // 拖拽的参照对象
        Long targetObjectId = entity.getTargetObjectId();
        // 拖拽后插入的位置：向上 or 向下
        Boolean insertUp = entity.getInsertUp();

        // 需要拖拽的原始对象
        ProjectTemplatePlan presentEntity = this.getById(entity.getId());

        // 前置处理
        dragBefore(presentEntity);

        // 拖拽后位置的参考对象
        ProjectTemplatePlan targetObject = this.getById(targetObjectId);

        // 1 数据校验
        dragCheckData(presentEntity, targetObject, insertUp);

        // 查询所有需要修改顺序的数据
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectTemplatePlan::getProjectTemplateId, targetObject.getProjectTemplateId());

        // 需要调整顺序的最大的 sort
        Integer maxSort = null;
        if (targetObject.getSort() < presentEntity.getSort()) {
            // 向上拖拽
            // 查询最大的序号
            maxSort = getMaxSort(presentEntity);
            wrapper.ge(ProjectTemplatePlan::getSort, targetObject.getSort())
                    .le(ProjectTemplatePlan::getSort, maxSort);
        } else {
            // 向下拖拽
            // 查询最大的序号
            maxSort = getMaxSort(targetObject);
            wrapper.le(ProjectTemplatePlan::getSort, maxSort)
                    .ge(ProjectTemplatePlan::getSort, presentEntity.getSort());
        }
        wrapper.orderByAsc(ProjectTemplatePlan::getSort);
        List<ProjectTemplatePlan> list = this.list(wrapper);


        List<ProjectTemplatePlan> updateList = new ArrayList<>();
        // 向上拖拽
        if (targetObject.getSort() < presentEntity.getSort()) {
            // list 分三个集合
            Integer targetObjMaxSort = this.getMaxSort(targetObject);
            List<ProjectTemplatePlan> firstList = list.stream()
                    .filter(item -> item.getSort() <= targetObjMaxSort).toList();
            List<ProjectTemplatePlan> middleList = list.stream()
                    .filter(item -> item.getSort() > targetObjMaxSort && item.getSort() < presentEntity.getSort()).toList();
            List<ProjectTemplatePlan> lastList = list.stream()
                    .filter(item -> item.getSort() >= presentEntity.getSort()).toList();
            if (insertUp) {
                // 向上插入
                updateList.addAll(lastList);
                updateList.addAll(firstList);
                updateList.addAll(middleList);
            } else {
                // 向下插入
                updateList.addAll(firstList);
                updateList.addAll(lastList);
                updateList.addAll(middleList);
            }
        } else {
            // 向下拖拽
            // list 分三个集合
            Integer presentEntityMaxSort = this.getMaxSort(presentEntity);
            List<ProjectTemplatePlan> firstList = list.stream().filter(item -> item.getSort() <= presentEntityMaxSort).toList();
            List<ProjectTemplatePlan> middleList = list.stream().filter(item -> item.getSort() > presentEntityMaxSort && item.getSort() < targetObject.getSort()).toList();
            List<ProjectTemplatePlan> lastList = list.stream().filter(item -> item.getSort() >= targetObject.getSort()).toList();

            if (insertUp) {
                // 向上插入
                updateList.addAll(middleList);
                updateList.addAll(firstList);
                updateList.addAll(lastList);
            } else {
                // 向下插入
                updateList.addAll(middleList);
                updateList.addAll(lastList);
                updateList.addAll(firstList);
            }
        }

        Integer minSort = updateList.stream().min(Comparator.comparingInt(ProjectTemplatePlan::getSort)).get().getSort();
        for (ProjectTemplatePlan t : updateList) {
            t.setSort(minSort++);
        }
        // 批量修改
        boolean ok = this.updateBatchById(updateList);
        // 后置处理
        dragAfter(updateList, updateIds);

//        throw new ServiceException();
        return ok;
    }

    /**
     * 查询当前节点和子节点中最大的序号
     *
     * @param entity
     * @return
     */
    private Integer getMaxSort(ProjectTemplatePlan entity) {
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectTemplatePlan::getProjectTemplateId, entity.getProjectTemplateId())
                .eq(ProjectTemplatePlan::getParentId, entity.getId())
                .orderByDesc(ProjectTemplatePlan::getSort)
                .last(SqlConstants.LIMIT_1);
        ProjectTemplatePlan one = this.getOne(wrapper);
        if (one != null) {
            return this.getMaxSort(one);
        }
        return entity.getSort();
    }

    @Override
    @Transactional
    public Boolean clear(TemplatePlanClear templatePlanClear) {
        Long projectTemplateId = templatePlanClear.getProjectTemplateId();
        List<Long> ids = clearTemplatePlan(CollUtil.toList(projectTemplateId));

        if (CollUtil.isEmpty(ids)) return true;

        templatePlanClear.setProjectTemplatePlanIds(JSON.toJSONString(ids));
        // 保存删除表述
        return templatePlanClearService.save(templatePlanClear);
    }

    /**
     * 清空计划
     *
     * @param projectTemplateIds 项目模板ID
     * @return
     */
    @Transactional
    public List<Long> clearTemplatePlan(Collection<Long> projectTemplateIds) {
        // 查询所有模板计划
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.select(ProjectTemplatePlan::getId)
                .in(ProjectTemplatePlan::getProjectTemplateId, projectTemplateIds);
        List<ProjectTemplatePlan> list = this.list(wrapper);

        if (CollUtil.isEmpty(list)) {
            return List.of();
        }
        List<Long> ids = list.stream().map(ProjectTemplatePlan::getId).toList();

        // 删除所有前置任务
        templatePlanFrontService.deleteByProjectTemplatePlanIds(ids);

        // 删除项目模板计划
        this.removeBatchByIds(ids);

        return ids;
    }

    /**
     * 获取 Sort 并处理相关数据（相关的Sort和FrontTask）
     *
     * @param projectTemplatePlan
     * @return
     */
    private void calculateSortAndWbs(ProjectTemplatePlan projectTemplatePlan) {
        LambdaQueryWrapper<ProjectTemplatePlan> wrapper = Wrappers.lambdaQuery();
        wrapper.select(ProjectTemplatePlan::getId, ProjectTemplatePlan::getWbs, ProjectTemplatePlan::getSort, ProjectTemplatePlan::getParentId)
                .eq(ProjectTemplatePlan::getProjectTemplateId, projectTemplatePlan.getProjectTemplateId())
                .orderByAsc(ProjectTemplatePlan::getSort, ProjectTemplatePlan::getWbs);
        List<ProjectTemplatePlan> list = this.list(wrapper);

        if (CollUtil.isNotEmpty(list)) {
            AtomicReference<Integer> upOneSort = new AtomicReference<>(1);
            // 3、计划的 Sort 重排
            list.forEach(item -> {
                item.setSort(upOneSort.get());
                Integer sort = upOneSort.get();
                upOneSort.compareAndSet(upOneSort.get(), sort + 1);
            });
            WbsCleanerUtils.cleanWbs(list);

            this.updateBatchById(list);

            // 4、查询后续所有计划的后置任务
            List<Long> ids = list.stream().map(ProjectTemplatePlan::getId).toList();
            LambdaQueryWrapper<TemplatePlanFront> templatePlanFrontWrapper = Wrappers.lambdaQuery();
            templatePlanFrontWrapper.in(TemplatePlanFront::getProjectTemplatePlanId, ids);
            List<TemplatePlanFront> templatePlanFronts = templatePlanFrontService.list(templatePlanFrontWrapper);

            Map<Long, Integer> idSortMap = list.stream().collect(Collectors.toMap(ProjectTemplatePlan::getId, ProjectTemplatePlan::getSort));
            if (CollUtil.isNotEmpty(templatePlanFronts)) {
                // 5、修改后续所有计划的前置任务数据
                templatePlanFronts.forEach(item ->
                        item.setFrontPlanSort(idSortMap.get(item.getProjectTemplatePlanId())));
                templatePlanFrontService.updateBatchById(templatePlanFronts);
            }
        }
    }

    @Override
    public Map<Long, Integer> findIdSortMapByIds(List<Long> ids) {
        throw new ServiceException("开发中");
    }
}

package com.wbyy.pms.modules.log.content.workHour;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/10 14:12
 */
@Slf4j
public class RemoveWorkHourDetailContentBuilder implements IContentBuilder<List<WorkHourDetail>> {
    @Override
    public String buildContent(LogRecord logRecord, List<WorkHourDetail> originalData, List<WorkHourDetail> newData) {
        if (CollectionUtil.isEmpty(originalData)) {
            log.warn("删除任务工时时，原始数据为空");
            return "";
        }
        WorkHourDetail detail = originalData.get(0);
        logRecord.setDataModule(detail.getEntryName());
        logRecord.setDataModuleId(detail.getPlanId().toString());
        logRecord.setBizId(detail.getProjectId().toString());
        logRecord.setBizCode(detail.getProjectNumber());
        logRecord.setBizName(detail.getProjectName());
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        return userName+"删除了一条工时【"
                + DateUtil.formatDate(detail.getFillDate())+"】【"+detail.getWorkHour().toPlainString()+"】h";
    }
}

package com.wbyy.pms.modules.log.content.workHour;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wbyy.common.core.utils.SpringUtils;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.log.sender.LogSender;
import com.wbyy.pms.modules.workhour.entry.domain.WorkHourEntry;
import com.wbyy.pms.modules.workhour.entry.domain.dto.CopyLastWeekDto;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/10 16:24
 */
@Slf4j
public class CopyLastWeekContentBuilder implements IContentBuilder<CopyLastWeekDto> {
    @Override
    public String buildContent(LogRecord logRecord, CopyLastWeekDto originalData, CopyLastWeekDto newData) {
        if (null==newData){
            log.warn("工时复制上周时，新数据为空");
            return "";
        }
        List<WorkHourEntry> entries = newData.getEntries()
                .stream()
                .filter(item->null!=item.getPlanId()&&null!=item.getProjectId())
                .toList();
        if (CollectionUtil.isEmpty(entries)){
            log.warn("工时复制上周时，工时条目为空");
            return "";
        }
        Integer weekNumber = newData.getLastWeekNumber();
        String yearNumber = newData.getLastYearNumber();
        Boolean copyDetail = newData.getCopyDetail();
        String copyContent = copyDetail?"内容":"条目";
        logRecord.setOperationType(copyDetail?"新增工时内容":"新增工时条目");
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        Map<Long, List<WorkHourEntry>> entryGroupByProjectId = entries.stream()
                .collect(Collectors.groupingBy(WorkHourEntry::getProjectId));
        LogSender logSender = SpringUtils.getBean(LogSender.class);
        for (Long projectId : entryGroupByProjectId.keySet()) {
            List<WorkHourEntry> findEntries = entryGroupByProjectId.get(projectId);
            if (CollectionUtil.isEmpty(findEntries)) continue;
            WorkHourEntry entry = findEntries.get(0);
            LogRecord record = new LogRecord();
            BeanUtil.copyProperties(logRecord,record);
            record.setBizId(projectId.toString());
            record.setBizCode(entry.getProjectName());
            record.setBizName(entry.getProjectName());
            record.setDataModule(findEntries.stream().map(WorkHourEntry::getEntryName)
                    .collect(Collectors.joining(",")));
            record.setDataModuleId(findEntries.stream().map(WorkHourEntry::getPlanId)
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
            record.setContent(userName+"复制了了【"+yearNumber+"年"+weekNumber+"周】工时"+copyContent);
            logSender.send(record);
        }
        return "";
    }
}

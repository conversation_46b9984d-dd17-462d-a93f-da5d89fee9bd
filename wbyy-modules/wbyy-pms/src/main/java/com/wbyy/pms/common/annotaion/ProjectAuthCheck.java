package com.wbyy.pms.common.annotaion;

import com.wbyy.common.security.annotation.Logical;

import java.lang.annotation.*;

/**
 * @author: 王金都
 * @date: 2025/5/20 13:49
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ProjectAuthCheck {

    /**
     * 项目id，通过SPEL获取
     */
    String projectId();

    /**
     * 需要校验的权限码
     */
    String[] permCode() default {};

    /**
     * 验证模式：AND | OR，默认AND
     */
    Logical logical() default Logical.AND;
}

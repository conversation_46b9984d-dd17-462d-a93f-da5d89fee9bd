package com.wbyy.pms.common.converter;

import com.wbyy.pms.modules.project.planrelate.domain.ProjectPlanReleaseRelate;
import com.wbyy.pms.modules.project.planrelate.domain.ProjectPlanSnapshotRelate;
import com.wbyy.pms.modules.project.planrelate.domain.ProjectPlanVersionRelate;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProjectPlanRelatesConverter {
    ProjectPlanRelatesConverter INSTANCT = Mappers.getMapper(ProjectPlanRelatesConverter.class);

    ProjectPlanReleaseRelate snapshot2Release(ProjectPlanSnapshotRelate model);

    List<ProjectPlanReleaseRelate> snapshot2Release(List<ProjectPlanSnapshotRelate> model);

    ProjectPlanVersionRelate snapshot2Version(ProjectPlanSnapshotRelate model);

    List<ProjectPlanVersionRelate> snapshot2Version(List<ProjectPlanSnapshotRelate> model);
}

package com.wbyy.pms.modules.team.resourceload.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/4  11:47
 * @description 资源负荷表返回值
 */
@Data
@Builder
public class ResourceLoadVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "每天数据")
    private Map<String, DayData> data;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "用户名字")
    private String userName;

    @Schema(description = "工号")
    private String workNumber;

    @Schema(description = "月总计")
    private BigDecimal total;

    @Data
    @Builder
    public static class DayData implements Serializable {

        @Schema(description = "天")
        private Integer day;

        @Schema(description = "值")
        private BigDecimal value;
    }
}

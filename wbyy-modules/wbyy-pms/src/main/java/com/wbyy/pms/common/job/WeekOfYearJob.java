package com.wbyy.pms.common.job;

import com.wbyy.pms.modules.configuration.weekofyear.service.IWeekOfYearService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: 王金都
 * @date: 2025/6/23 13:51
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class WeekOfYearJob {
    private final IWeekOfYearService weekOfYearService;

    @XxlJob(value = "weekOfYearAsyncJobHandler")
    public ReturnT<String> asyncData(){
        weekOfYearService.asyncData();
        return ReturnT.SUCCESS;
    }
}

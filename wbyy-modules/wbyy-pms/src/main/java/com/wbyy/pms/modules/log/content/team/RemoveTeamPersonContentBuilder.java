package com.wbyy.pms.modules.log.content.team;

import com.wbyy.common.core.utils.SpringUtils;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.project.baserole.domain.ProjectBaseRole;
import com.wbyy.pms.modules.project.baserole.service.IProjectBaseRoleService;
import com.wbyy.pms.modules.project.teamperson.domain.ProjectTeamPersonPO;

/**
 * <AUTHOR>
 * @date 2025/7/8  15:16
 * @description 添加项目成员日志生成器
 */
public class RemoveTeamPersonContentBuilder implements IContentBuilder<ProjectTeamPersonPO> {

    /**
     * 构建日志内容
     *
     * @param logRecord    日志记录对象，包含用户信息、操作信息等
     * @param originalData 原始数据（修改前的数据）
     * @param newData      新数据（修改后的数据）
     * @return String 生成的日志内容
     * <AUTHOR>
     * @date 2025/7/8  15:16
     */
    @Override
    public String buildContent(LogRecord logRecord, ProjectTeamPersonPO originalData, ProjectTeamPersonPO newData) {
        String bizId = logRecord.getBizId();

        // 日志对象是项目
        logRecord.setDataModule(logRecord.getBizName());
        // 获取项目角色名称
        IProjectBaseRoleService baseRoleService = SpringUtils.getBean(IProjectBaseRoleService.class);
        ProjectBaseRole baseRole = baseRoleService.getRoleInfoByProjectIdAndRoleDeptId(Long.parseLong(bizId), newData.getProjectRoleId());
        // 操作用户信息
        String content = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】将【";
        // 拼接日志内容
        content += newData.getRealName() + "（" + newData.getWorkNumber() + "）】移除【" + baseRole.getName() + "】项目角色";
        return content;
    }
}

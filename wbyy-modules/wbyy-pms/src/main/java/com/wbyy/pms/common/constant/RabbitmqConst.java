package com.wbyy.pms.common.constant;

/**
 * <AUTHOR>
 * @date 2025/6/26  14:04
 * @description mq的一些常量配置
 */
public class RabbitmqConst {

    /**
     * 交换机定义
     */
    // pms - project交换机
    public static final String PROJECT_EXCHANGE = "wbyy.project.exchange";

    // pms - workHour交换机
    public static final String WORK_HOUR_EXCHANGE = "wbyy.workHour.exchange";

    /**
     * 队列定义
     */

    // pms - 关联计划改变修改数据队列
    public static final String PLAN_ACTIVE_UPDATE_QUEUE = "plan-active-update-queue";
    // pms - 关联计划改变通知队列
    public static final String PLAN_ACTIVE_NOTICE_QUEUE = "plan-active-notice-queue";
    // pms - 关联计划改变通知队列
    public static final String PLAN_FEEDBACK_UPDATE_QUEUE = "plan-feedback-update-queue";
    // pms - 计划生效后，如果有删除的计划，同步修改合同关联任务的状态
    public static final String PLAN_ACTIVE_SYNC_CONTRACT_QUEUE = "plan-active-sync-contract-queue";

    // pms - 工时填报同步队列
    public static final String WORK_HOUR_SYNC_QUEUE = "work-hour-sync-queue";

    // pms - oa 工作流同步队列
    public static final String OA_FLOW_SYNC_QUEUE = "oa-flow-sync-queue";
    public static final String OA_FLOW_SYNC_TO_OBJECT_QUEUE = "oa-flow-sync-to-object-queue";


    /**
     * 路由key定义
     */
    // pms - 计划生效key
    public static final String PLAN_ACTIVE_ROUTING_KEY = "plan.active";

    // pms - 计划反馈key
    public static final String PLAN_FEEDBACK_ROUTING_KEY = "plan.feedback";

    // pms - 工时填报key
    public static final String WORK_HOUR_FILLED_KEY = "work.hour.filled";
}

package com.wbyy.pms.modules.log.content.plan;

import cn.hutool.core.collection.CollectionUtil;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.contract.nodeplan.domain.ContractNodePlan;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanSnapshot;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: 王金都
 * @date: 2025/7/7 16:42
 */
@Slf4j
public class RemovePlanContentBuilder implements IContentBuilder<List<ProjectPlanSnapshot>> {
    @Override
    public String buildContent(LogRecord logRecord, List<ProjectPlanSnapshot> originalData, List<ProjectPlanSnapshot> newData) {
        if (CollectionUtil.isEmpty(originalData)){
            log.warn("删除任务生成日志时，原数据为空");
            return "";
        }
        String planNames = originalData.stream().map(ProjectPlanSnapshot::getName).collect(Collectors.joining("、"));
        logRecord.setDataModule(planNames);
        logRecord.setDataModuleId(originalData.stream()
                .map(item->item.getId().toString())
                .collect(Collectors.joining("、")));
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        return userName+"将【"+planNames+"】任务删除";
    }
}

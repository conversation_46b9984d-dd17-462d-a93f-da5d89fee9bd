package com.wbyy.pms.modules.team.resourceload.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: 王金都
 * @date: 2025/7/5 19:14
 */
@Data
public class DetailDto {

    @Schema(description = "用户id")
    @NotNull(message = "用户id不得为空")
    private Long userId;

    @Schema(description = "年月，格式：yyyy-MM ,如2025-07，只传本字段查个人的月数据")
    @NotBlank(message = "年月不得为空")
    private String yearMonth;

    @Schema(description = "日，格式：d ,如1,2,11,26，传month和day查个人那一天的明细数据")
    private String day;

    @Schema(description = "项目id，在项目页中查询的时候传")
    private Long projectId;
}

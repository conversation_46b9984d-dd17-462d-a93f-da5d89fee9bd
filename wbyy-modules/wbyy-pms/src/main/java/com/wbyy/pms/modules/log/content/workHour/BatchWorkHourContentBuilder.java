package com.wbyy.pms.modules.log.content.workHour;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.utils.SpringUtils;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.log.sender.LogSender;
import com.wbyy.pms.modules.project.base.domain.ProjectBase;
import com.wbyy.pms.modules.project.base.service.IProjectBaseService;
import com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/10 10:52
 */
@Slf4j
public class BatchWorkHourContentBuilder implements IContentBuilder<List<WorkHourDetail>> {
    @Override
    public String buildContent(LogRecord logRecord, List<WorkHourDetail> originalData, List<WorkHourDetail> newData) {
        if (CollectionUtil.isEmpty(newData)){
            log.warn("移动端上报工时时，新数据为空");
            return "";
        }
        // 1:暂存；2:提交
        Integer operationType = newData.stream().map(WorkHourDetail::getOperationType).findFirst().orElse(null);
        if (null==operationType){
            log.warn("移动端上报工时时，操作类型为空");
            return "";
        }
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        String operationTypeStr = 1==operationType?"暂存":"提交";
        if ("提交".equals(operationTypeStr)){
            logRecord.setOperationType("提交工时");
        }
        if (newData.size()==1){
            return buildContent(userName,operationTypeStr,newData);
        }else {
            IProjectBaseService projectBaseService = SpringUtils.getBean(IProjectBaseService.class);
            LogSender logSender = SpringUtils.getBean(LogSender.class);
            List<Long> projectIds = newData.stream().map(WorkHourDetail::getProjectId).filter(Objects::nonNull).toList();
            List<ProjectBase> projects = projectBaseService.listByIds(projectIds);
            for (ProjectBase project : projects) {
                Long projectId = project.getId();
                List<WorkHourDetail> details = newData.stream()
                        .filter(item -> projectId.equals(item.getProjectId()))
                        .toList();
                if (CollectionUtil.isEmpty(details)) continue;
                LogRecord record = new LogRecord();
                BeanUtil.copyProperties(logRecord,record);
                record.setBizId(projectId.toString());
                record.setBizName(project.getName());
                record.setBizCode(project.getNumber());
                record.setDataModule(details.stream().map(WorkHourDetail::getEntryName)
                        .collect(Collectors.joining(",")));
                record.setDataModuleId(details.stream().map(WorkHourDetail::getPlanId)
                                .filter(Objects::nonNull)
                                .map(String::valueOf)
                        .collect(Collectors.joining(",")));
                record.setContent(buildContent(userName,operationTypeStr,details));
                logSender.send(record);
            }
            return "";
        }
    }

    private String buildContent(String userName,String operationTypeStr,List<WorkHourDetail> details){
        WorkHourDetail detail = details.get(0);
        if (details.size()==1){
            if (null!=detail.getOriginalWorkHour()&&detail.getOriginalWorkHour().compareTo(detail.getWorkHour())!=0){
                return userName+operationTypeStr+"了一条工时，将【"+ DateUtil.formatDate(detail.getFillDate())
                        +"】 【"+detail.getOriginalWorkHour().toPlainString()+"】h 变更为【"+
                        detail.getWorkHour().toPlainString()+"】h"+
                        (StrUtil.isNotBlank(detail.getDescription())?"，工时描述：【"+detail.getDescription()+"】":"");
            }
            return userName+operationTypeStr+"了一条工时【"+ DateUtil.formatDate(detail.getFillDate())
                    +"】 【"+detail.getWorkHour().toPlainString()+"】h "+
                    (StrUtil.isNotBlank(detail.getDescription())?"，工时描述：【"+detail.getDescription()+"】":"");
        }else {
            return userName+"批量"+operationTypeStr+"了【"+details.size()+"】条工时【"+ DateUtil.formatDate(detail.getFillDate())
                    +"】 ";
        }
    }
}

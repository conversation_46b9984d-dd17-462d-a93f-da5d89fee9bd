package com.wbyy.pms.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: 王金都
 * @date: 2025/5/6 16:01
 */
@AllArgsConstructor
@Getter
public enum ProjectInfoRouterEnum {

    // 分析测试中心
    ACT("act","projectInfoAtcService"),
    // 本奥医学
    BENAO("benao","projectInfoBenaoService"),
    // 仿制药BE临床研究中心
    CRCGDBE("crcgdbe","projectInfoCrcgdbeService"),
    // 药物研发中心
    DRDC("drdc","projectInfoDrdcService");


    private final String code;
    private final String beanName;

    public static String getBeanNameByCode(String code){
        return Arrays.stream(ProjectInfoRouterEnum.values()).filter(item->item.getCode().equals(code))
                .map(ProjectInfoRouterEnum::getBeanName)
                .findFirst().orElse(null);
    }

    public static ProjectInfoRouterEnum getByCode(String code){
        return Arrays.stream(ProjectInfoRouterEnum.values()).filter(item->item.getCode().equals(code))
                .findFirst().orElse(null);
    }
}

package com.wbyy.pms.modules.team.resourceload.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/4  11:46
 * @description 资源负荷表入参
 */
@Data
public class ResourceLoadDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "组织id")
    private Long deptId;

    @Schema(description = "类型 1=工时数 2=任务数")
    @NotNull(message = "查询类型不能为空")
    private Integer mode;

    @Schema(description = "月份,yyyy-MM")
    @NotBlank(message = "月份不能为空")
    private String monthNumber;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "项目类型，1=项目成员本项目 2=项目成员全部项目")
    private Integer projectType;
}

package com.wbyy.pms.common.constant;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/5/14 10:44
 */
public class ProjectConst {
    // 0:绿色;1:红色;2:灰色
    public static final Integer GREEN_LIGHT = 0;
    public static final Integer RED_LIGHT = 1;
    public static final Integer GREY_LIGHT = 2;

    // 进度常量
    public static final Integer PROGRESS_0 = 0;
    public static final Integer PROGRESS_100=100;

    // 灰色灯对应的状态
    public static final List<Integer> GREY_STATUS = List.of(2, 3, 4);

    // 叶子节点标识
    public static final Integer LEAF = 1;
    public static final Integer NO_LEAF = 0;

    public static final BigDecimal VALUE_100 = new BigDecimal("100");

    // 里程碑
    public static final int MILESTONE = 1;

    public static final String WBYY_CN = "安徽万邦医药科技股份有限公司";
    public static final String BENAO_CN = "安徽本奥医学科技有限公司";

    // 临期期限
    public static final long DUE_LIMIT = 3L;

    // 正常完成
    public static final int NORMAL_FINISH = 2;
    // 延期完成
    public static final int DELAY_FINISH = 4;
    // 正常进行
    public static final int NORMAL_GOING = 1;
    // 延期进行
    public static final int DELAY_GOING = 3;
}

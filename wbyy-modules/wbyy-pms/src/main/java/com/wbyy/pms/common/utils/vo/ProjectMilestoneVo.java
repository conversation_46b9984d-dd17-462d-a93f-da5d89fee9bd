package com.wbyy.pms.common.utils.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @author: 王金都
 * @date: 2025/5/19 9:16
 */
@Data
public class ProjectMilestoneVo {

    @Schema(description = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planEndDate;

    @Schema(description = "实际结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date realityEndDate;

    @Schema(description = "计划名称")
    private String planName;

    @Schema(description = "状态（1:正常进行;2:正常完成;3:延期进行;4:延期完成）")
    private Integer status;
}

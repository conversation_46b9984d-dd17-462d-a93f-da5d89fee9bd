package com.wbyy.pms.common.config.validation;

import com.wbyy.pms.modules.project.base.domain.ProjectBase;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoAct;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoBenao;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoCrcgdbe;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoDrdc;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * @author: 王金都
 * @date: 2025/5/6 16:56
 */
@Component
public class ProjectBaseValidator implements ConstraintValidator<ConditionalValidation, ProjectBase> {
    @Autowired
    private Validator validator;

    @Override
    public boolean isValid(ProjectBase dto, ConstraintValidatorContext context) {
        if (null==dto) {
            context.buildConstraintViolationWithTemplate("入参不得为空")
                    .addConstraintViolation();
            return false;
        }
        // 1. 先校验父对象本身的属性（如 @NotNull、@Size 等）
        Set<ConstraintViolation<ProjectBase>> parentViolations = validator.validate(dto);
        if (!parentViolations.isEmpty()) {
            // 将父对象的错误信息传递到当前上下文
            context.disableDefaultConstraintViolation();
            parentViolations.forEach(v -> {
                context.buildConstraintViolationWithTemplate(v.getMessage())
                        .addPropertyNode(v.getPropertyPath().toString())
                        .addConstraintViolation();
            });
            return false;
        }
        // 2. 根据中心编码动态校验
        String centerDeptCode = dto.getCenterDeptCode();
        Set<ConstraintViolation<?>> childViolations = new HashSet<>();
        switch (centerDeptCode){
            case "act"-> {
                ProjectInfoAct model = dto.getProjectInfoAct();
                if (null==model){
                    context.buildConstraintViolationWithTemplate("分析测试中心表单不得为空")
                            .addConstraintViolation();
                    return false;
                }else {
                    childViolations.addAll(validator.validate(model));
                }
            }
            case "benao"->{
                ProjectInfoBenao model = dto.getProjectInfoBenao();
                if (null==model){
                    context.buildConstraintViolationWithTemplate("本奥医学表单不得为空")
                            .addConstraintViolation();
                    return false;
                }else {
                    childViolations.addAll(validator.validate(model));
                }
            }
            case "crcgdbe"->{
                ProjectInfoCrcgdbe model = dto.getProjectInfoCrcgdbe();
                if (null==model){
                    context.buildConstraintViolationWithTemplate("仿制药BE临床研究中心表单不得为空")
                            .addConstraintViolation();
                    return false;
                }else {
                    childViolations.addAll(validator.validate(model));
                }
            }
            case "drdc"->{
                ProjectInfoDrdc model = dto.getProjectInfoDrdc();
                if (null==model){
                    context.buildConstraintViolationWithTemplate("药物研发中心表单不得为空")
                            .addConstraintViolation();
                    return false;
                }else {
                    childViolations.addAll(validator.validate(model));
                }
            }
            default -> {
                context.buildConstraintViolationWithTemplate("未维护的项目表单")
                        .addConstraintViolation();
                return false;
            }
        }
        // 3. 处理子对象校验错误
        if (!childViolations.isEmpty()) {
            context.disableDefaultConstraintViolation();
            childViolations.forEach(v -> {
                context.buildConstraintViolationWithTemplate(v.getMessage())
                        .addConstraintViolation();
            });
            return false;
        }
        return true;
    }
}

package com.wbyy.pms.modules.log.content.workHour;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/9 14:26
 */
@Slf4j
public class UpdateWorkHourContentBuilder implements IContentBuilder<WorkHourDetail> {
    @Override
    public String buildContent(LogRecord logRecord, WorkHourDetail originalData, WorkHourDetail newData) {
        if (null==newData){
            log.warn("编辑工时生成日志时，新数据为空");
            return "";
        }
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        if(BigDecimal.ZERO.equals(newData.getWorkHour())){
            return userName+"删除了一条工时【"+ DateUtil.formatDate(newData.getFillDate())
                    +"】 【"+newData.getOriginalWorkHour().toPlainString()+"】h";
        }
        if (null!=newData.getOriginalWorkHour()&&newData.getOriginalWorkHour().compareTo(newData.getWorkHour())!=0){
            return userName+"将工时【"+ DateUtil.formatDate(newData.getFillDate())
                    +"】 【"+newData.getOriginalWorkHour().toPlainString()+"】h 变更为【"+
                    newData.getWorkHour().toPlainString()+"】h"+
                    (StrUtil.isNotBlank(newData.getDescription())?"，工时描述：【"+newData.getDescription()+"】":"");
        }
        return userName+"录入了一条工时【"+ DateUtil.formatDate(newData.getFillDate())
                +"】 【"+newData.getWorkHour().toPlainString()+"】h "+
                (StrUtil.isNotBlank(newData.getDescription())?"，工时描述：【"+newData.getDescription()+"】":"");
    }
}

package com.wbyy.pms.modules.log.content.plan;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.FieldChange;
import com.wbyy.log.model.LogRecord;
import com.wbyy.log.util.FieldUtil;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanSnapshot;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/7/7 15:19
 */
@Slf4j
public class UpdatePlanContentBuilder implements IContentBuilder<ProjectPlanSnapshot> {
    @Override
    public String buildContent(LogRecord logRecord, ProjectPlanSnapshot originalData, ProjectPlanSnapshot newData) {
        List<FieldChange> changes = new ArrayList<>();
        FieldUtil.buildChanges(ProjectPlanSnapshot.class,originalData, newData, changes);
        if (CollectionUtil.isEmpty(changes)){
            log.warn("修改任务生成日志时，没有变动信息");
            return "";
        }
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        String prefix = userName + "将任务【" + logRecord.getDataModule() + "】的";
        StringBuilder content = new StringBuilder();
        for (FieldChange change : changes) {
            content.append("【").append(change.getFieldLabel()).append("】")
                    .append("由【").append(change.getOldValue()).append("】")
                    .append("调整为【").append(change.getNewValue()).append("】");
        }
        if (StrUtil.isBlank(content)) return "";
        return prefix + content;
    }
}

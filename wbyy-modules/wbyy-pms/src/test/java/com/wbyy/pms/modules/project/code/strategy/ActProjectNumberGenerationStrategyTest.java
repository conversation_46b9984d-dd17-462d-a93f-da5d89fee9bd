package com.wbyy.pms.modules.project.code.strategy;

import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.pms.modules.project.code.ProjectNumberGenerationContext;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2025/8/5 10:30
 */
@SpringBootTest
class ActProjectNumberGenerationStrategyTest {
    @Resource
    private ActProjectNumberGenerationStrategy strategy;

    @Test
    void test_generateProjectNumber() {
        ProjectNumberGenerationContext context = ProjectNumberGenerationContext.builder()
                .year(1900).businessType("SSA").build();
        String currentProjectNumber = strategy.getCurrentProjectNumber(context);

        String projectNumber = strategy.generateProjectNumber(context);

        // 比较projectNumber是否为currentProjectNumber+1
        assertEquals(Integer.parseInt(StringUtils.substring(currentProjectNumber, -3)) + 1,
                Integer.parseInt(StringUtils.substring(projectNumber, -3)));
    }
}
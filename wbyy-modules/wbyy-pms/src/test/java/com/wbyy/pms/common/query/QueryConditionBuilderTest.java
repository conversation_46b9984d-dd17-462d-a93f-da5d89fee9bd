package com.wbyy.pms.common.query;

import com.wbyy.biz.common.form.common.query.QueryField;
import com.wbyy.biz.common.preference.modules.setting.domain.SearchWidgetTypeEnum;
import org.junit.jupiter.api.Test;
import org.springframework.data.util.Pair;

import java.util.List;

import static com.wbyy.biz.common.form.common.query.QueryConditionBuilder.buildSearchCondition;
import static com.wbyy.biz.common.form.common.query.QueryConditionBuilder.buildSelectColumns;
import static org.junit.jupiter.api.Assertions.*;
/**
 * <AUTHOR>
 */
 class QueryConditionBuilderTest {

    @Test
    void testBuildSearchCondition() {
        assertEquals("u.username like CONCAT('%', #{field.value}, '%')",
                buildSearchCondition("u", new QueryField("username", SearchWidgetTypeEnum.INPUT, "zhangsan")));

        assertEquals("u.username = #{field.value}",
                buildSearchCondition("u", new Query<PERSON>ield("username", SearchWidgetTypeEnum.NUMBER, 1)));

        assertEquals("u.typeId = #{field.value}",
                buildSearchCondition("u", new QueryField("typeId", SearchWidgetTypeEnum.SELECT, "1")));

        assertEquals("u.typeId = #{field.value}",
                buildSearchCondition("u", new QueryField("typeId", SearchWidgetTypeEnum.TREE_SELECT, "1")));

        assertEquals("u.deptId in (#{field.value[0]}, #{field.value[1]}, #{field.value[2]})",
                buildSearchCondition("u", new QueryField("deptId", SearchWidgetTypeEnum.SELECT_MULTIPLE, List.of(1, 2, 3))));

        assertEquals("u.deptId in (#{field.value[0]}, #{field.value[1]}, #{field.value[2]})",
                buildSearchCondition("u", new QueryField("deptId", SearchWidgetTypeEnum.TREE_SELECT_MULTIPLE, List.of(1, 2, 3))));

        assertEquals("u.status in (#{field.value[0]}, #{field.value[1]}, #{field.value[2]})",
                buildSearchCondition("u", new QueryField("status", SearchWidgetTypeEnum.CHECKBOX, List.of(1, 2, 3))));

        assertEquals("u.sex = #{field.value}",
                buildSearchCondition("u", new QueryField("sex", SearchWidgetTypeEnum.RADIO, "1")));

        assertEquals("u.birthday = #{field.value}",
                buildSearchCondition("u", new QueryField("birthday", SearchWidgetTypeEnum.DATE, "2021-01-01")));

        assertEquals("u.birthday between #{field.value[0]} and #{field.value[1]}",
                buildSearchCondition("u", new QueryField("birthday", SearchWidgetTypeEnum.DATE_RANGE, List.of("2021-01-01", "2021-01-31"))));

        assertEquals("u.createTime = #{field.value}",
                buildSearchCondition("u", new QueryField("createTime", SearchWidgetTypeEnum.DATETIME, "2021-01-01 10:00:00")));

        assertEquals("u.createTime between #{field.value[0]} and #{field.value[1]}",
                buildSearchCondition("u", new QueryField("createTime", SearchWidgetTypeEnum.DATETIME_RANGE,
                        List.of("2021-01-01 10:00:00", "2021-01-31 10:00:00"))));
    }

    @Test
    void testBuildSelectColumns() {
        assertEquals("u.type_id as typeId",
                buildSelectColumns("u", List.of(Pair.of("type_id", "typeId"))));
    }
}
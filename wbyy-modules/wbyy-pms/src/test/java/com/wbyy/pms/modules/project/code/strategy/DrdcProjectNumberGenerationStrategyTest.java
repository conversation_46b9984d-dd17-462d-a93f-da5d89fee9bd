package com.wbyy.pms.modules.project.code.strategy;

import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.pms.modules.project.code.ProjectNumberGenerationContext;
import com.wbyy.pms.modules.project.code.enums.ZYTypeEnum;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2025/8/5 11:30
 */
@SpringBootTest
class DrdcProjectNumberGenerationStrategyTest {
    @Resource
    private DrdcProjectNumberGenerationStrategy strategy;

    @Test
    void test_generateProjectNumber1() {
        ProjectNumberGenerationContext context = ProjectNumberGenerationContext.builder()
                .year(1901).projectName("阿莫西林创新药")
                .innovativeDrug("Z")
                .build();
        String currentProjectNumber = strategy.getCurrentProjectNumber(context);

        String projectNumber = strategy.generateProjectNumber(context);

        // 比较projectNumber是否为currentProjectNumber+1
        assertEquals(Integer.parseInt(StringUtils.substring(currentProjectNumber, -2)) + 1,
                Integer.parseInt(StringUtils.substring(projectNumber, -2)));
    }

    @Test
    void test_generateProjectNumber2() {
        ProjectNumberGenerationContext context = ProjectNumberGenerationContext.builder()
                .year(1901).projectName("阿莫西林短期项目")
                .shortTerm(true)
                .build();
        String currentProjectNumber = strategy.getCurrentProjectNumber(context);

        String projectNumber = strategy.generateProjectNumber(context);

        // 比较projectNumber是否为currentProjectNumber+1
        assertEquals(Integer.parseInt(StringUtils.substring(currentProjectNumber, -2)) + 1,
                Integer.parseInt(StringUtils.substring(projectNumber, -2)));
    }

    @Test
    void test_generateProjectNumber3() {
        ProjectNumberGenerationContext context = ProjectNumberGenerationContext.builder()
                .year(1901).projectName("阿莫西林开发制剂")
                .zyType(ZYTypeEnum.PREPARATION)
                .build();
        String currentProjectNumber = strategy.getCurrentProjectNumber(context);

        String projectNumber = strategy.generateProjectNumber(context);

        // 比较projectNumber是否为currentProjectNumber+1
        assertEquals(Integer.parseInt(StringUtils.substring(currentProjectNumber, -4, -2)) + 1,
                Integer.parseInt(StringUtils.substring(projectNumber, -4, -2)));
    }

    @Test
    void test_generateProjectNumber4() {
        ProjectNumberGenerationContext context = ProjectNumberGenerationContext.builder()
                .year(1901).projectName("阿莫西林普通项目")
                .build();
        String currentProjectNumber = strategy.getCurrentProjectNumber(context);

        String projectNumber = strategy.generateProjectNumber(context);

        // 比较projectNumber是否为currentProjectNumber+1
        assertEquals(Integer.parseInt(StringUtils.substring(currentProjectNumber, -2)) + 1,
                Integer.parseInt(StringUtils.substring(projectNumber, -2)));
    }
}
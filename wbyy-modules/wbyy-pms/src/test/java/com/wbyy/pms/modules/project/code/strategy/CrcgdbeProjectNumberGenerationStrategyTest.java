package com.wbyy.pms.modules.project.code.strategy;

import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.pms.modules.project.code.ProjectNumberGenerationContext;
import com.wbyy.pms.modules.project.code.enums.ExperimentTypeEnum;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2025/8/5 14:30
 */
@SpringBootTest
class CrcgdbeProjectNumberGenerationStrategyTest {
    @Resource
    private CrcgdbeProjectNumberGenerationStrategy strategy;

    @Test
    void test_generateProjectNumber1() {
        ProjectNumberGenerationContext context = ProjectNumberGenerationContext.builder()
                .year(1925).projectName("阿莫西林创新药")
                .experimentType(ExperimentTypeEnum.PRE_EXPERIMENT)
                .filing(false)
                .build();
        String currentProjectNumber = strategy.getCurrentProjectNumber(context);

        String projectNumber = strategy.generateProjectNumber(context);

        // 比较projectNumber是否为currentProjectNumber+1
        assertEquals(Integer.parseInt(StringUtils.substring(currentProjectNumber, -5)) + 1,
                Integer.parseInt(StringUtils.substring(projectNumber, -5)));
    }

    @Test
    void test_generateProjectNumber2() {
        ProjectNumberGenerationContext context = ProjectNumberGenerationContext.builder()
                .year(1901).projectName("阿莫西林创新药")
                .experimentType(ExperimentTypeEnum.PRE_EXPERIMENT)
                .filing(true)
                .build();
        String currentProjectNumber = strategy.getCurrentProjectNumber(context);

        String projectNumber = strategy.generateProjectNumber(context);

        // 比较projectNumber是否为currentProjectNumber+1
        assertEquals(Integer.parseInt(StringUtils.substring(currentProjectNumber, -5)) + 1,
                Integer.parseInt(StringUtils.substring(projectNumber, -5)));
    }

    @Test
    void test_generateProjectNumber3() {
        ProjectNumberGenerationContext context = ProjectNumberGenerationContext.builder()
                .year(1901).projectName("阿莫西林创新药")
                .experimentType(ExperimentTypeEnum.FORMAL_TRIAL)
                .build();
        String currentProjectNumber = strategy.getCurrentProjectNumber(context);

        String projectNumber = strategy.generateProjectNumber(context);

        // 比较projectNumber是否为currentProjectNumber+1
        assertEquals(Integer.parseInt(StringUtils.substring(currentProjectNumber, -3)) + 1,
                Integer.parseInt(StringUtils.substring(projectNumber, -3)));
    }
}
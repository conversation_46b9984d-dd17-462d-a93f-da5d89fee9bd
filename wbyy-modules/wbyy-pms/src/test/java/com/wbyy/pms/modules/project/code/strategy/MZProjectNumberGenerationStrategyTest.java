package com.wbyy.pms.modules.project.code.strategy;

import com.wbyy.pms.modules.project.code.ProjectNumberGenerationContext;
import com.wbyy.pms.modules.project.code.enums.ExperimentTypeEnum;
import com.wbyy.pms.modules.project.code.service.IProjectNumberSequenceService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertEquals;


/**
 * <AUTHOR>
 * @date 2025/7/29 9:30
 */
@SpringBootTest
class MZProjectNumberGenerationStrategyTest {
    @Resource
    private MZProjectNumberGenerationStrategy strategy;
    @Resource
    private BenaoProjectNumberGenerationStrategy benaoStrategy;
    @Resource
    private YrProjectNumberGenerationStrategy yrStrategy;

    @Test
    void test_generateProjectNumber() {
        ProjectNumberGenerationContext context = ProjectNumberGenerationContext.builder()
                .year(1900).build();
        String currentProjectNumber = strategy.getCurrentProjectNumber(context);

        String projectNumber = strategy.generateProjectNumber(context);

        // 比较projectNumber是否为currentProjectNumber+1
        assertEquals(Integer.parseInt(currentProjectNumber.substring(2)) + 1,
                Integer.parseInt(projectNumber.substring(2)));
    }


    // 测试用例 - 本奥
    @Test
    void test_generateProjectNumberBenAo() {
        ProjectNumberGenerationContext context = ProjectNumberGenerationContext.builder()
                .projectName("测试项目")
                .deptCode("BENAO")
                .experimentType(ExperimentTypeEnum.PRE_EXPERIMENT)
                .filing(true)
                .businessType("LC")
                .year(1900).build();

        String projectNumber = benaoStrategy.generateProjectNumber(context);
        System.out.println("projectNumber => " + projectNumber);
    }

    // 测试用例 - 伊然
    @Test
    void test_generateProjectNumberYiran() {
        ProjectNumberGenerationContext context = ProjectNumberGenerationContext.builder()
                .projectName("测试项目")
                .deptCode("YR")
                .experimentType(ExperimentTypeEnum.PRE_EXPERIMENT)
                .filing(true)
                .businessType("LC")
                .year(1900).build();

        String projectNumber = yrStrategy.generateProjectNumber(context);
        System.out.println("projectNumber => " + projectNumber);
    }
}
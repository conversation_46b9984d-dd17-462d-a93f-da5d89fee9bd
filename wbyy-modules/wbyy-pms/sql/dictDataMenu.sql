-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(), 0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(@parentId, '字典数据', '1912021141432614914', '1', 'dictData', 'system/dictData/index', 1, 0, 'C', '0', '0', 'system:dictData:list', '#', 'admin', sysdate(), '', null, '字典数据菜单');


-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '字典数据查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:dictData:list',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '字典数据新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:dictData:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '字典数据修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:dictData:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '字典数据删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'system:dictData:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '字典数据导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'system:dictData:export',       '#', 'admin', sysdate(), '', null, '');
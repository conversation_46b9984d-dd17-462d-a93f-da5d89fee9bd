-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(), 0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(@parentId, '项目模板', '1912021141432614914', '1', 'template', 'project/template/index', 1, 0, 'C', '0', '0', 'project:template:list', '#', 1, sysdate(), null, null, '项目模板菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目模板查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'project:template:list',        '#', 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目模板新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'project:template:add',          '#', 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目模板修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'project:template:edit',         '#', 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目模板删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'project:template:remove',       '#', 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目模板导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'project:template:export',       '#', 1, sysdate(), null, null, '');
-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(), 0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(@parentId, '工时审核流程', '1930431278705143809', '1', 'workHourApproval', 'workhour/workHourApproval/index', 1, 0, 'C', '0', '0', 'workhour:workHourApproval:list', '#', 1, sysdate(), null, null, '工时审核流程菜单', 'pms-system');


-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '工时审核流程查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'workhour:workHourApproval:list',        '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '工时审核流程新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'workhour:workHourApproval:add',          '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '工时审核流程修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'workhour:workHourApproval:edit',         '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '工时审核流程删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'workhour:workHourApproval:remove',       '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '工时审核流程导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'workhour:workHourApproval:export',       '#', 1, sysdate(), null, null, '', 'pms-system');
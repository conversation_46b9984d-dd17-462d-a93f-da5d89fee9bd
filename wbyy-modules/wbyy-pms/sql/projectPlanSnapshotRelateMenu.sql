-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(), 0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(@parentId, '项目计划关联-编制', '3', '1', 'projectPlanSnapshotRelate', 'projectplansnapshotrelate/projectPlanSnapshotRelate/index', 1, 0, 'C', '0', '0', 'projectplansnapshotrelate:projectPlanSnapshotRelate:list', '#', 1, sysdate(), null, null, '项目计划关联-编制菜单', 'pms-system');


-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目计划关联-编制查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'projectplansnapshotrelate:projectPlanSnapshotRelate:list',        '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目计划关联-编制新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'projectplansnapshotrelate:projectPlanSnapshotRelate:add',          '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目计划关联-编制修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'projectplansnapshotrelate:projectPlanSnapshotRelate:edit',         '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目计划关联-编制删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'projectplansnapshotrelate:projectPlanSnapshotRelate:remove',       '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目计划关联-编制导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'projectplansnapshotrelate:projectPlanSnapshotRelate:export',       '#', 1, sysdate(), null, null, '', 'pms-system');
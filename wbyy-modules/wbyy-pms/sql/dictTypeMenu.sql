-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(), 0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(@parentId, '字典类型', '1912021141432614914', '1', 'dictType', 'system/dictType/index', 1, 0, 'C', '0', '0', 'system:dictType:list', '#', '1', sysdate(), null, null, '字典类型菜单');


-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '字典类型查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:dictType:list',        '#', '1', sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '字典类型新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:dictType:add',          '#', '1', sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '字典类型修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:dictType:edit',         '#', '1', sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '字典类型删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'system:dictType:remove',       '#', '1', sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '字典类型导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'system:dictType:export',       '#', '1', sysdate(), null, null, '');
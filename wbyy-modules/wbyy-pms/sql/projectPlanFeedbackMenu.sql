-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(), 0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(@parentId, '项目任务反馈', '3', '1', 'projectPlanFeedback', 'project/projectPlanFeedback/index', 1, 0, 'C', '0', '0', 'project:projectPlanFeedback:list', '#', 1, sysdate(), null, null, '项目任务反馈菜单', 'pms-system');


-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目任务反馈查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'project:projectPlanFeedback:list',        '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目任务反馈新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'project:projectPlanFeedback:add',          '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目任务反馈修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'project:projectPlanFeedback:edit',         '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目任务反馈删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'project:projectPlanFeedback:remove',       '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目任务反馈导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'project:projectPlanFeedback:export',       '#', 1, sysdate(), null, null, '', 'pms-system');
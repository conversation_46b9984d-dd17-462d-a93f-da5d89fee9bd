-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(), 0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(@parentId, '合同基本信息', '1937679298310381570', '1', 'base', 'contract/base/index', 1, 0, 'C', '0', '0', 'contract:base:list', '#', 1, sysdate(), null, null, '合同基本信息菜单', 'pms-system');


-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '合同基本信息查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'contract:base:list',        '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '合同基本信息新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'contract:base:add',          '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '合同基本信息修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'contract:base:edit',         '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '合同基本信息删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'contract:base:remove',       '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '合同基本信息导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'contract:base:export',       '#', 1, sysdate(), null, null, '', 'pms-system');
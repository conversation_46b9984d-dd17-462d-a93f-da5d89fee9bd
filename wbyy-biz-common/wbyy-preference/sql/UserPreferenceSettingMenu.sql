-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(), 0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(@parentId, '用户偏好设置', '1950398226253946882', '1', 'setting', 'preference/setting/index', 1, 0, 'C', '0', '0', 'preference:setting:list', '#', 1, sysdate(), null, null, '用户偏好设置菜单', 'pms-system');


-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '用户偏好设置保存', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'preference:setting:save',        '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '用户偏好设置重置', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'preference:setting:remove',          '#', 1, sysdate(), null, null, '', 'pms-system');
package com.wbyy.biz.common.preference.modules.setting.controller;

import com.wbyy.biz.common.preference.modules.setting.domain.UserPreferenceSettingPO;
import com.wbyy.biz.common.preference.modules.setting.domain.query.UserPreferenceSettingQuery;
import com.wbyy.biz.common.preference.modules.setting.service.IUserPreferenceSettingService;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.common.security.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户偏好设置Controller
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "用户偏好设置")
@RequestMapping("/user/preference/setting")
public class UserPreferenceSettingController extends BaseController {

    private final IUserPreferenceSettingService userPreferenceSettingService;

//    @Operation(summary = "查询")
//    @RequiresPermissions("preference:setting:select")
//    @GetMapping("/select")
//    public R<UserPreferenceSettingPO> select(@ParameterObject UserPreferenceSettingQuery query) {
//        final Long userId = SecurityUtils.getUserId();
//        query.setUserId(userId);
//        return R.ok(userPreferenceSettingService.select(query));
//    }

    @Operation(summary = "保存")
    @RequiresPermissions("preference:setting:save")
    @Log(title = "用户偏好设置保存", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public R<Boolean> save(@RequestBody @Validated UserPreferenceSettingPO po) {
        po.setUserId(SecurityUtils.getUserId());
        return R.ok(userPreferenceSettingService.save(po));
    }

    @Operation(summary = "重置")
    @RequiresPermissions("preference:setting:remove")
    @Log(title = "用户偏好设置重置", businessType = BusinessType.DELETE)
    @DeleteMapping("/reset")
    public R<Void> reset(@RequestBody UserPreferenceSettingQuery query) {
        query.setUserId(SecurityUtils.getUserId());
        userPreferenceSettingService.reset(query);
        return R.ok();
    }
}

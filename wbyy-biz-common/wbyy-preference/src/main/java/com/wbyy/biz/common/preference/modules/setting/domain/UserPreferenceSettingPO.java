package com.wbyy.biz.common.preference.modules.setting.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.common.core.handler.mybatis.LongListTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;
import java.util.List;

/**
 * 用户偏好设置对象 user_preference_setting
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "user_preference_setting", autoResultMap = true)
@Schema(description = "用户偏好设置实体类")
@EqualsAndHashCode(callSuper = true)
public class UserPreferenceSettingPO extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    /** 模块 */
    @Excel(name = "模块")
    @Schema(description = "模块", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模块不能为空")
    @Size(max = 30, message = "模块不能超过 30 个字符")
    private String module;

    /** 业务类型 */
    @Excel(name = "业务类型")
    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务类型不能为空")
    @Size(max = 30, message = "业务类型不能超过 30 个字符")
    private String businessType;

    /** 偏好类型 */
    @Excel(name = "偏好类型")
    @Schema(description = "偏好类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "偏好类型不能为空")
    private PreferenceTypeEnum preferenceType;

    /** 设置 */
    @Excel(name = "字段配置")
    @Schema(description = "字段配置", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "字段配置不能为空")
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> setting;
}

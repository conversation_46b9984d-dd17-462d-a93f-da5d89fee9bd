package com.wbyy.biz.common.preference.modules.setting.domain;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 * @date 2025/8/1 9:31
 */
public enum SearchWidgetTypeEnum {
    INPUT("input", "文本框"),
    SELECT("select", "下拉单选框"),
    SELECT_MULTIPLE("select_multiple", "下拉多选框"),
    NUMBER("number", "数字输入框"),
    CHECKBOX("checkbox", "复选框"),
    RADIO("radio", "单选框"),
    DATE("date", "日期选择"),
    DATE_RANGE("daterange", "日期范围选择"),
    TREE_SELECT("treeSelect", "树形单选"),
    TREE_SELECT_MULTIPLE("treeSelect_multiple", "树形多选"),
    DATETIME("datetime", "日期时间选择"),
    DATETIME_RANGE("datetimerange", "日期时间范围")
    ;

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;

    SearchWidgetTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
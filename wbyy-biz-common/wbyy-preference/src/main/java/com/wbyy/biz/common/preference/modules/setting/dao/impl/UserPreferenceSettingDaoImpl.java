package com.wbyy.biz.common.preference.modules.setting.dao.impl;

import cn.hutool.core.util.StrUtil;
import java.util.List;
import java.util.Arrays;

import com.wbyy.biz.common.preference.modules.setting.domain.query.UserPreferenceSettingQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.biz.common.preference.modules.setting.mapper.UserPreferenceSettingMapper;
import com.wbyy.biz.common.preference.modules.setting.dao.IUserPreferenceSettingDao;
import com.wbyy.biz.common.preference.modules.setting.domain.UserPreferenceSettingPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户偏好设置Dao实现层
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserPreferenceSettingDaoImpl extends ServiceImpl<UserPreferenceSettingMapper, UserPreferenceSettingPO> implements IUserPreferenceSettingDao {

    @Override
    public UserPreferenceSettingPO selectById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<UserPreferenceSettingPO> selectList(UserPreferenceSettingPO po) {
        return this.findByPO(po);
    }

    @Override
    public List<UserPreferenceSettingPO> findByPO(UserPreferenceSettingPO po) {
        if (po == null) {
            return List.of();
        }
        LambdaQueryWrapper<UserPreferenceSettingPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(po.getUserId() != null, UserPreferenceSettingPO::getUserId, po.getUserId());
        wrapper.eq(StrUtil.isNotEmpty(po.getModule()), UserPreferenceSettingPO::getModule, po.getModule());
        wrapper.eq(StrUtil.isNotEmpty(po.getBusinessType()), UserPreferenceSettingPO::getBusinessType, po.getBusinessType());
        wrapper.eq(po.getPreferenceType() != null, UserPreferenceSettingPO::getPreferenceType, po.getPreferenceType());
        wrapper.orderByDesc(UserPreferenceSettingPO::getId);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(UserPreferenceSettingPO po) {
        return this.save(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(UserPreferenceSettingPO po) {
        return this.updateById(po);
    }

    /**
     * 批量删除用户偏好设置
     *
     * @param ids 需要删除的用户偏好设置主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }


    @Override
    public UserPreferenceSettingPO selectOne(UserPreferenceSettingQuery query) {
        LambdaQueryWrapper<UserPreferenceSettingPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPreferenceSettingPO::getUserId, query.getUserId());
        queryWrapper.eq(UserPreferenceSettingPO::getModule, query.getModule());
        queryWrapper.eq(UserPreferenceSettingPO::getBusinessType, query.getBusinessType());
        queryWrapper.eq(UserPreferenceSettingPO::getPreferenceType, query.getPreferenceType());
        return this.getOne(queryWrapper, false);
    }

    @Override
    public UserPreferenceSettingPO selectOne(UserPreferenceSettingPO po) {
        LambdaQueryWrapper<UserPreferenceSettingPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPreferenceSettingPO::getUserId, po.getUserId());
        queryWrapper.eq(UserPreferenceSettingPO::getModule, po.getModule());
        queryWrapper.eq(UserPreferenceSettingPO::getBusinessType, po.getBusinessType());
        queryWrapper.eq(UserPreferenceSettingPO::getPreferenceType, po.getPreferenceType());
        return this.getOne(queryWrapper);
    }
}

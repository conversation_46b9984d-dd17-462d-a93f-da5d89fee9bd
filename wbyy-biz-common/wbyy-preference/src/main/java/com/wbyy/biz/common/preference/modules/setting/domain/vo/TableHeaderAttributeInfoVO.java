package com.wbyy.biz.common.preference.modules.setting.domain.vo;

import com.wbyy.biz.common.preference.modules.setting.domain.TableHeaderAlignEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/30 14:34
 */
@Data
@Schema(description = "表头属性信息")
public class TableHeaderAttributeInfoVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "属性ID")
    private Long id;

    @Schema(description = "英文属性名")
    private String prop;

    @Schema(description = "中文名称")
    private String label;

    @Schema(description = "固定列标识")
    private Boolean tableHeaderLeftFixedFlag = Boolean.FALSE;

    @Schema(description = "是否可配")
    private Boolean tableHeaderSupportConfigFlag = Boolean.TRUE;

    @Schema(description = "表头宽度")
    private Integer tableHeaderWidth;

    @Schema(description = "表头对齐方式")
    private TableHeaderAlignEnum tableHeaderAlign = TableHeaderAlignEnum.LEFT;
}

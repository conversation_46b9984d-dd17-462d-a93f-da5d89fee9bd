package com.wbyy.biz.common.preference.modules.setting.domain.vo;

import com.wbyy.biz.common.preference.modules.setting.domain.SearchWidgetTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/30 17:56
 */
@Data
@Schema(description = "查询条件属性信息")
public class SearchAttributeInfoVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "属性ID")
    private Long id;

    @Schema(description = "英文属性名")
    private String prop;

    @Schema(description = "中文名称")
    private String label;

    @Schema(description = "控件类型")
    private SearchWidgetTypeEnum searchWidgetType;

    @Schema(description = "数据源配置ID")
    private Long dataSourceConfigId;

    @Schema(description = "数据源配置")
    private String dataSourceConfig;
}
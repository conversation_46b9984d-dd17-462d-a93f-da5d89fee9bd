package com.wbyy.biz.common.preference.modules.setting.service.impl;

import com.wbyy.biz.common.preference.modules.setting.dao.IUserPreferenceSettingDao;
import com.wbyy.biz.common.preference.modules.setting.domain.UserPreferenceSettingPO;
import com.wbyy.biz.common.preference.modules.setting.domain.query.UserPreferenceSettingQuery;
import com.wbyy.biz.common.preference.modules.setting.service.IUserPreferenceSettingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户偏好设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserPreferenceSettingServiceImpl implements IUserPreferenceSettingService {
    private final IUserPreferenceSettingDao userPreferenceSettingDao;

    @Override
    public UserPreferenceSettingPO select(UserPreferenceSettingQuery query) {
        return userPreferenceSettingDao.selectOne(query);
    }

    @Override
    @Transactional
    public Boolean save(UserPreferenceSettingPO po) {
        UserPreferenceSettingPO exist = userPreferenceSettingDao.selectOne(po);
        if (exist == null) {
            return userPreferenceSettingDao.insert(po);
        }
        po.setId(exist.getId());
        return userPreferenceSettingDao.updateById(po);
    }

    @Override
    @Transactional
    public void reset(UserPreferenceSettingQuery query) {
        UserPreferenceSettingPO exist = select(query);
        if (exist == null) {
            return;
        }
        userPreferenceSettingDao.removeById(exist);
    }
}

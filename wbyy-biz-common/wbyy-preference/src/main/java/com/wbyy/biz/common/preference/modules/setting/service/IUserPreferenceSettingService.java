package com.wbyy.biz.common.preference.modules.setting.service;

import com.wbyy.biz.common.preference.modules.setting.domain.UserPreferenceSettingPO;
import com.wbyy.biz.common.preference.modules.setting.domain.query.UserPreferenceSettingQuery;

/**
 * 用户偏好设置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IUserPreferenceSettingService {
    /**
     * 查询用户偏好设置
     *
     * @param query 用户偏好设置查询条件
     * @return 用户偏好设置
     */
    UserPreferenceSettingPO select(UserPreferenceSettingQuery query);

    /**
     * 保存用户偏好设置
     *
     * @param po 用户偏好设置
     * @return 结果
     */
    Boolean save(UserPreferenceSettingPO po);

    /**
     * 重置用户偏好设置
     *
     * @param query 用户偏好设置查询条件
     * @return 结果
     */
    void reset(UserPreferenceSettingQuery query);
}

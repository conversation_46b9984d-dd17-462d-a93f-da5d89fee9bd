package com.wbyy.biz.common.preference.modules.setting.domain.query;

import com.github.pagehelper.IPage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/1 11:51
 */
@Data
@Schema(description = "自定义查询参数")
public class CustomSearchQuery {
    @Schema(description = "当前记录起始索引")
    private Long pageNum;

    @Schema(description = "每页显示记录数")
    private Long pageSize;

    @Schema(description = "排序列")
    private String orderByColumn;

    @Schema(description = "排序的方向desc或者asc")
    private String isAsc = "asc";

    @Schema(description = "模块")
    private String module;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "自定义对象ID")
    private Long objectInfoId;

    @Schema(description = "自定义查询条件")
    private Map<String, Object> customSearchFields;

}
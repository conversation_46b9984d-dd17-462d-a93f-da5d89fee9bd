package com.wbyy.biz.common.preference.modules.setting.domain;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 * @date 2025/7/31 15:22
 */
public enum TableHeaderAlignEnum {
    LEFT("left", "居左"),
    CENTER("center", "居中"),
    RIGHT("right", "居右");

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;

    TableHeaderAlignEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
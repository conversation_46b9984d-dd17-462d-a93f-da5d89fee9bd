package com.wbyy.biz.common.preference.modules.setting.dao;

import java.util.List;
import com.wbyy.biz.common.preference.modules.setting.domain.UserPreferenceSettingPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.biz.common.preference.modules.setting.domain.query.UserPreferenceSettingQuery;

/**
 * 用户偏好设置Dao接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IUserPreferenceSettingDao extends IService<UserPreferenceSettingPO> {
    /**
     * 查询用户偏好设置
     *
     * @param id 用户偏好设置主键
     * @return 用户偏好设置
     */
    UserPreferenceSettingPO selectById(Long id);

    /**
     * 查询用户偏好设置列表
     *
     * @param po 用户偏好设置
     * @return 用户偏好设置集合
     */
    List<UserPreferenceSettingPO> selectList(UserPreferenceSettingPO po);

    /**
     * 根据 po 中不为null的字段 查询用户偏好设置列表
     *
     * @param po 用户偏好设置
     * @return 用户偏好设置列表
     */
    List<UserPreferenceSettingPO> findByPO(UserPreferenceSettingPO po);

    /**
     * 新增用户偏好设置
     *
     * @param po 用户偏好设置
     * @return 结果
     */
    boolean insert(UserPreferenceSettingPO po);

    /**
     * 修改用户偏好设置
     *
     * @param po 用户偏好设置
     * @return 结果
     */
    boolean update(UserPreferenceSettingPO po);

    /**
     * 批量删除用户偏好设置
     *
     * @param ids 需要删除的用户偏好设置主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 根据查询条件查询用户偏好设置
     * @param query
     * @return
     */
    UserPreferenceSettingPO selectOne(UserPreferenceSettingQuery query);

    /**
     * 根据查询条件查询用户偏好设置
     * @param po
     * @return
     */
    UserPreferenceSettingPO selectOne(UserPreferenceSettingPO po);
}

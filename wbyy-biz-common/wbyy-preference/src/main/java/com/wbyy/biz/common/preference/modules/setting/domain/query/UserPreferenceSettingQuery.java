package com.wbyy.biz.common.preference.modules.setting.domain.query;

import com.wbyy.biz.common.preference.modules.setting.domain.PreferenceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/30 11:35
 */
@Data
@Builder
@Schema(description = "用户偏好设置查询参数")
public class UserPreferenceSettingQuery implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long userId;

    @Schema(description = "模块", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模块不能为空")
    private String module;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String businessType;

    @Schema(description = "自定义对象ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long objectInfoId;

    @Schema(description = "偏好类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private PreferenceTypeEnum preferenceType;
}
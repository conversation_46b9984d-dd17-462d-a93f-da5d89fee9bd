package com.wbyy.biz.common.drag.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.constant.HttpMagConstants;
import com.wbyy.common.core.constant.SqlConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.redis.annotation.RedisLock;
import com.wbyy.biz.common.drag.domain.BaseVxeHierarchyEntity;
import com.wbyy.biz.common.drag.service.IBaseVxeHierarchyService;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.wbyy.biz.common.drag.constant.LockKeyConst .LOCK_BASE_VXE_HIERARCHY;
import static com.wbyy.biz.common.drag.utils.DragUtils.getNextSort;

/**
 * 有层级的 VXE table ServiceImpl
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor
public class BaseVxeHierarchyServiceImpl<M extends BaseMapper<T>, T extends BaseVxeHierarchyEntity>
        extends ServiceImpl<M, T>
        implements IBaseVxeHierarchyService<T> {

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    @RedisLock(prefix = LOCK_BASE_VXE_HIERARCHY + "insert:", key = "#entity.id")
    public T insert(T entity, Set<Long> updateIds) {
        // 前置
        this.insertBefore(entity);

        Boolean insertUp = entity.getInsertUp();
        Long parentId = entity.getParentId();
        // 参照对象
        T targetObject = this.getById(entity.getTargetObjectId());

        // 这里分五种情况：
        LambdaQueryWrapper<T> wrapper = getWrapper(entity);
        // 5、insertUp false 并且 targetObjectId = null 即：快速添加 (一直加在第一层的最后一个位置)
        if (targetObject == null) {
            wrapper.orderByDesc(T::getSort)
                    .last(SqlConstants.LIMIT_1);
            T one = this.getOne(wrapper);
            entity.setSort(getNextSort(one, true));
        } else {
            // 1、insertUp true 并且 parentId = (targetObjectId 的 parentId) 即：同级的上一个位置
            if (insertUp && parentId.equals(targetObject.getParentId())) {
                entity.setSort(getNextSort(targetObject, false));
            }
            // 2、insertUp true 并且 parentId = (targetObjectId 的 id) 即：子级的第一个位置
            if (insertUp && parentId.equals(targetObject.getId())) {
                entity.setSort(getNextSort(targetObject, true));
            }
            // 3、insertUp false 并且 parentId = (targetObjectId 的 parentId) 即：同级的下一个位置
            if (!insertUp && parentId.equals(targetObject.getParentId())) {
                wrapper = getWrapper(entity);
                wrapper.eq(T::getParentId, parentId)
                        .ge(T::getSort, targetObject.getSort())
                        .last(SqlConstants.LIMIT_1)
                        .orderByAsc(T::getSort);
                T one = this.getOne(wrapper);
                entity.setSort(getNextSort(one, true));
            }
            // 4、insertUp false 并且 parentId = (targetObjectId 的 id) 即：子级的最后一个位置
            if (!insertUp && parentId.equals(targetObject.getId())) {
                wrapper = getWrapper(entity);
                wrapper.eq(T::getParentId, targetObject.getId())
                        .last(SqlConstants.LIMIT_1)
                        .orderByDesc(T::getSort);
                T one = this.getOne(wrapper);

                entity.setSort(getNextSort(one, true));
            }
        }

        // 开启事务
        transactionTemplate.execute(status -> {
            // 保存当前数据
            getProxy().save(entity);

            // 查询新增时，修改修改的数据
            List<T> modifiedData = getNeedModifiedDataWithInsert(entity);
            if (CollUtil.isNotEmpty(modifiedData)) {
                AtomicReference<Integer> minSort = new AtomicReference<>();
                modifiedData.stream().min(Comparator.comparingInt(T::getSort)).ifPresentOrElse(
                        e -> minSort.set(e.getSort()),
                        () -> minSort.set(1));

                modifiedData.forEach(item -> {
                    item.setSort(minSort.getAndSet(minSort.get() + 1));
                });
                getProxy().updateBatchById(modifiedData);
            }

            this.insertAfter(entity, modifiedData, updateIds);
            return true;
        });
        return this.getById(entity.getId());
    }

    @Override
    @RedisLock(prefix = LOCK_BASE_VXE_HIERARCHY + "drag:", key = "#entity.id")
    public boolean drag(T entity, Set<Long> updateIds) {
        // 拖拽的参照对象
        Long targetObjectId = entity.getTargetObjectId();
        // 拖拽后插入的位置：向上 or 向下
        Boolean insertUp = entity.getInsertUp();

        // 需要拖拽的原始对象
        T presentEntity = this.getById(entity.getId());

        // 前置处理
        dragBefore(presentEntity);

        // 拖拽后位置的参考对象
        T targetObject = this.getById(targetObjectId);

        // 1 数据校验
        dragCheckData(presentEntity, targetObject, insertUp);

        // 查询所有需要修改顺序的数据
        List<T> list = getChangeDragData(targetObject, presentEntity);

        List<T> updateList = new ArrayList<>();
        // 向上拖拽
        if (targetObject.getSort() < presentEntity.getSort()) {
            // list 分三个集合
            Integer targetObjMaxSort = this.getMaxSortByChildrenAndThis(targetObject);
            List<T> firstList = list.stream()
                    .filter(item -> item.getSort() <= targetObjMaxSort).toList();
            List<T> middleList = list.stream()
                    .filter(item -> item.getSort() > targetObjMaxSort && item.getSort() < presentEntity.getSort()).toList();
            List<T> lastList = list.stream()
                    .filter(item -> item.getSort() >= presentEntity.getSort()).toList();
            if (insertUp) {
                // 向上插入
                updateList.addAll(lastList);
                updateList.addAll(firstList);
                updateList.addAll(middleList);
            } else {
                // 向下插入
                updateList.addAll(firstList);
                updateList.addAll(lastList);
                updateList.addAll(middleList);
            }
        } else {
            // 向下拖拽
            // list 分三个集合
            Integer presentEntityMaxSort = this.getMaxSortByChildrenAndThis(presentEntity);
            List<T> firstList = list.stream().filter(item -> item.getSort() <= presentEntityMaxSort).toList();
            List<T> middleList = list.stream().filter(item -> item.getSort() > presentEntityMaxSort && item.getSort() < targetObject.getSort()).toList();
            List<T> lastList = list.stream().filter(item -> item.getSort() >= targetObject.getSort()).toList();

            if (insertUp) {
                // 向上插入
                updateList.addAll(middleList);
                updateList.addAll(firstList);
                updateList.addAll(lastList);
            } else {
                // 向下插入
                updateList.addAll(middleList);
                updateList.addAll(lastList);
                updateList.addAll(firstList);
            }
        }

        Integer minSort = updateList.stream().min(Comparator.comparingInt(T::getSort)).get().getSort();
        for (T t : updateList) {
            t.setSort(minSort++);
        }

        // 开启事务
        Boolean executed = transactionTemplate.execute(status -> {
            // 批量修改
            getProxy().updateBatchById(updateList);

            // 后置处理
            dragAfter(updateList, updateIds);
            return true;
        });
        return Boolean.TRUE.equals(executed);
    }

    /**
     * 获取拖拽后需要修改序号的数据
     *
     * @param targetObject  被拖拽的对象
     * @param presentEntity 当前对象
     * @return 拖拽后需要修改序号的数据
     */
    private List<T> getChangeDragData(T targetObject, T presentEntity) {
        LambdaQueryWrapper<T> wrapper = getWrapper(targetObject);
        // 需要调整顺序的最大的 sort
        Integer maxSort = null;
        if (targetObject.getSort() < presentEntity.getSort()) {
            // 向上拖拽
            // 查询最大的序号
            maxSort = getMaxSortByChildrenAndThis(presentEntity);
            wrapper.ge(T::getSort, targetObject.getSort())
                    .le(T::getSort, maxSort);
        } else {
            // 向下拖拽
            // 查询最大的序号
            maxSort = getMaxSortByChildrenAndThis(targetObject);
            wrapper.le(T::getSort, maxSort)
                    .ge(T::getSort, presentEntity.getSort());
        }
        wrapper.orderByAsc(T::getSort);
        return this.list(wrapper);
    }

    /**
     * 查询当前节点和子节点中最大的序号
     *
     * @param entity 当前节点
     * @return 最大的序号
     */
    private Integer getMaxSortByChildrenAndThis(T entity) {
        LambdaQueryWrapper<T> wrapper = getWrapper(entity);
        wrapper.eq(T::getParentId, entity.getId())
                .orderByDesc(T::getSort)
                .last(SqlConstants.LIMIT_1);
        T one = this.getOne(wrapper);
        if (one != null) {
            return this.getMaxSortByChildrenAndThis(one);
        }
        return entity.getSort();
    }

    @Override
    @RedisLock(prefix = LOCK_BASE_VXE_HIERARCHY + "up-or-down-grade:", key = "#entity.id")
    public boolean upOrDownGrade(T entity, Set<Long> updateIds) {
        Boolean up = entity.getUp();

        // 需要升级、降级的原始对象
        T presentEntity = this.getById(entity.getId());

        Optional.ofNullable(presentEntity).orElseThrow(() -> new ServiceException(HttpMagConstants.NO_DATA));

        // 前面的兄弟节点
        T upBrotherNode = upOrDownCheckData(entity.getUp(), presentEntity);

        // 前置回调
        upOrDownGradeBefore(presentEntity);
        transactionTemplate.execute(status -> {
            if (up) {
                // 升级，父节点的 parentId, 设置为自己的 parentId
                T parentNode = this.getById(presentEntity.getParentId());

                // 查询需要修改序号的数据集合
                // 查询升级前 同级的节点中最大的 序号
                // （如何最后一个兄弟节点有子节点，该方法会返回最后一个兄弟节点的所有子节点的最大序号）
                Integer maxSort = getMaxSortBySameLeve(presentEntity);

                List<T> list = getListBySortInterval(presentEntity.getSort(), maxSort, presentEntity);
                // 拖拽的节点的最大序号，包含子节点
                Integer maxSortByChildrenAndThis = getMaxSortByChildrenAndThis(presentEntity);
                if (CollUtil.isNotEmpty(list)) {
                    // 设置拖拽节点的parentID
                    T t = list.get(0);
                    list.remove(0);
                    t.setParentId(parentNode.getParentId());
                    list.add(0, t);

                    List<T> firstList = list.stream().filter(item -> item.getSort() <= maxSortByChildrenAndThis).toList();
                    List<T> lastList = list.stream().filter(item -> item.getSort() > maxSortByChildrenAndThis).toList();

                    List<T> updateList = new ArrayList<>();
                    updateList.addAll(lastList);
                    updateList.addAll(firstList);
                    // 计算最小值
                    int minSort = updateList.stream().min(Comparator.comparingInt(T::getSort)).get().getSort();
                    for (T t1 : updateList) {
                        t1.setSort(minSort++);
                    }
                    getProxy().updateBatchById(updateList);
                }
            } else {
                // 降级，把上一个兄弟节点的ID 设置为自己的parentID
                presentEntity.setParentId(upBrotherNode.getId());
                getProxy().updateById(presentEntity);
            }

            // 后置回调
            upOrDownGradeAfter(entity.getUp(), presentEntity, upBrotherNode, updateIds);
            return true;
        });
        return true;
    }


    /**
     * 根据序号区间，查询数据
     *
     * @param minSort 最小值
     * @param maxSort 最大值
     * @param entity  实体
     * @return
     */
    protected List<T> getListBySortInterval(Integer minSort, Integer maxSort, T entity) {
        LambdaQueryWrapper<T> wrapper = getWrapper(entity);
        wrapper.ge(T::getSort, minSort)
                .le(T::getSort, maxSort)
                .orderByAsc(T::getSort);
        return this.list(wrapper);
    }

    /**
     * 查询同级的节点中最大的 序号
     *
     * @param entity 当前节点
     * @return 最大的序号
     */
    private Integer getMaxSortBySameLeve(T entity) {
        LambdaQueryWrapper<T> wrapper = getWrapper(entity);
        wrapper.eq(T::getParentId, entity.getParentId())
                .orderByDesc(T::getSort)
                .last(SqlConstants.LIMIT_1);
        T one = this.getOne(wrapper);
        if (one != null) {
            return getMaxSortByChildrenAndThis(one);
        }
        // 如果为空 数据有问题，
        throw new ServiceException(HttpMagConstants.NO_DATA);
    }

    /**
     * 数据校验
     *
     * @param up            升级
     * @param presentEntity 需要升级降级的对象
     * @return 升级的兄弟节点
     */
    @Nullable
    protected T upOrDownCheckData(Boolean up, T presentEntity) {
        // 第一级节点不允许升级
        if (presentEntity.getParentId() == 0L && up) {
            throw new ServiceException(HttpMagConstants.NOT_UP);
        }

        LambdaQueryWrapper<T> wrapper = getWrapper(presentEntity);
        wrapper.eq(T::getParentId, presentEntity.getParentId())
                .lt(T::getSort, presentEntity.getSort())
                .orderByDesc(T::getSort);
        T upBrotherNode = this.getOne(wrapper, false);

        // 叶子节点，并且前面没有兄弟节点 不允许降级
        if (upBrotherNode == null && !up) {
            throw new ServiceException(HttpMagConstants.NOT_DOWN);
        }
        return upBrotherNode;
    }

    /**
     * 数据校验
     *
     * @param presentEntity 拖拽的源对象
     * @param targetObject 拖拽的目标对象
     * @param insertUp 插入位置
     * @param <T> 实体类型
     */
    protected static <T extends BaseVxeHierarchyEntity> void dragCheckData(T presentEntity, T targetObject, Boolean insertUp) {

        Optional.ofNullable(presentEntity).orElseThrow(() -> new ServiceException(HttpMagConstants.NO_DATA));
        Optional.ofNullable(targetObject).orElseThrow(() -> new ServiceException(HttpMagConstants.NO_DATA));

        if (targetObject.getId().equals(presentEntity.getId())) {
            throw new ServiceException(HttpMagConstants.DRAG_INVALID);
        }

        // 1.1 校验是否同一层级
        if (!presentEntity.getParentId().equals(targetObject.getParentId())) {
            throw new ServiceException(StrUtil.format("必须在同级拖拽", presentEntity.getSort(), targetObject.getSort()));
        }
        // 1.2 校验位置是否合法
        if (insertUp) {
            // 向上插入
            if (targetObject.getSort() - presentEntity.getSort() == 1) {
                throw new ServiceException(StrUtil.format("当前顺序为【{}】，拖拽后的位置为【{}】，不符合要求", presentEntity.getSort(), targetObject.getSort()));
            }
        } else {
            // 向下插入
            if (presentEntity.getSort() - targetObject.getSort() == 1) {
                throw new ServiceException(StrUtil.format("当前顺序为【{}】，拖拽后的位置为【{}】，不符合要求", presentEntity.getSort(), targetObject.getSort()));
            }
        }
    }

    /**
     * 拖拽之前
     *
     * @param entity 拖拽的对象（最新数据对象）
     */
    protected void dragBefore(T entity) {

    }

    /**
     * 拖拽之后
     *
     * @param list      变动的数据集合（最新数据对象）
     * @param updateIds
     */
    protected void dragAfter(List<T> list, Set<Long> updateIds) {

    }

    /**
     * 升级降级之前
     *
     * @param entity 升级降级的对象（最新数据对象）
     */
    protected void upOrDownGradeBefore(T entity) {

    }

    /**
     * 升级降级之后
     *
     * @param up 升级
     * @param entity        升级降级的对象（最新数据对象）
     * @param upBrotherNode 升级降级前的 上一个兄弟节点
     * @param updateIds
     */
    protected void upOrDownGradeAfter(Boolean up, T entity, T upBrotherNode, Set<Long> updateIds) {

    }

    /**
     * 新增之后
     *
     * @param entity       新增的对象
     * @param modifiedData 涉及到修改的数据
     * @param updateIds
     */
    protected void insertAfter(T entity, List<T> modifiedData, Set<Long> updateIds) {
    }

    /**
     * 新增之前
     *
     * @param entity 新增的对象
     */
    protected void insertBefore(T entity) {
    }

    /**
     * 查询新增时，修改修改的数据
     *
     * @param entity 新增的对象
     * @return 修改的数据集合
     */
    private List<T> getNeedModifiedDataWithInsert(T entity) {
        LambdaQueryWrapper<T> wrapper = getWrapper(entity);
        wrapper.ge(T::getSort, entity.getSort())
                .orderByAsc(T::getSort)
                .orderByDesc(T::getCreateTime);
        return this.list(wrapper);
    }
}

package com.wbyy.biz.common.drag.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.wbyy.orm.core.domain.BaseAuditEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * 无层级的 VXE table Domain
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@SuperBuilder
@Accessors(chain = true)
@Schema(description = "拖拽实体类")
public class BaseVxeEntity extends BaseAuditEntity {
    @Serial
    private static final long serialVersionUID = 4626937714730755824L;

    /**
     * 主键id
     */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    @TableId
    private Long id;

    @Schema(description = "自增ID（排序字段）")
    @TableField
    private Integer sort;


    @Schema(description = "拖拽后的参照对象主键ID")
    @TableField(exist = false)
    private Long targetObjectId;

    @Schema(description = "拖拽后的参照对象排序字段")
    @TableField(exist = false)
    private Long targetObjectSort;

    /**
     * 是否向上插，默认:是
     */
    @TableField(exist = false)
    @Schema(description = "是否向上插，默认:是", hidden = true)
    private Boolean insertUp = Boolean.TRUE;
}

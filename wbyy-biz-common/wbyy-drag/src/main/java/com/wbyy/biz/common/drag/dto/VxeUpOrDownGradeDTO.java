package com.wbyy.biz.common.drag.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 升级降级入参
 *
 * <AUTHOR>
 */
@Data
public class VxeUpOrDownGradeDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -6498952818331796407L;

    @Schema(description = "主键id")
    @NotNull(message = "主键必传")
    private Long id;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "是否升级（默认升级，降级传 false）", defaultValue = "true")
    private Boolean up = Boolean.TRUE;

}

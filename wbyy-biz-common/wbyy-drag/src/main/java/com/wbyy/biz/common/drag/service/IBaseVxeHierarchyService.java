package com.wbyy.biz.common.drag.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.biz.common.drag.domain.BaseVxeHierarchyEntity;

import java.util.Set;

/**
 * 有层级的 VXE table
 *
 * <AUTHOR>
 */
public interface IBaseVxeHierarchyService<T extends BaseVxeHierarchyEntity> extends IService<T> {

    /**
     * 获取代理对象，用于数据库操作
     *
     * @return 子类代理对象
     */
    default IBaseVxeHierarchyService<T> getProxy() {
        throw new ServiceException("请子类重写该方法，并返回子类代理对象");
    }

    /**
     * 获取 Wrappers ，子类可以重写，添加特有的条件
     *
     * @param entity
     * @return
     */
    default LambdaQueryWrapper<T> getWrapper(T entity) {
        return Wrappers.lambdaQuery((Class<T>) entity.getClass());
    }

    /**
     * 新增
     *
     * @param entity    新增的对象
     * @param updateIds
     * @return 新增的对象
     */
    T insert(T entity, Set<Long> updateIds);

    /**
     * 拖拽
     *
     * @param entity
     * @param updateIds
     * @return
     */
    boolean drag(T entity, Set<Long> updateIds);

    /**
     * 升级降级
     *
     * @param entity 升级降级对象
     * @param updateIds 修改的数据ID集合
     * @return 是否成功
     */
    boolean upOrDownGrade(T entity, Set<Long> updateIds);

}

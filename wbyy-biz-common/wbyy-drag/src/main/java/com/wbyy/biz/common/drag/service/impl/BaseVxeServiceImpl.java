package com.wbyy.biz.common.drag.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.biz.common.drag.domain.BaseVxeEntity;
import com.wbyy.biz.common.drag.service.IBaseVxeService;
import com.wbyy.common.core.constant.HttpMagConstants;
import com.wbyy.common.core.constant.SqlConstants;
import com.wbyy.common.core.exception.ServiceException;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

import static com.wbyy.biz.common.drag.utils.DragUtils.getNextSort;

/**
 * 无层级的 VXE table ServiceImpl
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor
public class BaseVxeServiceImpl<M extends BaseMapper<T>, T extends BaseVxeEntity> extends ServiceImpl<M, T> implements IBaseVxeService<T> {

    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 新增之后
     *
     * @param entity       新增的对象
     * @param modifiedData 涉及到修改的数据
     * @param updateIds
     */
    protected void insertAfter(T entity, List<T> modifiedData, Set<Long> updateIds) {
    }

    /**
     * 新增之前
     *
     * @param entity 新增的对象
     */
    protected void insertBefore(T entity) {
    }

    @Override
    public T insert(T entity, Set<Long> updateIds) {
        // 前置
        this.insertBefore(entity);

        Boolean insertUp = entity.getInsertUp();
        // 参照对象
        T targetObject = this.getById(entity.getTargetObjectId());

        // 这里分3种情况：
        LambdaQueryWrapper<T> wrapper = getWrapper(entity);
        // 3、insertUp false 并且 targetObjectId = null
        // 即：快速添加 (一直加在最后一个位置)
        if (targetObject == null) {
            wrapper.orderByDesc(T::getSort)
                    .last(SqlConstants.LIMIT_1);
            T one = this.getOne(wrapper);
            entity.setSort(getNextSort(one, true));
        } else {
            // 1、insertUp true 并且 parentId = (targetObjectId 的 parentId) 即：同级的上一个位置
            if (insertUp) {
                entity.setSort(getNextSort(targetObject, false));
            } else {
                // 2、insertUp false 并且 parentId = (targetObjectId 的 parentId) 即：同级的下一个位置
                wrapper = getWrapper(entity);
                wrapper.ge(T::getSort, targetObject.getSort())
                        .last(SqlConstants.LIMIT_1)
                        .orderByAsc(T::getSort);
                T one = this.getOne(wrapper);
                entity.setSort(getNextSort(one, true));
            }
        }

        // 开启事务
        transactionTemplate.execute(status -> {
            // 保存当前数据
            getProxy().save(entity);

            // 查询新增时，修改修改的数据
            List<T> modifiedData = getNeedModifiedDataWithInsert(entity);
            if (CollUtil.isNotEmpty(modifiedData)) {
                AtomicReference<Integer> minSort = new AtomicReference<>();
                modifiedData.stream().min(Comparator.comparingInt(T::getSort)).ifPresentOrElse(
                        e -> minSort.set(e.getSort()),
                        () -> minSort.set(1));

                modifiedData.forEach(item -> {
                    item.setSort(minSort.getAndSet(minSort.get() + 1));
                });
                getProxy().updateBatchById(modifiedData);
            }

            this.insertAfter(entity, modifiedData, updateIds);
            return true;
        });
        return this.getById(entity.getId());
    }

    /**
     * 查询新增时，修改修改的数据
     *
     * @param entity 新增的对象
     * @return 修改的数据集合
     */
    private List<T> getNeedModifiedDataWithInsert(T entity) {
        LambdaQueryWrapper<T> wrapper = getWrapper(entity);
        wrapper.ge(T::getSort, entity.getSort())
                .orderByAsc(T::getSort)
                .orderByDesc(T::getCreateTime);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean drag(T entity) {
        // 拖拽的参照对象
        Long targetObjectId = entity.getTargetObjectId();
        // 拖拽后插入的位置：向上 or 向下
        Boolean insertUp = entity.getInsertUp();

        // 需要拖拽的原始对象
        T presentEntity = this.getById(entity.getId());
        // 前置处理
        dragBefore(presentEntity);

        // 拖拽后位置的参考对象
        T targetObject = this.getById(targetObjectId);

        // 数据校验
        checkData(insertUp, targetObject, presentEntity);

        // 查询所有需要修改顺序的数据
        LambdaQueryWrapper<T> wrapper = getWrapper(entity);
        if (targetObject.getSort() < presentEntity.getSort()) {
            // 向下拖拽
            wrapper.ge(T::getSort, targetObject.getSort())
                    .le(T::getSort, presentEntity.getSort());
        } else {
            // 向上拖拽
            wrapper.le(T::getSort, targetObject.getSort())
                    .ge(T::getSort, presentEntity.getSort());
        }
        wrapper.orderByAsc(T::getSort);
        List<T> list = this.list(wrapper);

        if (targetObject.getSort() < presentEntity.getSort()){
            // 向上拖拽
            T t = list.get(list.size() - 1);
            list.remove(list.size() - 1);
            if (insertUp){
                // 向上插入
                list.add(0, t);
            } else {
                // 向下插入
                list.add(1, t);
            }
        } else {
            // 向下拖拽
            T t = list.get(0);
            list.remove(0);
            if (insertUp){
                // 向上插入
                list.add(list.size() - 1, t);
            } else {
                // 向下插入
                list.add(list.size(), t);
            }
        }

        Integer minSort = list.stream().min(Comparator.comparingInt(T::getSort)).get().getSort();
        for (T t : list) {
            t.setSort(minSort++);
        }
        // 批量修改
        boolean isOk =  this.updateBatchById(list);
        // 后置处理
        dragAfter(list);
        return isOk;
    }

    /**
     * 数据校验
     * @param insertUp
     * @param targetObject
     * @param presentEntity
     * @param <T>
     */
    private static <T extends BaseVxeEntity> void checkData(Boolean insertUp, T targetObject, T presentEntity) {
        if (targetObject == null || presentEntity == null){
            throw new ServiceException(HttpMagConstants.NO_DATA);
        }
        // 数据校验
        if (insertUp) {
            // 向上插入
            if (targetObject.getSort() - presentEntity.getSort() == 1) {
                throw new ServiceException(StrUtil.format("当前顺序为【{}】，拖拽后的位置为【{}】，不符合要求", presentEntity.getSort(), targetObject.getSort()));
            }
        } else {
            // 向下插入
            if (presentEntity.getSort() - targetObject.getSort() == 1) {
                throw new ServiceException(StrUtil.format("当前顺序为【{}】，拖拽后的位置为【{}】，不符合要求", presentEntity.getSort(), targetObject.getSort()));
            }
        }
    }

    /**
     * 拖拽之前
     *
     * @param entity 拖拽的对象（查询数据库）
     */
    protected void dragBefore(T entity) {

    }

    /**
     * 拖拽之后
     *
     * @param list 变动的数据集合
     */
    protected void dragAfter(List<T> list) {

    }
}

package com.wbyy.biz.common.drag.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.biz.common.drag.domain.BaseVxeEntity;

import java.util.Set;

/**
 * 无层级的 VXE table
 *
 * <AUTHOR>
 */
public interface IBaseVxeService<T extends BaseVxeEntity> extends IService<T> {

    /**
     * 获取代理对象，用于数据库操作
     *
     * @return 子类代理对象
     */
    default IBaseVxeService<T> getProxy() {
        throw new ServiceException("请子类重写该方法，并返回子类代理对象");
    }

    /**
     * 获取 Wrappers ，子类可以重写，添加特有的条件
     *
     * @param entity
     * @return
     */
    default LambdaQueryWrapper<T> getWrapper(T entity) {
        return Wrappers.lambdaQuery((Class<T>) entity.getClass());
    }

    /**
     * 拖拽
     *
     * @param entity 拖拽的对象
     * @return 拖拽结果
     */
    boolean drag(T entity);


    /**
     * 新增
     *
     * @param entity    新增的对象
     * @param updateIds 修改ID集合
     * @return 新增的对象
     */
    T insert(T entity, Set<Long> updateIds);
}

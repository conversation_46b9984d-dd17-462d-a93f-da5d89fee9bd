package com.wbyy.biz.common.form.modules.objectattribute.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wbyy.biz.common.form.modules.objectattribute.domain.ObjectAttributeInfoPO;
import com.wbyy.biz.common.form.modules.objectattribute.domain.vo.ObjectAttributeInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 对象属性信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface ObjectAttributeInfoMapper extends BaseMapper<ObjectAttributeInfoPO> {


    /**
     * ALTER TABLE 添加字段
     *
     * @param sql sql片段
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/16 13:34
     */
    boolean alterColumn(@Param("sql") String sql);


    /**
     * 根据表名，表字段， 检查字段是否有数据
     *
     * @param tableName 表名
     * @param fieldName 字段名
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/17 08:35
     */
    boolean checkTableHasValue(@Param("tableName") String tableName,
                               @Param("fieldName") String fieldName);

    /**
     * 删除表字段
     *
     * @param tableName 表名
     * @param fieldName 字段名
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/17 08:45
     */
    boolean deleteColumn(@Param("tableName") String tableName,
                         @Param("fieldName") String fieldName);

    List<ObjectAttributeInfoVo> selectSimpleByObjectInfoId(@Param("objectInfoId") Long objectInfoId);
}

package com.wbyy.biz.common.form.common.utils;

import cn.hutool.core.util.StrUtil;
import com.wbyy.biz.common.form.common.constant.CommonConstants;

/**
 * <AUTHOR>
 * @date 2025/7/28 14:33
 */
public class ObjectUtils {

    /**
     * 移除字段前缀
     *
     * @param originalFiled
     * @return String
     * <AUTHOR>
     * @date 2025/7/28 14:34
     */
    public static String removeFiled(String originalFiled){
        if (StrUtil.isBlank(originalFiled)) return originalFiled;
        return originalFiled.replaceFirst(CommonConstants.FIELD_NAME_PREFIX,"");
    }

    /**
     * 添加字段前缀
     *
     * @param originalFiled
     * @return String
     * <AUTHOR>
     * @date 2025/7/28 16:43
     */
    public static String appendField(String originalFiled){
        if (StrUtil.isBlank(originalFiled)) return originalFiled;
        return CommonConstants.FIELD_NAME_PREFIX+originalFiled;
    }

    /**
     * 驼峰转下划线
     *
     * @param camelCase
     * @return String
     */
    public static String camelToUnderline(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        char[] chars = camelCase.toCharArray();

        for (int i = 0; i < chars.length; i++) {
            char current = chars[i];

            boolean isUpper = Character.isUpperCase(current);
            boolean prevIsLower = i > 0 && Character.isLowerCase(chars[i - 1]);
            boolean nextIsLower = i < chars.length - 1 && Character.isLowerCase(chars[i + 1]);

            // 添加下划线的条件：
            // 1. 当前是大写
            // 2. 当前不是首字符
            // 3. 前一个是小写 或 当前后接小写（处理 ID -> id, Id -> id）
            if (isUpper && i > 0 && (prevIsLower || nextIsLower)) {
                result.append('_');
            }

            result.append(Character.toLowerCase(current));
        }

        return result.toString();
    }

    public static void main(String[] args) {
        System.out.println(camelToUnderline("userName"));  // user_name
        System.out.println(camelToUnderline("UserName"));  // user_name
        System.out.println(camelToUnderline("userID"));    // user_id
        System.out.println(camelToUnderline("UserID"));    // user_id
    }
}

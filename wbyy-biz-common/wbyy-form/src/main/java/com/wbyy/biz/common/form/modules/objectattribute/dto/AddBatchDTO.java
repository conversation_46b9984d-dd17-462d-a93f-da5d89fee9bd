package com.wbyy.biz.common.form.modules.objectattribute.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 批量添加对象字段
 * <AUTHOR>
 * @date 2025/7/17 09:29
 */
@Schema(description = "批量添加对象字段")
@Data
public class AddBatchDTO {

    @Schema(description = "对象信息id")
    @NotNull(message = "对象信息id不能为空")
    private Long objectInfoId;

    @Schema(description = "通用字段id")
    @NotNull(message = "通用字段id不能为空")
    private List<Long> commonAttributeIds;
}
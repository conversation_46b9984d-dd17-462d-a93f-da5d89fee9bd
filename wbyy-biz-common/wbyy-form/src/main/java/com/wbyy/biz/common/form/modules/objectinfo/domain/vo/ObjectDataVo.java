package com.wbyy.biz.common.form.modules.objectinfo.domain.vo;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/29 17:04
 */
@Data
public class ObjectDataVo {
    @Schema(description = "对象id")
    private Long objectInfoId;

    @Schema(description = "数据id")
    private Long id;

    @Schema(description = "对象数据")
    private Map<String,Object> data;
}

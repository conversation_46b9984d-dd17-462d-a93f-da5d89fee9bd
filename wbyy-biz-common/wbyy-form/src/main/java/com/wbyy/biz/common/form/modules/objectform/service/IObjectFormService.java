package com.wbyy.biz.common.form.modules.objectform.service;

import java.util.List;

import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormPO;
import com.wbyy.biz.common.form.modules.objectform.domain.dto.ObjectFormConfigDto;

/**
 * 对象表单Service接口
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IObjectFormService {
    /**
     * 查询对象表单
     *
     * @param id 对象表单主键
     * @return 对象表单
     */
    ObjectFormPO selectById(Long id);

    /**
     * 根据对象Id和表单类型获取默认的表单配置
     *
     * @param objectInfoId
     * @param formType
     * @return ObjectFormPO
     * <AUTHOR>
     * @date 2025/7/30 14:40
     */
    ObjectFormPO getInfoByObject(Long objectInfoId,String formType);

    /**
     * 查询对象表单列表
     *
     * @param po 对象表单
     * @return 对象表单集合
     */
    List<ObjectFormPO> selectList(ObjectFormPO po);

    /**
     * 新增对象表单
     *
     * @param po 对象表单
     * @return 结果
     */
    boolean insert(ObjectFormPO po);

    /**
     * 复用
     *
     * @param po
     * @return Long
     * <AUTHOR>
     * @date 2025/7/12 10:36
     */
    Long copy(ObjectFormPO po);

    /**
     * 修改对象表单
     *
     * @param po 对象表单
     * @return 结果
     */
    boolean update(ObjectFormPO po);

    /**
     * 批量删除对象表单
     *
     * @param ids 需要删除的对象表单主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    boolean configForm(ObjectFormConfigDto dto);

}

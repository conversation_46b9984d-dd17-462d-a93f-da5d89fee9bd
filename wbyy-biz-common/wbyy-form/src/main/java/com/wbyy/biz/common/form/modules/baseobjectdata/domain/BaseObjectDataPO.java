package com.wbyy.biz.common.form.modules.baseobjectdata.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;

/**
 * base业务数据与对象数据Id关联对象 base_object_data
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "base_object_data", autoResultMap = true)
@Schema(description = "base业务数据与对象数据Id关联实体类")
@EqualsAndHashCode(callSuper = true)
public class BaseObjectDataPO extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键Id */
    @Schema(description = "主键Id（新增：不传；修改：必传）")
    private Long id;

    /** 对象组标识，project、contract... */
    @Excel(name = "对象组标识，project、contract...")
    @Schema(description = "对象组标识，project、contract...", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "对象组标识，project、contract...不能为空")
    @Size(max = 32, message = "对象组标识，project、contract...不能超过 32 个字符")
    private String groupKey;

    /** base数据id，具体base表数据Id */
    @Excel(name = "base数据id，具体base表数据Id")
    @Schema(description = "base数据id，具体base表数据Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "base数据id，具体base表数据Id不能为空")
    private Long baseId;

    /** 对象Id */
    @Excel(name = "对象Id")
    @Schema(description = "对象Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "对象Id不能为空")
    private Long objectInfoId;

    /** 对象数据Id */
    @Excel(name = "对象数据Id")
    @Schema(description = "对象数据Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "对象数据Id不能为空")
    private Long objectDataId;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;




}

package com.wbyy.biz.common.form.common.converter;

import com.wbyy.biz.common.form.modules.commonattribute.domain.CommonAttributeInfoPO;
import com.wbyy.biz.common.form.modules.objectattribute.domain.ObjectAttributeInfoPO;
import com.wbyy.biz.common.form.modules.objectattribute.dto.ObjectAttributeInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/11 09:32
 */
@Mapper
public interface AttributeConverter {
    AttributeConverter INSTANCT = Mappers.getMapper(AttributeConverter.class);
    ObjectAttributeInfoPO dto2Po(ObjectAttributeInfoDTO dto);
    CommonAttributeInfoPO objectAttribute2CommonAttribute(ObjectAttributeInfoPO objectAttribute);
    ObjectAttributeInfoPO commonAttribute2ObjectAttribute(CommonAttributeInfoPO commonAttribute);
    List<ObjectAttributeInfoPO> commonAttribute2ObjectAttribute(List<CommonAttributeInfoPO> commonAttributes);
}
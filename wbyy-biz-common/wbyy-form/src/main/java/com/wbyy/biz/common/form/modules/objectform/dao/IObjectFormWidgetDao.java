package com.wbyy.biz.common.form.modules.objectform.dao;

import java.util.Collection;
import java.util.List;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormWidgetPO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 对象表单组件信息Dao接口
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IObjectFormWidgetDao extends IService<ObjectFormWidgetPO> {
    /**
     * 查询对象表单组件信息
     *
     * @param id 对象表单组件信息主键
     * @return 对象表单组件信息
     */
    ObjectFormWidgetPO selectById(Long id);

    /**
     * 查询对象表单组件信息列表
     *
     * @param po 对象表单组件信息
     * @return 对象表单组件信息集合
     */
    List<ObjectFormWidgetPO> selectList(ObjectFormWidgetPO po);

    /**
     * 根据 po 中不为null的字段 查询对象表单组件信息列表
     *
     * @param po 对象表单组件信息
     * @return 对象表单组件信息列表
     */
    List<ObjectFormWidgetPO> findByPO(ObjectFormWidgetPO po);

    /**
     * 新增对象表单组件信息
     *
     * @param po 对象表单组件信息
     * @return 结果
     */
    boolean insert(ObjectFormWidgetPO po);

    /**
     * 修改对象表单组件信息
     *
     * @param po 对象表单组件信息
     * @return 结果
     */
    boolean update(ObjectFormWidgetPO po);

    /**
     * 批量删除对象表单组件信息
     *
     * @param ids 需要删除的对象表单组件信息主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 根据表单id集合删除表单组件
     *
     * @param objectFormIds
     * @return void
     * <AUTHOR>
     * @date 2025/7/12 10:32
     */
    void deleteByObjectFormIds(Collection<Long> objectFormIds);

    /**
     * 复制表单组件
     *
     * @param oldObjectFormId
     * @param newObjectFormId
     * @return void
     * <AUTHOR>
     * @date 2025/7/12 10:43
     */
    void copy(Long oldObjectFormId, Long newObjectFormId);

    /**
     * 根据表单id获取表单组件
     *
     * @param objectFormId
     * @return List<ObjectFormWidgetPO>
     * <AUTHOR>
     * @date 2025/7/12 10:46
     */
    List<ObjectFormWidgetPO> listByObjectFormId(Long objectFormId);

    /**
     * 根据表单id批量保存表单组件
     *
     * @param widgets
 * @param objectFormId
     * @return void
     * <AUTHOR>
     * @date 2025/7/12 10:52
     */
    void saveBatchByObjectFormId(List<ObjectFormWidgetPO> widgets, Long objectFormId);
}

package com.wbyy.biz.common.form.modules.objectform.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;

/**
 * 对象单数据源配置对象 object_form_data_source
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "object_form_data_source", autoResultMap = true)
@Schema(description = "对象单数据源配置实体类")
@EqualsAndHashCode(callSuper = true)
public class ObjectFormDataSourcePO extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    @Schema(description = "配置id")
    @NotBlank(message = "配置id不能为空")
    private String configId;

    /** 数据源配置json */
    @Excel(name = "数据源配置json")
    @Schema(description = "数据源配置json", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据源配置json不能为空")
    private String dataSourceConfig;

    /** 数据源配置名称 */
    @Excel(name = "数据源配置名称")
    @Schema(description = "数据源配置名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据源配置名称不能为空")
    private String label;

    /** 数据源类型 */
    @Excel(name = "数据源类型")
    @Schema(description = "数据源类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据源类型不能为空")
    private String dataSourceType;

    /** 数据源字典编码 */
    @Excel(name = "数据源字典编码")
    @Schema(description = "数据源字典编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String dictCode;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;




}

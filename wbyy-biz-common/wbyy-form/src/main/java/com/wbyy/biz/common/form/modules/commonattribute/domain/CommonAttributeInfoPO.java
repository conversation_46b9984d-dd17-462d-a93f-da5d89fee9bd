package com.wbyy.biz.common.form.modules.commonattribute.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.List;

/**
 * 通用属性信息对象 common_attribute_info
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "common_attribute_info", autoResultMap = true)
@Schema(description = "通用属性信息实体类")
@EqualsAndHashCode(callSuper = true)
public class CommonAttributeInfoPO extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    @Schema(description = "字段id集合（查询用）")
    @TableField(exist = false)
    private List<Long> ids;

    /** 字段名称 */
    @Excel(name = "字段名称")
    @Schema(description = "字段名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "字段名称不能为空")
    @Size(max = 30, message = "字段名称不能超过 30 个字符")
    private String name;

    /**
     * 英文名称
     */
    @Excel(name = "英文名称")
    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 100, message = "英文名称不能超过 100 个字符")
    @NotNull(message = "英文名称不能为空")
    private String propertyName;

    /** 字段键值 */
    @Excel(name = "字段键值")
    @Schema(description = "字段键值", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 100, message = "字段键值不能超过 100 个字符")
    private String keyValue;

    /** 字段类型(字典) */
    @Excel(name = "字段类型(字典)")
    @Schema(description = "字段类型(字典)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "字段类型不能为空")
    @Size(max = 30, message = "字段类型(字典)不能超过 30 个字符")
    private String fieldType;

    /** 字段长度 */
    @Excel(name = "字段长度")
    @Schema(description = "字段长度")
    @Max(value = 255, message = "字段长度不能超过 255 ")
    private Integer fieldLength;

    /** 字段小数位长度 */
    @Excel(name = "字段小数位长度")
    @Schema(description = "字段小数位长度")
    @Max(value = 10, message = "字段小数位长度不能超过 10 ")
    private Integer fieldDecimalLength;

    /** 默认值 */
    @Excel(name = "默认值")
    @Schema(description = "默认值")
    @Size(max = 100, message = "默认值不能超过 100 个字符")
    private String defaultValue;

    /** 单位 */
    @Excel(name = "单位")
    @Schema(description = "单位")
    @Size(max = 30, message = "单位不能超过 30 个字符")
    private String unit;

    /** 英文单位 */
    @Excel(name = "英文单位")
    @Schema(description = "英文单位")
    @Size(max = 30, message = "英文单位不能超过 30 个字符")
    private String unitEn;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;


}

package com.wbyy.biz.common.form.modules.objectinfo.mapper;

import com.wbyy.biz.common.form.common.query.QueryField;
import com.wbyy.biz.common.form.modules.objectinfo.domain.dto.ObjectDataDbDto;
import com.wbyy.system.api.model.LoginUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/7/29 15:26
 */
public interface ObjectDataMapper {

    void save(@Param("tableName") String tableName,
              @Param("columns") List<String> columns,
              @Param("values") List<Object> values);

    void update(@Param("id") Long id,
                @Param("tableName") String tableName,
                @Param("dbDtos") List<ObjectDataDbDto> dbDtos);

    Map<String, Object> info(@Param("id") Long id,
                             @Param("tableName") String tableName,
                             @Param("columns") Set<String> columns);

    /**
     * 根据自定义表明，主键，删除数据
     *
     * @param id        自定义表主键
     * @param tableName 自定义表名
     * @param loginUser 登录用户
     * <AUTHOR>
     * @date 2025/8/5 10:47
     */
    void removeByTableId(@Param("id") Long id,
                         @Param("tableName") String tableName,
                         @Param("loginUser") LoginUser loginUser);

    /**
     * 执行sql
     *
     * @param systemBuildInFlag
     * @param querySQL           sql
     * @param fromSQL
     * @param searchSystemFields
     * @param searchCustomFields
     * @param permissionSql
     * @return List<Map < String, Object>>
     * <AUTHOR>
     * @date 2025/8/6 16:38
     */

    List<Map<String, Object>> executionSQL(@Param("systemBuildInFlag") Boolean systemBuildInFlag,
                                           @Param("querySQL") String querySQL,
                                           @Param("fromSQL") String fromSQL,
                                           @Param("searchSystemFields")List<QueryField> searchSystemFields,
                                           @Param("searchCustomFields")List<QueryField> searchCustomFields,
                                           @Param("permissionSql")String permissionSql);

}

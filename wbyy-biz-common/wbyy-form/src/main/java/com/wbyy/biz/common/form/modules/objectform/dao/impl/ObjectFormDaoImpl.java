package com.wbyy.biz.common.form.modules.objectform.dao.impl;

import cn.hutool.core.util.StrUtil;
import java.util.List;
import java.util.Arrays;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.biz.common.form.modules.objectform.mapper.ObjectFormMapper;
import com.wbyy.biz.common.form.modules.objectform.dao.IObjectFormDao;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import static com.wbyy.common.core.constant.Constants.DISABLE_STATUS;

/**
 * 对象表单Dao实现层
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectFormDaoImpl extends ServiceImpl<ObjectFormMapper, ObjectFormPO> implements IObjectFormDao {

    @Override
    public ObjectFormPO selectById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<ObjectFormPO> selectList(ObjectFormPO po) {
        return this.findByPO(po);
    }

    @Override
    public List<ObjectFormPO> findByPO(ObjectFormPO po) {
        if (po == null) {
            return List.of();
        }
        LambdaQueryWrapper<ObjectFormPO> wrapper = Wrappers.lambdaQuery();
        wrapper.select(ObjectFormPO::getId,ObjectFormPO::getFormName,ObjectFormPO::getStatusFlag,ObjectFormPO::getDefaultFlag,
                ObjectFormPO::getFormType,ObjectFormPO::getCreateNameBy,ObjectFormPO::getCreateBy,ObjectFormPO::getCreateTime,
                ObjectFormPO::getObjectInfoId);
        wrapper.eq(po.getObjectInfoId() != null, ObjectFormPO::getObjectInfoId, po.getObjectInfoId());
        wrapper.like(StrUtil.isNotEmpty(po.getFormName()), ObjectFormPO::getFormName, po.getFormName());
        wrapper.eq(StrUtil.isNotEmpty(po.getFormType()), ObjectFormPO::getFormType, po.getFormType());
        wrapper.eq(null!=po.getStatusFlag(), ObjectFormPO::getStatusFlag, po.getStatusFlag());
        wrapper.eq(null!=po.getDefaultFlag(), ObjectFormPO::getDefaultFlag, po.getDefaultFlag());
        wrapper.orderByDesc(ObjectFormPO::getId);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ObjectFormPO po) {
        return this.save(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ObjectFormPO po) {
        return this.updateById(po);
    }

    /**
     * 批量删除对象表单
     *
     * @param ids 需要删除的对象表单主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    public ObjectFormPO getDefaultByObjectIdByFormTypeNotById(Long objectInfoId,String formType, Long id) {
        LambdaQueryWrapper<ObjectFormPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectFormPO::getObjectInfoId,objectInfoId);
        queryWrapper.eq(ObjectFormPO::getFormType,formType);
        queryWrapper.ne(null!=id,ObjectFormPO::getId,id);
        return this.getOne(queryWrapper,false);
    }

    @Override
    public void unDefaultByObjectInfoIdByFormType(Long objectInfoId, String formType) {
        LambdaUpdateWrapper<ObjectFormPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ObjectFormPO::getDefaultFlag,DISABLE_STATUS);
        updateWrapper.eq(ObjectFormPO::getObjectInfoId,objectInfoId);
        updateWrapper.eq(ObjectFormPO::getFormType,formType);
        this.update(updateWrapper);
    }

    @Override
    public Long getIdByObject(Long objectInfoId, String formType) {
        return this.baseMapper.selectIdByObject(objectInfoId,formType);
    }

}

package com.wbyy.biz.common.form.modules.objectinfo.service;

import com.alibaba.fastjson2.JSONObject;
import com.wbyy.biz.common.form.modules.objectinfo.domain.vo.ObjectInfoVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/29 16:03
 */
public interface IObjectDataRelationService {
    /**
     * 获取业务所关联的对象组标识
     *
     * @return String
     * <AUTHOR>
     * @date 2025/7/29 16:04
     */
    String getGroupKey();

    /**
     * 新增前的通知
     *
     * @param data 对象数据
     * <AUTHOR>
     * @date 2025/8/5 09:19
     */
    default void beforeSave(JSONObject data){};

    /**
     * 新增后的通知
     *
     * @param baseId 业务基础表数据id
     * @param dataId 对象数据id
     * @param data 对象数据
     * <AUTHOR>
     * @date 2025/7/29 16:05
     */
    default void afterSave(Long baseId, Long dataId, JSONObject data){};

    /**
     * 修改前的通知
     *
     * @param baseId 业务基础表数据id
     * @param dataId 对象数据id
     * @param data 对象数据
     * <AUTHOR>
     * @date 2025/8/5 09:21
     */
    default void beforeUpdate(Long baseId, Long dataId, JSONObject data){};

    /**
     * 删除后的通知
     *
     * @param baseId 业务基础表数据id
     * @param dataId 对象数据id
     * @param data 对象数据
     * <AUTHOR>
     * @date 2025/7/29 16:05
     */
    default void afterUpdate(Long baseId, Long dataId, JSONObject data){};

    /**
     * 删除前的通知
     *
     * @param baseId 业务基础表数据id
     * @param dataId 删除对象数据id
     * @param data
     * <AUTHOR>
     * @date 2025/8/5 09:21
     */
    default void beforeDelete(Long baseId, Long dataId, JSONObject data){};

    /**
     * 删除后的通知
     *
     * @param baseId 业务基础表数据id
     * @param dataId 对象数据id
     * @param dto 删除对象数据  入参
     * <AUTHOR>
     * @date 2025/7/29 16:05
     */
    default void afterDelete(Long baseId, Long dataId, JSONObject dto){};

    /**
     * 查询前的通知
     *
     * @param baseTableAlias 业务基础表别名
     * @param dataTableAlias 对象数据表别名
     * @param objectInfo 对象信息
     * @return String 返回 权限 SQL 片段
     * <AUTHOR>
     * @date 2025/8/5 09:21
     */
    default String beforeQuery(String baseTableAlias, String dataTableAlias, ObjectInfoVo objectInfo){
        return "";
    };

    /**
     * 修改后的通知
     *
     * @param list  查询结果
     * @param objectInfo 对象信息
     * <AUTHOR>
     * @date 2025/7/29 16:05
     */
    default void afterQuery(ObjectInfoVo objectInfo, List<Map<String, Object>> list){};

    /**
     * 详情查询后的后置
     *
     * @param objectInfoId
     * @param objectDataId
     * @param data
     * @return void
     * <AUTHOR>
     * @date 2025/8/11 17:44
     */
    default void afterInfo(Long objectInfoId,Long objectDataId,Map<String, Object> data){};
}

package com.wbyy.biz.common.form.modules.objectform.service.impl;

import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.biz.common.form.modules.objectform.dao.IObjectFormWidgetDao;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormWidgetPO;
import com.wbyy.biz.common.form.modules.objectform.service.IObjectFormWidgetService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 对象表单组件信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectFormWidgetServiceImpl implements IObjectFormWidgetService {
    private final IObjectFormWidgetDao objectFormWidgetDao;

    @Override
    public ObjectFormWidgetPO selectById(Long id) {

        return objectFormWidgetDao.selectById(id);
    }

    @Override
    public List<ObjectFormWidgetPO> selectList(ObjectFormWidgetPO po) {

        return objectFormWidgetDao.selectList(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ObjectFormWidgetPO po) {

        return objectFormWidgetDao.insert(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ObjectFormWidgetPO po) {

        return objectFormWidgetDao.update(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {

        return objectFormWidgetDao.deleteByIds(ids);
    }
}

package com.wbyy.biz.common.form.modules.objectattribute.dao;

import com.wbyy.biz.common.drag.service.IBaseVxeService;
import com.wbyy.biz.common.form.modules.objectattribute.domain.ObjectAttributeInfoPO;
import com.wbyy.biz.common.form.modules.objectattribute.domain.vo.ObjectAttributeInfoVo;

import java.util.List;

/**
 * 对象属性信息Dao接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IObjectAttributeInfoDao extends IBaseVxeService<ObjectAttributeInfoPO> {
    /**
     * 查询对象属性信息
     *
     * @param id 对象属性信息主键
     * @return 对象属性信息
     */
    ObjectAttributeInfoPO selectById(Long id);

    /**
     * 查询对象属性信息列表
     *
     * @param po 对象属性信息
     * @return 对象属性信息集合
     */
    List<ObjectAttributeInfoPO> selectList(ObjectAttributeInfoPO po);

    /**
     * 根据 po 中不为null的字段 查询对象属性信息列表
     *
     * @param po 对象属性信息
     * @return 对象属性信息列表
     */
    List<ObjectAttributeInfoPO> findByPO(ObjectAttributeInfoPO po);

    /**
     * 修改对象属性信息
     *
     * @param po 对象属性信息
     * @return 结果
     */
    boolean update(ObjectAttributeInfoPO po);

    /**
     * 批量删除对象属性信息
     *
     * @param ids 需要删除的对象属性信息主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);


    /**
     * 添加表字段
     *
     * @param sql sql片段
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/16 13:32
     */
    boolean alterColumn(String sql);

    /**
     * 根据表名，表字段， 检查字段是否有数据
     *
     * @param tableName 表名
     * @param fieldName 字段名
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/17 08:35
     */
    boolean checkTableHasValue(String tableName, String fieldName);


    /**
     * 删除表字段
     *
     * @param tableName 表名
     * @param fieldName 字段名
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/17 08:45
     */
    boolean deleteColumn(String tableName, String fieldName);

    /**
     * 根据 对象ID 删除对象字段
     *
     * @param objectId 对象ID
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/17 11:57
     */
    boolean deleteByObjectInfoId(Long objectId);

    List<ObjectAttributeInfoVo> getSimpleByObjectInfoId(Long objectInfoId);
}

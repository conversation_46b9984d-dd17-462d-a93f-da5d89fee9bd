package com.wbyy.biz.common.form.annotation;

import com.wbyy.biz.common.preference.modules.setting.domain.SearchWidgetTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 系统对象字段信息注解
 * 用于描述系统类字段的元信息，支持自动转换为ObjectAttributeInfoPO
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SystemPOFieldInfo {
    
    /**
     * 字段名称（中文描述）
     */
    String name() default "";
    
    /**
     * 字段属性（英文属性名）
     */
    String property() default "";
    
    /**
     * 字段类型
     * 支持：varchar, int, bigint, decimal, date, datetime, text, longtext
     */
    String fieldType() default "varchar";
    
    /**
     * 字段长度
     * 对于varchar类型必须指定，decimal类型表示总位数
     */
    int length() default 100;
    
    /**
     * 字段小数位长度
     * 仅对decimal类型有效
     */
    int decimalLength() default 0;
    
    /**
     * 单位
     */
    String unit() default "";
    
    /**
     * 英文单位
     */
    String unitEn() default "";
    
    /**
     * 默认值
     */
    String defaultValue() default "";
    
    /**
     * 是否支持查询
     */
    boolean searchSupport() default true;
    
    /**
     * 是否查询默认显示
     */
    boolean searchDefault() default true;
    
    /**
     * 字段查询控件类型
     */
    SearchWidgetTypeEnum searchWidgetType() default SearchWidgetTypeEnum.INPUT;
    
    /**
     * 是否支持表头
     */
    boolean tableHeaderSupport() default true;
    
    /**
     * 是否表头默认显示
     */
    boolean tableHeaderDefault() default true;
    
    /**
     * 表头宽度
     */
    int tableHeaderWidth() default 120;
    
    /**
     * 是否左侧固定表头
     */
    boolean tableHeaderLeftFixed() default false;
    
    /**
     * 是否可配
     */
    boolean tableHeaderSupportConfig() default true;

    /**
     * 是否是表字段
     */
    boolean tableFieldExist() default true;
}

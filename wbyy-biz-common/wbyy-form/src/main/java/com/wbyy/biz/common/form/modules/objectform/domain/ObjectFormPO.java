package com.wbyy.biz.common.form.modules.objectform.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;
import java.util.List;

/**
 * 对象表单对象 object_form
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "object_form", autoResultMap = true)
@Schema(description = "对象表单实体类")
@EqualsAndHashCode(callSuper = true)
public class ObjectFormPO extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 对象信息表id */
    @Excel(name = "对象信息表id")
    @Schema(description = "对象信息表id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "对象信息表id不能为空")
    private Long objectInfoId;

    /** 表单名称 */
    @Excel(name = "表单名称")
    @Schema(description = "表单名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "表单名称不能为空")
    @Size(max = 100, message = "表单名称不能超过 100 个字符")
    private String formName;

    /** 表单类型:新建、编辑/详情.... */
    @Excel(name = "表单类型:新建、编辑/详情....")
    @Schema(description = "表单类型:新建、编辑/详情....", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "表单类型:新建、编辑/详情....不能为空")
    @Size(max = 32, message = "表单类型:新建、编辑/详情....不能超过 32 个字符")
    private String formType;

    /** 表单配置，保存表单配置的json */
    @Excel(name = "表单配置，保存表单配置的json")
    @Schema(description = "表单配置，保存表单配置的json")
    private String formConfig;

    /** 禁用=1,启用=0 */
    @Excel(name = "禁用=1,启用=0")
    @Schema(description = "状态:禁用=1,启用=0", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer statusFlag;

    /** 是=0,否=1 */
    @Excel(name = "是=0,否=1")
    @Schema(description = "默认:是=0,否=1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer defaultFlag;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;


    /**
     * 表单组件
     */
    @Schema(description = "表单组件配置")
    @TableField(exist = false)
    private List<ObjectFormWidgetPO> widgets;

    /**
     * 清空审计信息
     *
     * @param
     * @return void
     * <AUTHOR>
     * @date 2025/7/12 10:39
     */
    public void clearAudit(){
        this.id=null;
        this.setCreateBy(null);
        this.setCreateNameBy(null);
        this.setCreateTime(null);
        this.setUpdateBy(null);
        this.setUpdateTime(null);
        this.setUpdateNameBy(null);
    }


}

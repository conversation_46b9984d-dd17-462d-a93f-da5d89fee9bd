package com.wbyy.biz.common.form.modules.objectattribute.dto;


import com.wbyy.biz.common.preference.modules.setting.domain.SearchWidgetTypeEnum;
import com.wbyy.biz.common.preference.modules.setting.domain.TableHeaderAlignEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/8/4 17:22
 */
@Data
public class ObjectAttributeInfoDTO {

    private Long objectInfoId;

    private String name;

    private String propertyName;

    private String keyValue;

    private String disabled;

    private String fieldType;

    private Integer fieldLength;

    private Integer fieldDecimalLength;

    private String defaultValue;

    private String unit;

    private String unitEn;

    private Integer delFlag;

    private Boolean systemBuildInFlag;

    private Boolean searchSupportFlag;

    private Boolean searchDefaultFlag;

    private SearchWidgetTypeEnum searchWidgetType;

    private Long searchDataSourceId;

    private Boolean tableHeaderSupportFlag;

    private Boolean tableHeaderLeftFixedFlag;

    private Boolean tableHeaderDefaultFlag;

    private Boolean tableHeaderSupportConfigFlag;

    private Integer tableHeaderWidth;

    private TableHeaderAlignEnum tableHeaderAlign;
}
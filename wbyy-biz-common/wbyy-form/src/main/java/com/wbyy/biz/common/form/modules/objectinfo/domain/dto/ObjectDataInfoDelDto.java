package com.wbyy.biz.common.form.modules.objectinfo.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/29 17:06
 */
@Data
@Schema(description = "对象数据删除入参")
public class ObjectDataInfoDelDto {
    @Schema(description = "对象id")
    @NotNull(message = "对象id不得为空")
    private Long objectInfoId;

    @Schema(description = "数据id")
    @NotNull(message = "数据id不得为空")
    private Long id;

    @Schema(description = "删除原因")
    private String reason;
}

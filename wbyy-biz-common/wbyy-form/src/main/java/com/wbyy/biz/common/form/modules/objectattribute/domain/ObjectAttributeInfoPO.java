package com.wbyy.biz.common.form.modules.objectattribute.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.biz.common.drag.domain.BaseVxeEntity;
import com.wbyy.biz.common.preference.modules.setting.domain.SearchWidgetTypeEnum;
import com.wbyy.biz.common.preference.modules.setting.domain.TableHeaderAlignEnum;
import com.wbyy.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * 对象属性信息对象 object_attribute_info
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "object_attribute_info", autoResultMap = true)
@Schema(description = "对象属性信息实体类")
@EqualsAndHashCode(callSuper = true)
public class ObjectAttributeInfoPO extends BaseVxeEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
//    @Schema(description = "主键id（新增：不传；修改：必传）")
//    private Long id;


    /**
     * 对象ID
     */
    @Excel(name = "对象ID")
    @Schema(description = "对象ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "对象ID不能为空")
    private Long objectInfoId;

    /**
     * 字段名称
     */
    @Excel(name = "字段名称")
    @Schema(description = "字段名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 30, message = "字段名称不能超过 30 个字符")
    @NotNull(message = "字段名称不能为空")
    private String name;

    /**
     * 英文名称
     */
    @Excel(name = "英文名称")
    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 100, message = "英文名称不能超过 100 个字符")
    @NotNull(message = "英文名称不能为空")
    private String propertyName;

    /**
     * 字段键值
     */
    @Excel(name = "字段键值")
    @Schema(description = "字段键值")
    @Size(max = 100, message = "字段键值不能超过 100 个字符")
    @Pattern(regexp = "^[a-z][a-z0-9]*(_[a-z0-9]+)*$", message = "必须使用小写字母、数字、下划线 ，禁止出现数字开头，禁止两个下划线中间只出现数字")
    private String keyValue;

    /**
     * 禁用
     */
    @Excel(name = "禁用")
    @Schema(description = "禁用")
    @Size(max = 4, message = "禁用不能超过 4 个字符")
    private String disabled;

    /**
     * 字段类型(字典)
     */
    @Excel(name = "字段类型(字典)")
    @Schema(description = "字段类型(字典)")
    @Size(max = 30, message = "字段类型(字典)不能超过 30 个字符")
    private String fieldType;

    /**
     * 字段长度
     */
    @Excel(name = "字段长度")
    @Schema(description = "字段长度")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer fieldLength;

    /**
     * 字段小数位长度
     */
    @Excel(name = "字段小数位长度")
    @Schema(description = "字段小数位长度")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer fieldDecimalLength;

    /** 序号 */
//    @Excel(name = "序号")
//    @Schema(description = "序号")
//    private Integer sort;

    /**
     * 是否通用字段，通用字段单独管理了，该字段不需要了
     */
    @Deprecated
    @Excel(name = "是否不通用字段")
    @Schema(description = "是否不通用字段")
    private String unCommon;

    /**
     * 默认值
     */
    @Excel(name = "默认值")
    @Schema(description = "默认值")
    @Size(max = 100, message = "默认值不能超过 100 个字符")
    private String defaultValue;

    /**
     * 单位
     */
    @Excel(name = "单位")
    @Schema(description = "单位")
    @Size(max = 30, message = "单位不能超过 30 个字符")
    private String unit;

    /**
     * 英文单位
     */
    @Excel(name = "英文单位")
    @Schema(description = "英文单位")
    @Size(max = 30, message = "英文单位不能超过 30 个字符")
    private String unitEn;

    @TableField(exist = false)
    private Long neId;
    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;

    @NotNull(message = "对象内置字段标识不能为空")
    @Schema(description = "是否对象内置字段")
    private Boolean systemBuildInFlag = Boolean.FALSE;

    // 查询设置相关的字段======

    @Schema(description = "是否支持查询")
    @NotNull(message = "是否支持查询不能为空")
    private Boolean searchSupportFlag = Boolean.FALSE;

    @Schema(description = "是否查询默认显示")
    Boolean searchDefaultFlag = Boolean.FALSE;

    @Schema(description = "字段查询控件类型")
    private SearchWidgetTypeEnum searchWidgetType;

    @Schema(description = "数据来源")
    private Long searchDataSourceId;

    // 表头设置相关的字段======

    @Schema(description = "是否支持表头")
    @NotNull(message = "是否表头不能为空")
    private Boolean tableHeaderSupportFlag = Boolean.FALSE;

    @Schema(description = "是否左侧固定表头")
    private Boolean tableHeaderLeftFixedFlag = Boolean.FALSE;

    @Schema(description = "是否表头默认显示")
    private Boolean tableHeaderDefaultFlag = Boolean.FALSE;

    @Schema(description = "是否可配")
    private Boolean tableHeaderSupportConfigFlag = Boolean.TRUE;

    @Schema(description = "是否是表字段")
    private Boolean tableFieldExistFlag = Boolean.TRUE;

    @Schema(description = "表头宽度")
    private Integer tableHeaderWidth;

    @Schema(description = "表头对齐方式")
    private TableHeaderAlignEnum tableHeaderAlign = TableHeaderAlignEnum.LEFT;
}

package com.wbyy.biz.common.form.modules.objectinfo.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/29 17:06
 */
@Data
public class ObjectDataInfoDto {
    @Schema(description = "对象id")
    @NotNull(message = "对象id不得为空")
    private Long objectInfoId;

    @Schema(description = "数据id")
    @NotNull(message = "数据id不得为空")
    private Long id;
}

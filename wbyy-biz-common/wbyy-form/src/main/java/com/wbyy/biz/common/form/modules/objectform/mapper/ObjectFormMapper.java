package com.wbyy.biz.common.form.modules.objectform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormPO;
import org.apache.ibatis.annotations.Param;

/**
 * 对象表单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface ObjectFormMapper extends BaseMapper<ObjectFormPO> {

    Long selectIdByObject(@Param("objectInfoId") Long objectInfoId,@Param("formType") String formType);
}

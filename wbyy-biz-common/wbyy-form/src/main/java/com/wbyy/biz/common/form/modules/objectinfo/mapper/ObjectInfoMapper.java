package com.wbyy.biz.common.form.modules.objectinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.wbyy.biz.common.form.modules.objectinfo.domain.ObjectInfoPO;
import com.wbyy.biz.common.form.modules.objectinfo.domain.vo.ObjectInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 对象信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface ObjectInfoMapper extends MPJBaseMapper<ObjectInfoPO> {

    /**
     * 创建表
     * @param params
     * @return
     */
    boolean createdTable(Map<String, Object> params);

    /**
     * 判断对象信息表是否存在数据
     *
     * @param tableName 对象KEY
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/16 10:48
     */
    boolean hasData(String tableName);

    /**
     * 删除表
     * @param tableName
     * @return
     */
    boolean dropTable(String tableName);

    ObjectInfoVo selectSimpleById(@Param("id") Long id);

}

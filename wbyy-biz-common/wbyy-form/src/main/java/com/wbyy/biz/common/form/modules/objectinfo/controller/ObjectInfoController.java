package com.wbyy.biz.common.form.modules.objectinfo.controller;

import com.wbyy.biz.common.form.modules.objectinfo.domain.ObjectInfoPO;
import com.wbyy.biz.common.form.modules.objectinfo.service.IObjectInfoService;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 对象信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "对象信息")
@RequestMapping("/object/info")
public class ObjectInfoController extends BaseController {

    private final IObjectInfoService objectInfoService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("objectinfo:info:list")
    @GetMapping("/page")
    public TableDataInfo<ObjectInfoPO> page(@ParameterObject ObjectInfoPO po) {
        startPage();
        List<ObjectInfoPO> list = objectInfoService.selectList(po);
        return getDataTable(list);
    }

    @Operation(summary = "导出列表")
    @RequiresPermissions("objectinfo:info:export")
    @Log(title = "对象信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ObjectInfoPO po) {
        List<ObjectInfoPO> list = objectInfoService.selectList(po);
        ExcelUtil<ObjectInfoPO> util = new ExcelUtil<ObjectInfoPO>(ObjectInfoPO.class);
        util.exportExcel(response, list, "对象信息数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("objectinfo:info:query")
    @GetMapping(value = "/{id}")
    public R<ObjectInfoPO> getInfo(@PathVariable("id") Long id) {
        return R.ok(objectInfoService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("objectinfo:info:add")
    @Log(title = "对象信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated ObjectInfoPO po) {
        return R.ok(objectInfoService.insert(po));
    }

    @Operation(summary = "发布")
    @RequiresPermissions("objectinfo:info:publish")
    @Log(title = "对象信息")
    @PostMapping("/publish")
    public R<Boolean> publish(@RequestBody ObjectInfoPO po) {
        return R.ok(objectInfoService.publish(po));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("objectinfo:info:edit")
    @Log(title = "对象信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated ObjectInfoPO po) {
        return R.ok(objectInfoService.update(po));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("objectinfo:info:remove")
    @Log(title = "对象信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        return R.ok(objectInfoService.deleteByIds(ids));
    }
}

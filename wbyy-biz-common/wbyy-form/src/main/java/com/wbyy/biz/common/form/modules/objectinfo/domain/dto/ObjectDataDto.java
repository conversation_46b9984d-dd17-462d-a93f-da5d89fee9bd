package com.wbyy.biz.common.form.modules.objectinfo.domain.dto;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/29 13:52
 */
@Data
public class ObjectDataDto {

    @Schema(description = "对象id")
    @NotNull(message = "对象id不得为空")
    private Long objectInfoId;

    @Schema(description = "对象数据")
    @NotNull(message = "对象数据不得为空")
    private JSONObject data;
}

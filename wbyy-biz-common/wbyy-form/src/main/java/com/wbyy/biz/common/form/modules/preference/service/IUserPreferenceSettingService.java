package com.wbyy.biz.common.form.modules.preference.service;

import com.wbyy.biz.common.form.modules.objectattribute.domain.ObjectAttributeInfoPO;
import com.wbyy.biz.common.preference.modules.setting.domain.vo.SearchAttributeInfoVO;
import com.wbyy.biz.common.preference.modules.setting.domain.vo.TableHeaderAttributeInfoVO;
import com.wbyy.biz.common.preference.modules.setting.domain.query.UserPreferenceSettingQuery;

import java.util.List;

/**
 * 用户偏好设置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IUserPreferenceSettingService {

    /**
     * 获取可选表头字段列表
     *
     * @param query 用户偏好设置查询条件
     * @return 字段列表
     */
    List<TableHeaderAttributeInfoVO> getTableHeaderOptionalFields(UserPreferenceSettingQuery query);


    /**
     * 获取已选表头字段列表
     *
     * @param query 用户偏好设置查询条件
     * @return 字段列表
     */
    List<TableHeaderAttributeInfoVO> getTableHeaderSelectedFields(UserPreferenceSettingQuery query);

    /**
     * 获取可选筛选字段列表
     *
     * @param query 用户偏好设置查询条件
     * @return 字段列表
     */
    List<SearchAttributeInfoVO> getSearchOptionalFields(UserPreferenceSettingQuery query);

    /**
     * 获取已选筛选字段列表
     *
     * @param query 用户偏好设置查询条件
     * @return 字段列表
     */
    List<SearchAttributeInfoVO> getSearchSelectedFields(UserPreferenceSettingQuery query);


    /**
     * 获取已选表头字段属性信息列表
     *
     * @param query 用户偏好设置查询条件
     * @return 属性信息列表
     */
    List<ObjectAttributeInfoPO> getTableHeaderSelectedAttributeInfos(UserPreferenceSettingQuery query);
}

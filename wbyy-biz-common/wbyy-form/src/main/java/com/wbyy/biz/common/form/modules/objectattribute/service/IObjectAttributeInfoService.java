package com.wbyy.biz.common.form.modules.objectattribute.service;

import com.wbyy.biz.common.form.modules.objectattribute.domain.ObjectAttributeInfoPO;
import com.wbyy.biz.common.form.modules.objectattribute.dto.AddBatchDTO;
import com.wbyy.biz.common.form.modules.objectattribute.dto.ObjectAttributeInfoDTO;

import java.util.List;

/**
 * 对象属性信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IObjectAttributeInfoService {
    /**
     * 查询对象属性信息
     *
     * @param id 对象属性信息主键
     * @return 对象属性信息
     */
    ObjectAttributeInfoPO selectById(Long id);

    /**
     * 查询对象属性信息列表
     *
     * @param dto 对象属性信息
     * @return 对象属性信息集合
     */
    List<ObjectAttributeInfoPO> selectList(ObjectAttributeInfoDTO dto);

    /**
     * 新增对象属性信息
     *
     * @param po 对象属性信息
     * @return 结果
     */
    boolean insert(ObjectAttributeInfoPO po);

    /**
     * 新增系统内置字段
     *
     * @param attributeList 字段列表
     * @return void
     */
    void insertSystemBuildInField(Long objectInfoId, List<ObjectAttributeInfoPO> attributeList);

    /**
     * 批量新增
     *
     * @param dto 入参
     * @return Boolean
     * <AUTHOR>
     * @date 2025/7/17 09:31
     */
    String addBatch(AddBatchDTO dto);

    /**
     * 修改对象属性信息
     *
     * @param po 对象属性信息
     * @return 结果
     */
    boolean update(ObjectAttributeInfoPO po);

    /**
     * 批量删除对象属性信息
     *
     * @param ids 需要删除的对象属性信息主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 拖拽
     *
     * @param entity
     * @return
     */
    ObjectAttributeInfoPO doDrag(ObjectAttributeInfoPO entity);


}

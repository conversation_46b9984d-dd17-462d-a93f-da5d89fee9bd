package com.wbyy.biz.common.form.modules.baseobjectdata.controller;

import io.swagger.v3.oas.annotations.tags.Tag;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wbyy.biz.common.form.modules.baseobjectdata.service.IBaseObjectDataService;
import com.wbyy.common.core.web.controller.BaseController;

/**
 * base业务数据与对象数据Id关联Controller
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "base业务数据与对象数据Id关联")
@RequestMapping("/base/object/data")
public class BaseObjectDataController extends BaseController {

    private final IBaseObjectDataService baseObjectDataService;

//    @Operation(summary = "分页列表")
//    @GetMapping("/page")
//    public TableDataInfo<BaseObjectDataPO> page(@ParameterObject BaseObjectDataPO po) {
//        startPage();
//        List<BaseObjectDataPO> list = baseObjectDataService.selectList(po);
//        return getDataTable(list);
//    }
//
//    @Operation(summary = "导出列表")
//    @Log(title = "base业务数据与对象数据Id关联", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, BaseObjectDataPO po) {
//        List<BaseObjectDataPO> list = baseObjectDataService.selectList(po);
//        ExcelUtil<BaseObjectDataPO> util = new ExcelUtil<BaseObjectDataPO>(BaseObjectDataPO.class);
//        util.exportExcel(response, list, "base业务数据与对象数据Id关联数据");
//    }
//
//    @Operation(summary = "获取详细信息")
//    @GetMapping(value = "/{id}")
//    public R<BaseObjectDataPO> getInfo(@PathVariable("id") Long id) {
//        return R.ok(baseObjectDataService.selectById(id));
//    }
//
//    @Operation(summary = "新增")
//    @Log(title = "base业务数据与对象数据Id关联", businessType = BusinessType.INSERT)
//    @PostMapping
//    public R<Boolean> add(@RequestBody @Validated BaseObjectDataPO po) {
//        return R.ok(baseObjectDataService.insert(po));
//    }
//
//    @Operation(summary = "修改")
//    @Log(title = "base业务数据与对象数据Id关联", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public R<Boolean> edit(@RequestBody @Validated BaseObjectDataPO po) {
//        return R.ok(baseObjectDataService.update(po));
//    }
//
//    @Operation(summary = "删除")
//    @Log(title = "base业务数据与对象数据Id关联", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Boolean> remove(@PathVariable Long[] ids) {
//        return R.ok(baseObjectDataService.deleteByIds(ids));
//    }
}

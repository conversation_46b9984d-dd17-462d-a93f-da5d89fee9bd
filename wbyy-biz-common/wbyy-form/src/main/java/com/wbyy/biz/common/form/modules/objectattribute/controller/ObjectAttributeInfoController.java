package com.wbyy.biz.common.form.modules.objectattribute.controller;

import com.wbyy.biz.common.form.modules.objectattribute.DragDTO;
import com.wbyy.biz.common.form.modules.objectattribute.domain.ObjectAttributeInfoPO;
import com.wbyy.biz.common.form.modules.objectattribute.dto.AddBatchDTO;
import com.wbyy.biz.common.form.modules.objectattribute.dto.ObjectAttributeInfoDTO;
import com.wbyy.biz.common.form.modules.objectattribute.service.IObjectAttributeInfoService;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.bean.BeanUtils;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.redis.annotation.RedisLock;
import com.wbyy.common.security.annotation.RequiresPermissions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.wbyy.biz.common.form.common.constant.RedisConstants.LOCK_DRAG;

/**
 * 对象属性信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "对象属性信息")
@RequestMapping("/object/attribute/info")
public class ObjectAttributeInfoController extends BaseController {

    private final IObjectAttributeInfoService objectAttributeInfoService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("objectattribute:attribute:list")
    @GetMapping("/list")
    public R<List<ObjectAttributeInfoPO>> list(@ParameterObject ObjectAttributeInfoDTO dto) {
        List<ObjectAttributeInfoPO> list = objectAttributeInfoService.selectList(dto);
        return R.ok(list);
    }

//    @Operation(summary = "导出列表")
//    @RequiresPermissions("objectattribute:attribute:export")
//    @Log(title = "对象属性信息", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ObjectAttributeInfoPO po) {
//        List<ObjectAttributeInfoPO> list = objectAttributeInfoService.selectList(po);
//        ExcelUtil<ObjectAttributeInfoPO> util = new ExcelUtil<ObjectAttributeInfoPO>(ObjectAttributeInfoPO.class);
//        util.exportExcel(response, list, "对象属性信息数据");
//    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("objectattribute:attribute:query")
    @GetMapping(value = "/{id}")
    public R<ObjectAttributeInfoPO> getInfo(@PathVariable("id") Long id) {
        return R.ok(objectAttributeInfoService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("objectattribute:attribute:add")
    @Log(title = "对象属性信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated ObjectAttributeInfoPO po) {
        return R.ok(objectAttributeInfoService.insert(po));
    }

    @Operation(summary = "批量新增")
    @RequiresPermissions("objectattribute:attribute:add")
    @Log(title = "对象属性信息", businessType = BusinessType.INSERT)
    @PostMapping("add-batch")
    public R<String> addBatch(@RequestBody @Validated AddBatchDTO dto) {
        return R.ok(objectAttributeInfoService.addBatch(dto));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("objectattribute:attribute:edit")
    @Log(title = "对象属性信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated ObjectAttributeInfoPO po) {
        return R.ok(objectAttributeInfoService.update(po));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("objectattribute:attribute:remove")
    @Log(title = "对象属性信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        return R.ok(objectAttributeInfoService.deleteByIds(ids));
    }


    @Operation(summary = "拖拽")
    @Log(title = "对象属性", businessType = BusinessType.DRAG)
    @PostMapping("/drag")
    @RedisLock(prefix = LOCK_DRAG, key = "#dto.objectInfoId")
    public R<ObjectAttributeInfoPO> drag(@RequestBody DragDTO dto) {
        ObjectAttributeInfoPO entity = new ObjectAttributeInfoPO();
        BeanUtils.copyProperties(dto, entity);
        return R.ok( objectAttributeInfoService.doDrag(entity));
    }

}

package com.wbyy.biz.common.form.modules.objectinfo.controller;

import com.wbyy.biz.common.form.modules.objectinfo.domain.dto.ObjectDataSaveDto;
import com.wbyy.biz.common.form.modules.objectinfo.domain.dto.ObjectDataUpdateDto;
import com.wbyy.biz.common.form.modules.objectinfo.domain.dto.ObjectDataInfoDelDto;
import com.wbyy.biz.common.form.modules.objectinfo.domain.dto.ObjectDataInfoDto;
import com.wbyy.biz.common.form.modules.objectinfo.domain.vo.ObjectDataPostVo;
import com.wbyy.biz.common.form.modules.objectinfo.domain.vo.ObjectDataVo;
import com.wbyy.biz.common.form.modules.objectinfo.service.IObjectDataService;
import com.wbyy.biz.common.preference.modules.setting.domain.query.CustomSearchQuery;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/29 13:36
 */
@Tag(name = "对象数据操作")
@RestController
@RequestMapping("/object/data")
@RequiredArgsConstructor
public class ObjectDataController extends BaseController {

    private final IObjectDataService objectDataService;

    @Operation(summary = "对象数据新增")
    @PostMapping("/save")
    public R<ObjectDataPostVo> save(@RequestBody @Validated ObjectDataSaveDto dto){
        return R.ok(objectDataService.save(dto));
    }

    @Operation(summary = "对象数据修改")
    @PostMapping("/update")
    public R<ObjectDataPostVo> update(@RequestBody @Validated ObjectDataUpdateDto dto){
        return R.ok(objectDataService.update(dto));
    }

    @Operation(summary = "对象数据查询")
    @GetMapping("/get-one")
    public R<ObjectDataVo> getOne(@ParameterObject @Validated ObjectDataInfoDto dto){
        return R.ok(objectDataService.info(dto));
    }

    @Operation(summary = "对象数据删除")
    @DeleteMapping("/remove")
    public R<Boolean> remove(@RequestBody @Validated ObjectDataInfoDelDto dto) {
        return R.ok(objectDataService.removeByDto(dto));
    }
    @Operation(summary = "查询列表（分页）")
    @PostMapping("/page")
    public TableDataInfo<Map<String, Object>> page(@RequestBody CustomSearchQuery query) {
        List<Map<String, Object>> list = objectDataService.selectList(query);
        return getDataTable(list);
    }
}

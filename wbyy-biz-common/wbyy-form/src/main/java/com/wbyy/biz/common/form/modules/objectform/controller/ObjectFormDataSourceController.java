package com.wbyy.biz.common.form.modules.objectform.controller;

import cn.hutool.core.collection.CollectionUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormDataSourcePO;
import com.wbyy.biz.common.form.modules.objectform.service.IObjectFormDataSourceService;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.page.TableDataInfo;

/**
 * 对象单数据源配置Controller
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "对象单数据源配置")
@RequestMapping("/object/form/data/source")
public class ObjectFormDataSourceController extends BaseController {

    private final IObjectFormDataSourceService objectFormDataSourceService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("objectform:data_source:list")
    @GetMapping("/page")
    public TableDataInfo<ObjectFormDataSourcePO> page(@ParameterObject ObjectFormDataSourcePO po) {
        startPage();
        List<ObjectFormDataSourcePO> list = objectFormDataSourceService.selectList(po);
        return getDataTable(list);
    }

    @Operation(summary = "不分页列表")
    @RequiresPermissions("objectform:data_source:list")
    @GetMapping("/list")
    public R<List<ObjectFormDataSourcePO>> list(@ParameterObject ObjectFormDataSourcePO po) {
        List<ObjectFormDataSourcePO> list = objectFormDataSourceService.selectList(po);
        return R.ok(list);
    }

    @Operation(summary = "导出列表")
    @RequiresPermissions("objectform:data_source:export")
    @Log(title = "对象单数据源配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ObjectFormDataSourcePO po) {
        List<ObjectFormDataSourcePO> list = objectFormDataSourceService.selectList(po);
        ExcelUtil<ObjectFormDataSourcePO> util = new ExcelUtil<ObjectFormDataSourcePO>(ObjectFormDataSourcePO.class);
        util.exportExcel(response, list, "对象单数据源配置数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("objectform:data_source:query")
    @GetMapping(value = "/{id}")
    public R<ObjectFormDataSourcePO> getInfo(@PathVariable("id") Long id) {
        return R.ok(objectFormDataSourceService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("objectform:data_source:add")
    @Log(title = "对象单数据源配置", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated ObjectFormDataSourcePO po) {
        return R.ok(objectFormDataSourceService.insert(po));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("objectform:data_source:edit")
    @Log(title = "对象单数据源配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated ObjectFormDataSourcePO po) {
        return R.ok(objectFormDataSourceService.update(po));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("objectform:data_source:remove")
    @Log(title = "对象单数据源配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configId}")
    public R<Boolean> remove(@PathVariable String configId) {
        objectFormDataSourceService.deleteByConfigId(configId);
        return R.ok(true);
    }
}

package com.wbyy.biz.common.form.modules.baseobjectdata.dao;

import java.util.List;
import com.wbyy.biz.common.form.modules.baseobjectdata.domain.BaseObjectDataPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.system.api.model.LoginUser;

/**
 * base业务数据与对象数据Id关联Dao接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IBaseObjectDataDao extends IService<BaseObjectDataPO> {
    /**
     * 查询base业务数据与对象数据Id关联
     *
     * @param id base业务数据与对象数据Id关联主键
     * @return base业务数据与对象数据Id关联
     */
    BaseObjectDataPO selectById(Long id);

    /**
     * 查询base业务数据与对象数据Id关联列表
     *
     * @param po base业务数据与对象数据Id关联
     * @return base业务数据与对象数据Id关联集合
     */
    List<BaseObjectDataPO> selectList(BaseObjectDataPO po);

    /**
     * 根据 po 中不为null的字段 查询base业务数据与对象数据Id关联列表
     *
     * @param po base业务数据与对象数据Id关联
     * @return base业务数据与对象数据Id关联列表
     */
    List<BaseObjectDataPO> findByPO(BaseObjectDataPO po);

    /**
     * 新增base业务数据与对象数据Id关联
     *
     * @param po base业务数据与对象数据Id关联
     * @return 结果
     */
    boolean insert(BaseObjectDataPO po);

    /**
     * 修改base业务数据与对象数据Id关联
     *
     * @param po base业务数据与对象数据Id关联
     * @return 结果
     */
    boolean update(BaseObjectDataPO po);

    /**
     * 批量删除base业务数据与对象数据Id关联
     *
     * @param ids 需要删除的base业务数据与对象数据Id关联主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 根据baseId获取base与对象数据的关联信息
     *
     * @param baseId
     * @return List<BaseObjectDataPO>
     * <AUTHOR>
     * @date 2025/7/30 15:34
     */
    List<BaseObjectDataPO> listByBaseId(Long baseId);

    /**
     * 根据数据id获取
     *
     * @param dataId
     * @return Long
     * <AUTHOR>
     * @date 2025/8/5 9:42
     */
    BaseObjectDataPO getByDataId(Long dataId);

    /**
     * 根据baseid获取
     *
     * @param baseId
     * @return BaseObjectDataPO
     * <AUTHOR>
     * @date 2025/8/6 14:13
     */
    BaseObjectDataPO getByBaseId(Long baseId);


}

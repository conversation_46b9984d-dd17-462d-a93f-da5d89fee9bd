package com.wbyy.biz.common.form.modules.objectinfo.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.wbyy.biz.common.form.common.query.QueryField;
import com.wbyy.biz.common.form.modules.baseobjectdata.dao.IBaseObjectDataDao;
import com.wbyy.biz.common.form.modules.baseobjectdata.domain.BaseObjectDataPO;
import com.wbyy.biz.common.form.modules.objectattribute.dao.IObjectAttributeInfoDao;
import com.wbyy.biz.common.form.modules.objectattribute.domain.ObjectAttributeInfoPO;
import com.wbyy.biz.common.form.modules.objectattribute.domain.vo.ObjectAttributeInfoVo;
import com.wbyy.biz.common.form.modules.objectgroup.domain.ObjectGroupPO;
import com.wbyy.biz.common.form.modules.objectinfo.dao.IObjectInfoDao;
import com.wbyy.biz.common.form.modules.objectinfo.domain.dto.*;
import com.wbyy.biz.common.form.modules.objectinfo.domain.vo.ObjectDataPostVo;
import com.wbyy.biz.common.form.modules.objectinfo.domain.vo.ObjectDataVo;
import com.wbyy.biz.common.form.modules.objectinfo.domain.vo.ObjectInfoVo;
import com.wbyy.biz.common.form.modules.objectinfo.mapper.ObjectDataMapper;
import com.wbyy.biz.common.form.modules.objectinfo.service.IObjectDataRelationService;
import com.wbyy.biz.common.form.modules.objectinfo.service.IObjectDataService;
import com.wbyy.biz.common.preference.modules.setting.dao.IUserPreferenceSettingDao;
import com.wbyy.biz.common.preference.modules.setting.domain.PreferenceTypeEnum;
import com.wbyy.biz.common.preference.modules.setting.domain.UserPreferenceSettingPO;
import com.wbyy.biz.common.preference.modules.setting.domain.query.CustomSearchQuery;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wbyy.common.core.constant.Constants.ENABLE_STATUS;
import static com.wbyy.common.core.constant.Constants.NO_DELETE;
import static com.wbyy.common.core.utils.PageUtils.startPage;

/**
 * <AUTHOR>
 * @date 2025/7/29 13:38
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectDataServiceImpl implements IObjectDataService {

    private final IBaseObjectDataDao baseObjectDataDao;
    private final IObjectInfoDao objectInfoDao;
    private final IObjectAttributeInfoDao objectAttributeInfoDao;
    private final ObjectDataMapper objectDataMapper;
    private final List<IObjectDataRelationService> objectDataRelationServices;
    private final TransactionTemplate transactionTemplate;
    private final IUserPreferenceSettingDao userPreferenceSettingDao;

    private static final String AUDIT_ID = "id";
    private static final String AUDIT_DEL_FLAG = "del_flag";
    private static final String AUDIT_CREATE_BY = "create_by";
    private static final String AUDIT_CREATE_NAME_BY = "create_name_by";
    private static final String AUDIT_CREATE_TIME = "create_time";
    private static final String AUDIT_UPDATE_BY = "update_by";
    private static final String AUDIT_UPDATE_NAME_BY = "update_name_by";
    private static final String AUDIT_UPDATE_TIME = "update_time";

    private static final String BASE_TABLE_SUFFIX = "_base";

    private static final String BLANK_STR = "''";

    private static final List<String> AUDITS = List.of(AUDIT_ID, AUDIT_DEL_FLAG, AUDIT_CREATE_BY, AUDIT_CREATE_NAME_BY,
            AUDIT_CREATE_TIME, AUDIT_UPDATE_BY, AUDIT_UPDATE_NAME_BY, AUDIT_UPDATE_TIME);

    @Override
    public ObjectDataPostVo save(ObjectDataSaveDto dto) {
        Long objectInfoId = dto.getObjectInfoId();
        // 获取对象
        ObjectInfoVo objectInfo = this.getObjectAndCheck(objectInfoId);
        // 获取对象字段
        List<ObjectAttributeInfoVo> attributeInfos = this.getAttributeInfos(objectInfoId, true);
        String groupKey = objectInfo.getGroupKey();
        Optional<IObjectDataRelationService> relationService = objectDataRelationServices.stream()
                .filter(item -> groupKey.equals(item.getGroupKey()))
                .findFirst();
        JSONObject data = dto.getData();
        // 通知业务处理关联操作
        relationService.ifPresent(objectDataRelationService ->
                objectDataRelationService.beforeSave(data));
        // 内置字段
        List<ObjectDataDbDto> systemBuildInDbDtos = getColumnDbDtos(attributeInfos, data, true);
        // 自定义字段
        // 构造自定义字段入库数据
        List<ObjectDataDbDto> customDbDtos = getColumnDbDtos(attributeInfos, data, false);
        ;
        if (CollectionUtil.isEmpty(systemBuildInDbDtos) && CollectionUtil.isEmpty(customDbDtos)) {
            throw new ServiceException("没有可保存的字段");
        }
        // 构建修改时的审计字段
        Long baseId = this.buildSaveAudit(systemBuildInDbDtos);
        Long objectDataId = this.buildSaveAudit(customDbDtos);
        // 内置base表名
        String baseTableName = objectInfo.getGroupKey() + BASE_TABLE_SUFFIX;
        // 自定义对象表名
        String tableName = objectInfo.getKeyValue();
        transactionTemplate.execute(status -> {
            // 入库
            if (null != baseId) {
                objectDataMapper.save(baseTableName,
                        systemBuildInDbDtos.stream().map(ObjectDataDbDto::getColumn).toList(),
                        systemBuildInDbDtos.stream().map(ObjectDataDbDto::getValue).toList()
                );
            }
            // 入库
            if (null != objectDataId) {
                objectDataMapper.save(tableName,
                        customDbDtos.stream().map(ObjectDataDbDto::getColumn).toList(),
                        customDbDtos.stream().map(ObjectDataDbDto::getValue).toList()
                );
            }
            // 建立关联关系
            if (null != baseId && null != objectDataId) {
                BaseObjectDataPO po = BaseObjectDataPO.builder()
                        .groupKey(groupKey)
                        .baseId(baseId)
                        .objectInfoId(objectInfoId)
                        .objectDataId(objectDataId)
                        .build();
                baseObjectDataDao.insert(po);
            }
            // 通知业务处理关联操作
            relationService.ifPresent(objectDataRelationService ->
                    objectDataRelationService.afterSave(baseId, objectDataId, data));
            return null;
        });
        return new ObjectDataPostVo(baseId, objectDataId);
    }

    @Override
    public ObjectDataPostVo update(ObjectDataUpdateDto dto) {
        // 获取id
        JSONObject data = dto.getData();
        Long objectInfoId = dto.getObjectInfoId();
        // 获取对象
        ObjectInfoVo objectInfo = this.getObjectAndCheck(objectInfoId);
        // 获取对象字段
        List<ObjectAttributeInfoVo> attributeInfos = this.getAttributeInfos(objectInfoId, true);
        // 获取baseId
        Long objectDataId = dto.getObjectDataId();
        Long baseId = Optional.ofNullable(baseObjectDataDao.getByDataId(objectDataId))
                .orElse(new BaseObjectDataPO()).getBaseId();
        String groupKey = objectInfo.getGroupKey();
        Optional<IObjectDataRelationService> relationService = objectDataRelationServices.stream()
                .filter(item -> groupKey.equals(item.getGroupKey()))
                .findFirst();
        List<ObjectDataDbDto> systemBuildInDbDtos = getColumnDbDtos(attributeInfos, data, true);
        // 通知业务处理关联操作
        relationService.ifPresent(objectDataRelationService ->
                objectDataRelationService.beforeUpdate(baseId, objectDataId, data));
        // 自定义字段
        List<ObjectDataDbDto> customDbDtos = getColumnDbDtos(attributeInfos, data, false);
        if (CollectionUtil.isEmpty(systemBuildInDbDtos) && CollectionUtil.isEmpty(customDbDtos)) {
            throw new ServiceException("没有可保存的字段");
        }
        // 构建修改时的审计字段
        this.buildUpdateAudit(systemBuildInDbDtos);
        this.buildUpdateAudit(customDbDtos);
        // 内置base表名
        String baseTableName = objectInfo.getGroupKey() + BASE_TABLE_SUFFIX;
        // 自定义对象表名
        String tableName = objectInfo.getKeyValue();
        // 入库
        transactionTemplate.execute(status -> {
            if (CollectionUtil.isNotEmpty(systemBuildInDbDtos)) {
                objectDataMapper.update(baseId, baseTableName, systemBuildInDbDtos);
            }
            if (CollectionUtil.isNotEmpty(customDbDtos)) {
                objectDataMapper.update(objectDataId, tableName, customDbDtos);
            }
            // 通知业务处理关联操作
            relationService.ifPresent(objectDataRelationService ->
                    objectDataRelationService.afterUpdate(baseId, objectDataId, data));
            return null;
        });
        return new ObjectDataPostVo(baseId, objectDataId);
    }

    @Override
    public ObjectDataVo info(ObjectDataInfoDto dto) {
        Long objectInfoId = dto.getObjectInfoId();
        // 获取对象
        ObjectInfoVo objectInfo = this.getObjectAndCheck(objectInfoId);
        Long objectDataId = dto.getObjectDataId();
        // 获取baseId
        Long baseId = Optional.ofNullable(baseObjectDataDao.getByDataId(objectDataId))
                .orElse(new BaseObjectDataPO()).getBaseId();
        // 获取对象字段
        List<ObjectAttributeInfoVo> attributeInfos = this.getAttributeInfos(objectInfoId, true);
        // 内置字段
        List<ObjectAttributeInfoVo> systemBuildInAttrs = attributeInfos.stream()
                .filter(item -> Boolean.TRUE.equals(item.getSystemBuildInFlag())).toList();
        Map<String, ObjectAttributeInfoVo> systemBuildInAttrMap = systemBuildInAttrs.stream()
                .collect(Collectors.toMap(ObjectAttributeInfoVo::getKeyValue,
                        Function.identity(),
                        (o1, o2) -> o1));
        Set<String> baseColumns = null;
        if (null != baseId) {
            // base表查询列
            baseColumns = new HashSet<>(systemBuildInAttrMap.keySet());
        }
        // 自定义字段
        List<ObjectAttributeInfoVo> customAttrs = attributeInfos.stream()
                .filter(item -> Boolean.FALSE.equals(item.getSystemBuildInFlag())).toList();
        Map<String, ObjectAttributeInfoVo> customAttrMap = customAttrs.stream()
                .collect(Collectors.toMap(ObjectAttributeInfoVo::getKeyValue,
                        Function.identity(),
                        (o1, o2) -> o1));
        // 自定义表查询列
        Set<String> customColumns = new HashSet<>(customAttrMap.keySet());
        if (CollectionUtil.isEmpty(baseColumns) && CollectionUtil.isEmpty(customColumns)) {
            throw new ServiceException("没有可查询的列");
        }
        // 查询base表信息
        Map<String, Object> baseInfo = null;
        if (CollectionUtil.isNotEmpty(baseColumns)) {
            // 审计列加入查询
            baseColumns.addAll(AUDITS);
            baseInfo = objectDataMapper.info(baseId, objectInfo.getGroupKey() + BASE_TABLE_SUFFIX, baseColumns);
        }
        // 查询自定义表信息
        Map<String, Object> customInfo = null;
        if (CollectionUtil.isNotEmpty(customColumns)) {
            // 审计列加入查询
            customColumns.addAll(AUDITS);
            customInfo = objectDataMapper.info(objectDataId, objectInfo.getKeyValue(), customColumns);
        }
        if (CollectionUtil.isEmpty(baseInfo) && CollectionUtil.isEmpty(customInfo)) {
            throw new ServiceException("数据不存在");
        }
        // 处理查询出的数据，并除去前缀
        Map<String, Object> data = new HashMap<>();
        buildResult(baseInfo, systemBuildInAttrMap, data);
        buildResult(customInfo, customAttrMap, data);
        Optional<IObjectDataRelationService> relationService = objectDataRelationServices.stream()
                .filter(item -> objectInfo.getGroupKey().equals(item.getGroupKey()))
                .findFirst();
        relationService.ifPresent(objectDataRelationService->
                objectDataRelationService.afterInfo(objectInfoId,objectDataId,data));
        ObjectDataVo result = new ObjectDataVo();
        result.setObjectInfoId(objectInfoId);
        result.setObjectDataId(objectDataId);
        result.setData(data);
        return result;
    }

    /**
     * 获取内置字段或自定义字段
     *
     * @param attributeInfos
     * @param data
     * @param filter
     * @return List<ObjectDataDbDto>
     * <AUTHOR>
     * @date 2025/8/6 14:00
     */
    private List<ObjectDataDbDto> getColumnDbDtos(List<ObjectAttributeInfoVo> attributeInfos, JSONObject data, boolean filter) {
        // 筛选字段
        List<ObjectAttributeInfoVo> systemBuildInAttrs = attributeInfos.stream()
                .filter(item -> item.getSystemBuildInFlag().equals(filter)).toList();
        // 构造入库数据
        return this.getNoNullDbDtos(data, systemBuildInAttrs);
    }

    /**
     * 构建返回信息
     *
     * @param info
     * @param attrMap
     * @param data
     * @return void
     * <AUTHOR>
     * @date 2025/8/5 11:47
     */
    private void buildResult(Map<String, Object> info,
                             Map<String, ObjectAttributeInfoVo> attrMap,
                             Map<String, Object> data) {
        // TODO 字典、数据源翻译
        if (CollectionUtil.isNotEmpty(info)) {
            for (String field : info.keySet()) {
                ObjectAttributeInfoVo attr = attrMap.get(field);
                // 判断是不是审计字段，是直接作为键，不是用propertyName作为键
                String label = AUDITS.contains(field) ? field : attr.getPropertyName();
                Object value = info.get(field);
                if (null == attr) {
                    data.put(label, value);
                    continue;
                }
                if ("json".equals(attr.getFieldType()) && null != value) {
                    String string = value.toString();
                    data.put(label, JSON.parse(string));
                } else {
                    data.put(label, value);
                }
            }
        }
    }


    @Override
    public Boolean removeByDto(ObjectDataInfoDelDto dto) {

        Long objectInfoId = dto.getObjectInfoId();
        Long objectDataId = dto.getObjectDataId();
        // 获取对象
        ObjectInfoVo objectInfo = this.getObjectAndCheck(objectInfoId);

        // base 表的数据ID
        Long baseId = Optional.ofNullable(baseObjectDataDao.getByDataId(objectDataId)).orElse(new BaseObjectDataPO())
                .getBaseId();

        // 前置通知
        String groupKey = objectInfo.getGroupKey();
        objectDataRelationServices.stream()
                .filter(item -> groupKey.equals(item.getGroupKey()))
                .findFirst()
                .ifPresent(objectDataRelationService ->
                        objectDataRelationService.beforeDelete(baseId, objectDataId, dto.getData()));


        // 自定义对象表明
        String tableName = objectInfo.getKeyValue();
        // 删除数据
        LoginUser loginUser = SecurityUtils.getLoginUser();

        return transactionTemplate.execute(status -> {
            objectDataMapper.removeByTableId(objectDataId, tableName, loginUser);
            if (baseId != null) {
                ObjectGroupPO objectGroupPO = objectInfoDao.findObjectGroupByObjectId(objectInfo.getId());

                // 删除 base表的数据
                objectDataMapper.removeByTableId(baseId,
                        objectGroupPO.getGroupKey() + BASE_TABLE_SUFFIX,
                        loginUser);
            }

            // 后置通知
            objectDataRelationServices.stream()
                    .filter(item -> groupKey.equals(item.getGroupKey()))
                    .findFirst()
                    .ifPresent(objectDataRelationService ->
                            objectDataRelationService.afterDelete(baseId, objectDataId, dto.getData()));
            return true;
        });
    }

    @Override
    public List<Map<String, Object>> selectList(CustomSearchQuery query) {
        Long objectInfoId = query.getObjectInfoId();
        // 获取对象
        ObjectInfoVo objectInfo = this.getObjectAndCheck(objectInfoId);

        // 前置通知
        String groupKey = objectInfo.getGroupKey();
        IObjectDataRelationService service = objectDataRelationServices.stream()
                .filter(item -> groupKey.equals(item.getGroupKey()))
                .findFirst().get();
        String permissionSql = service.beforeQuery("t2", "t1", objectInfo);
        log.info("【自定对象】分页查询 权限SQL => {}", permissionSql);

        // 查询 base 表的字段
        // 查询 自定义对象字段
        List<ObjectAttributeInfoPO> objectAttrs = objectAttributeInfoDao.findByPO(
                ObjectAttributeInfoPO.builder()
                        .objectInfoId(objectInfo.getId())
                        .disabled("0")
                        .tableFieldExistFlag(true)
                        .build());
        // 构造 查询SQL
        List<UserPreferenceSettingPO> userPreferenceSettingPOS = userPreferenceSettingDao.findByPO(UserPreferenceSettingPO.builder()
                .module(query.getModule())
                .businessType(query.getBusinessType())
                .preferenceType(PreferenceTypeEnum.LIST)
                .build());
        String querySQL = buildQuerySQLForSelect(objectInfo, objectAttrs, userPreferenceSettingPOS);
        String fromSQL = buildQuerySQLForFrom(objectInfo);

        log.info("【自定对象】分页查询 SQL => {}", querySQL);

        List<QueryField> searchSystemFields = buildQueryFields(query, objectAttrs, true);
        List<QueryField> searchCustomFields = buildQueryFields(query, objectAttrs, false);

        // 查询数据
        startPage();
        List<Map<String, Object>> list = objectDataMapper.executionSQL(objectInfo.getSystemBuildInFlag(), querySQL, fromSQL, searchSystemFields, searchCustomFields, permissionSql);

        if (CollUtil.isNotEmpty(list)){
            list.forEach(item -> item.put("objectInfoId",objectInfoId));
        }
        // 后置通知
        objectDataRelationServices.stream()
                .filter(item -> groupKey.equals(item.getGroupKey()))
                .findFirst()
                .ifPresent(objectDataRelationService ->
                        objectDataRelationService.afterQuery(objectInfo, list));

        return list;
    }

    /**
     * 构造查询 from sql
     *
     * @param objectInfoVo             对象信息
     * @return String
     * <AUTHOR>
     * @date 2025/8/6 15:40
     */
    private String buildQuerySQLForFrom(ObjectInfoVo objectInfoVo) {
        StringBuilder sql = new StringBuilder();
        if (objectInfoVo.getSystemBuildInFlag()) {
            // 内置对象组，需要关联 表查询
            sql.append(" FROM ") // TODO   主表 ？？？    可读性？？？
                    .append(objectInfoVo.getGroupKey()).append("_base t2")
                    .append(" LEFT JOIN base_object_data t3 ON t3.base_id = t2.id")
                    .append(" LEFT JOIN ")
                    .append(objectInfoVo.getKeyValue()).append(" t1 ON t1.id = t3.object_data_id");
        } else {
            sql.append(" FROM ")
                    .append(objectInfoVo.getKeyValue()).append(" t1");
        }
        return sql.toString();
    }


    /**
     * 构造查询 select sql
     *
     * @param objectInfoVo             对象信息
     * @param objectAttrs              查询的字段，包含系统字段和自定义字段
     * @param userPreferenceSettingPOS 用户自定义字段
     * @return String
     * <AUTHOR>
     * @date 2025/8/6 15:40
     */
    private String buildQuerySQLForSelect(ObjectInfoVo objectInfoVo,
                                          List<ObjectAttributeInfoPO> objectAttrs,
                                          List<UserPreferenceSettingPO> userPreferenceSettingPOS) {


        StringBuilder sql = new StringBuilder();

        // 处理用户自定义展示字段
        if (CollectionUtil.isNotEmpty(userPreferenceSettingPOS)) {
            List<Long> settingIds = userPreferenceSettingPOS.get(0).getSetting();
            if (CollectionUtil.isNotEmpty(settingIds)) {
                // 这里应该从缓存或数据库获取字段信息，这里简化处理
                Map<Long, ObjectAttributeInfoPO> idAndAttributeMap = objectAttrs.stream().collect(Collectors.toMap(ObjectAttributeInfoPO::getId, item -> item));

                sql.append(settingIds.stream()
                        .map(id -> {
                            ObjectAttributeInfoPO attributeInfoPO = idAndAttributeMap.get(id);
                            if (attributeInfoPO != null
                                    && attributeInfoPO.getTableFieldExistFlag()
                                    && attributeInfoPO.getSystemBuildInFlag()) {
                                // 内置字段
                                return "t2." + attributeInfoPO.getKeyValue() + " as " + attributeInfoPO.getPropertyName();
                            } else if (attributeInfoPO != null
                                    && attributeInfoPO.getTableFieldExistFlag()
                                    && !attributeInfoPO.getSystemBuildInFlag()) {
                                // 自定义字段
                                return "t1." + attributeInfoPO.getKeyValue() + " as " + attributeInfoPO.getPropertyName();
                            }
                            return null;
                        })
                        .filter(StrUtil::isNotEmpty).collect(Collectors.joining(", ")));
            }
        }
        return sql.toString();
    }

    private List<QueryField> buildQueryFields(CustomSearchQuery query,
                                              List<ObjectAttributeInfoPO> objectAttrs,
                                              boolean isSystemBuildInField) {
        Map<String, Object> customSearchFields = query.getCustomSearchFields();
        if (CollectionUtil.isEmpty(customSearchFields)) {
            return new ArrayList<>();
        }
        List<QueryField> queryFields = new ArrayList<>();
        for (ObjectAttributeInfoPO objectAttr : objectAttrs) {
            String key = objectAttr.getPropertyName();
            if (!customSearchFields.containsKey(key)) {
                continue;
            }
            // 系统内置字段
            if (isSystemBuildInField) {
                if (objectAttr.getSystemBuildInFlag()) {
                    queryFields.add(QueryField.builder()
                            .column(objectAttr.getKeyValue())
                            .widgetType(objectAttr.getSearchWidgetType())
                            .value(customSearchFields.get(key))
                            .build());
                }
            } else {
                if (!objectAttr.getSystemBuildInFlag()) {
                    queryFields.add(QueryField.builder()
                            .column(objectAttr.getKeyValue())
                            .widgetType(objectAttr.getSearchWidgetType())
                            .value(customSearchFields.get(key))
                            .build());
                }
            }
        }
        return queryFields;
    }

    /**
     * 获取字段信息
     *
     * @param objectInfoId
     * @return List<ObjectAttributeInfoVo>
     * <AUTHOR>
     * @date 2025/7/29 16:46
     */
    private List<ObjectAttributeInfoVo> getAttributeInfos(Long objectInfoId, Boolean tableFieldExist) {
        List<ObjectAttributeInfoVo> attributeInfos = objectAttributeInfoDao.getSimpleByObjectInfoId(objectInfoId);
        if (CollectionUtil.isEmpty(attributeInfos)) {
            throw new ServiceException("对象没有维护字段");
        }
        if (null != tableFieldExist) {
            attributeInfos = attributeInfos.stream()
                    .filter(item -> tableFieldExist.equals(item.getTableFieldExistFlag()))
                    .toList();
        }
        return attributeInfos;
    }

    /**
     * 获取要处理的字段与值的映射信息
     *
     * @param data
     * @param attributeInfos
     * @return List<ObjectDataDbDto>
     * <AUTHOR>
     * @date 2025/7/29 16:43
     */
    private List<ObjectDataDbDto> getNoNullDbDtos(JSONObject data, List<ObjectAttributeInfoVo> attributeInfos) {
        if (CollectionUtil.isEmpty(attributeInfos)) return List.of();
        List<ObjectDataDbDto> dbDtos = new ArrayList<>();
        Set<String> addedFields = new HashSet<>();
        for (ObjectAttributeInfoVo attributeInfo : attributeInfos) {
            String fieldName = attributeInfo.getKeyValue();
            if (addedFields.contains(fieldName)) continue;
            addedFields.add(fieldName);
            String propertyName = attributeInfo.getPropertyName();
            Object fieldValue = data.get(propertyName);
            if (null == fieldValue) continue;
            String fieldValueStr = fieldValue.toString();
            if (StrUtil.isBlank(fieldValueStr) || BLANK_STR.equals(fieldValueStr)) {
                continue;
            }
            if (fieldValue instanceof JSONObject || fieldValue instanceof JSONArray) {
                fieldValue = JSON.toJSONString(fieldValue);
            }
            ObjectDataDbDto dbDto = new ObjectDataDbDto(fieldName, fieldValue);
            dbDtos.add(dbDto);
        }
        return dbDtos;
    }

    /**
     * 构建新增数据的id和审计字段
     *
     * @param dbDtos
     * @return Long
     * <AUTHOR>
     * @date 2025/7/29 15:57
     */
    private Long buildSaveAudit(List<ObjectDataDbDto> dbDtos) {
        if (CollectionUtil.isEmpty(dbDtos)) return null;
        LoginUser loginUser = SecurityUtils.getLoginUser();
        long id = IdUtil.getSnowflake().nextId();
        dbDtos.add(new ObjectDataDbDto(AUDIT_ID, id));
        dbDtos.add(new ObjectDataDbDto(AUDIT_CREATE_TIME, DateUtil.now()));
        dbDtos.add(new ObjectDataDbDto(AUDIT_CREATE_BY, null != loginUser ? loginUser.getUserId() : -1L));
        dbDtos.add(new ObjectDataDbDto(AUDIT_CREATE_NAME_BY, null != loginUser ? loginUser.getRealName() : "SYS_MAN"));
        dbDtos.add(new ObjectDataDbDto(AUDIT_DEL_FLAG, NO_DELETE));
        return id;
    }

    /**
     * 构建修改数据的审计字段
     *
     * @param dbDtos
     * @return Long
     * <AUTHOR>
     * @date 2025/7/29 15:57
     */
    private void buildUpdateAudit(List<ObjectDataDbDto> dbDtos) {
        if (CollectionUtil.isEmpty(dbDtos)) return;
        LoginUser loginUser = SecurityUtils.getLoginUser();
        dbDtos.add(new ObjectDataDbDto(AUDIT_UPDATE_TIME, DateUtil.now()));
        dbDtos.add(new ObjectDataDbDto(AUDIT_UPDATE_BY, null != loginUser ? loginUser.getUserId() : -1L));
        dbDtos.add(new ObjectDataDbDto(AUDIT_UPDATE_NAME_BY, null != loginUser ? loginUser.getRealName() : "SYS_MAN"));
    }


    /**
     * 根据对象id获取对象信息，并校验部分数据的合法性
     *
     * @param objectInfoId
     * @return ObjectInfoVo
     * <AUTHOR>
     * @date 2025/7/29 14:28
     */
    private ObjectInfoVo getObjectAndCheck(Long objectInfoId) {
        ObjectInfoVo objectInfo = objectInfoDao.getSimpleById(objectInfoId);
        if (null == objectInfo) throw new ServiceException("对象不存在");
        // 是否发布
        if (null == objectInfo.getPublish() || ENABLE_STATUS != objectInfo.getPublish()) {
            throw new ServiceException("对象未发布");
        }
        // 对象组表示是否为空
        if (StrUtil.isBlank(objectInfo.getGroupKey())) {
            throw new ServiceException("未维护对象组标识");
        }
        return objectInfo;
    }


}

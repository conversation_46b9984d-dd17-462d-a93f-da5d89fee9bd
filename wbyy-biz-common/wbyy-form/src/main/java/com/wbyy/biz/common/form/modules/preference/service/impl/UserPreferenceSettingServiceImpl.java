package com.wbyy.biz.common.form.modules.preference.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.wbyy.biz.common.form.modules.objectattribute.dao.IObjectAttributeInfoDao;
import com.wbyy.biz.common.form.modules.objectattribute.domain.ObjectAttributeInfoPO;
import com.wbyy.biz.common.form.modules.objectform.dao.IObjectFormDataSourceDao;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormDataSourcePO;
import com.wbyy.biz.common.preference.modules.setting.domain.vo.SearchAttributeInfoVO;
import com.wbyy.biz.common.preference.modules.setting.domain.vo.TableHeaderAttributeInfoVO;
import com.wbyy.biz.common.preference.modules.setting.dao.IUserPreferenceSettingDao;
import com.wbyy.biz.common.preference.modules.setting.domain.UserPreferenceSettingPO;
import com.wbyy.biz.common.preference.modules.setting.domain.query.UserPreferenceSettingQuery;
import com.wbyy.biz.common.form.modules.preference.service.IUserPreferenceSettingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户偏好设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service("pmsUserPreferenceSettingService")
@RequiredArgsConstructor
public class UserPreferenceSettingServiceImpl implements IUserPreferenceSettingService {
    private final IUserPreferenceSettingDao userPreferenceSettingDao;
    private final IObjectAttributeInfoDao objectAttributeInfoDao;
    private final IObjectFormDataSourceDao objectFormDataSourceDao;

    @Override
    public List<TableHeaderAttributeInfoVO> getTableHeaderOptionalFields(UserPreferenceSettingQuery query) {
        List<ObjectAttributeInfoPO> poList = getAttributeInfoList(query);
        return poToTableHeader(poList);
    }

    private List<ObjectAttributeInfoPO> getAttributeInfoList(UserPreferenceSettingQuery query) {
        if (query.getObjectInfoId() == null) {
            return new ArrayList<>();
        }
        return objectAttributeInfoDao.findByPO(ObjectAttributeInfoPO.builder()
                .objectInfoId(query.getObjectInfoId())
                .disabled("0")
                .build());
    }

    @Override
    public List<TableHeaderAttributeInfoVO> getTableHeaderSelectedFields(UserPreferenceSettingQuery query) {
        List<ObjectAttributeInfoPO> poList = getTableHeaderSelectedAttributeInfos(query);
        return poToTableHeader(poList);
    }

    private static List<TableHeaderAttributeInfoVO> poToTableHeader(List<ObjectAttributeInfoPO> poList) {
        if (CollectionUtil.isEmpty(poList)) {
            return new ArrayList<>();
        }
        List<TableHeaderAttributeInfoVO> result = new ArrayList<>();
        for (ObjectAttributeInfoPO po : poList) {
            TableHeaderAttributeInfoVO vo = new TableHeaderAttributeInfoVO();
            vo.setId(po.getId());
            vo.setProp(po.getPropertyName());
            vo.setLabel(po.getName());
            vo.setTableHeaderLeftFixedFlag(po.getTableHeaderLeftFixedFlag());
            vo.setTableHeaderSupportConfigFlag(po.getTableHeaderSupportConfigFlag());
            vo.setTableHeaderWidth(po.getTableHeaderWidth());
            vo.setTableHeaderAlign(po.getTableHeaderAlign());
            result.add(vo);
        }
        return result;
    }

    @Override
    public List<SearchAttributeInfoVO> getSearchOptionalFields(UserPreferenceSettingQuery query) {
        List<ObjectAttributeInfoPO> poList = getAttributeInfoList(query);
        return poToSearch(poList);
    }

    @Override
    public List<SearchAttributeInfoVO> getSearchSelectedFields(UserPreferenceSettingQuery query) {
        List<ObjectAttributeInfoPO> poList = getSearchSelectedAttributeInfos(query);
        return poToSearch(poList);
    }

    public List<ObjectAttributeInfoPO> getSearchSelectedAttributeInfos(UserPreferenceSettingQuery query) {
         return getSelectedAttributeInfos(query, true);
    }

    public List<ObjectAttributeInfoPO> getTableHeaderSelectedAttributeInfos(UserPreferenceSettingQuery query) {
        return getSelectedAttributeInfos(query, false);
    }

    private List<ObjectAttributeInfoPO> getSelectedAttributeInfos(UserPreferenceSettingQuery query, boolean search) {
        // 先查询用户偏好设置，如果没有设置，则返回定义的默认设置
        List<ObjectAttributeInfoPO> poList = getAttributeInfoList(query);
        if (CollectionUtil.isEmpty(poList)) {
            return new ArrayList<>();
        }
        UserPreferenceSettingPO exist = userPreferenceSettingDao.selectOne(query);
        if (exist != null) {
            List<Long> fieldIds = exist.getSetting();
            poList.removeIf(po -> !fieldIds.contains(po.getId()));
            poList.sort(Comparator.comparingInt(po -> fieldIds.indexOf(po.getId())));
        } else {
            poList.removeIf(po -> search ? !po.getSearchDefaultFlag() : !po.getTableHeaderDefaultFlag());
        }
        return  poList;
    }

    private List<SearchAttributeInfoVO> poToSearch(List<ObjectAttributeInfoPO> poList) {
        if (CollectionUtil.isEmpty(poList)) {
            return new ArrayList<>();
        }
        Map<Long, String> dataSourceConfigMap = getDataSourceConfigMap(poList);
        List<SearchAttributeInfoVO> result = new ArrayList<>();
        for (ObjectAttributeInfoPO po : poList) {
            SearchAttributeInfoVO vo = new SearchAttributeInfoVO();
            vo.setId(po.getId());
            vo.setProp(po.getPropertyName());
            vo.setLabel(po.getName());
            vo.setSearchWidgetType(po.getSearchWidgetType());
            vo.setDataSourceConfigId(po.getSearchDataSourceId());
            if (po.getSearchDataSourceId() != null) {
                if (dataSourceConfigMap.containsKey(po.getSearchDataSourceId())) {
                    vo.setDataSourceConfig(dataSourceConfigMap.get(po.getSearchDataSourceId()));
                }
            }
            result.add(vo);
        }
        return result;
    }

    private Map<Long, String> getDataSourceConfigMap(List<ObjectAttributeInfoPO> poList) {
        Map<Long, String> result = new HashMap<>();
        Set<Long> ids = poList.stream().map(ObjectAttributeInfoPO::getSearchDataSourceId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(ids)) {
            return result;
        }
        List<ObjectFormDataSourcePO> dataSourceList = objectFormDataSourceDao.listByIds(ids);
        if (CollectionUtil.isEmpty(dataSourceList)) {
            return result;
        }

        for (ObjectFormDataSourcePO po : dataSourceList) {
            result.put(po.getId(), po.getDataSourceConfig());
        }
        return result;
    }
}

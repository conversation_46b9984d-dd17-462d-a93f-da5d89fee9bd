package com.wbyy.biz.common.form.common.query;

import com.wbyy.biz.common.preference.modules.setting.domain.SearchWidgetTypeEnum;
import org.springframework.data.util.Pair;

import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2025/8/1 11:27
 */
public class QueryConditionBuilder {
    public static final String SELECT_COLUMN = "%s.%s as %s";
    public static final String SEARCH_SQL = "%s.%s %s";

    public static String buildSelectColumns(String tablePrefix, List<Pair<String, String>> columns) {
        StringJoiner joiner = new StringJoiner(", ");
        for (Pair<String, String> column : columns) {
            joiner.add(SELECT_COLUMN.formatted(tablePrefix, column.getFirst(), column.getSecond()));
        }
        return joiner.toString();
    }

    public static String buildSearchCondition(String tablePrefix, QueryField field) {
        String column = field.getColumn();
        SearchWidgetTypeEnum widgetType = field.getWidgetType();
        Object value = field.getValue();

        if (value == null) return null;

        return switch (widgetType) {
            case INPUT -> SEARCH_SQL.formatted(tablePrefix, column, "like CONCAT('%', #{field.value}, '%')");
            case NUMBER, RADIO, SELECT, TREE_SELECT, DATE, DATETIME -> SEARCH_SQL.formatted(tablePrefix, column, "= #{field.value}");
            case CHECKBOX, SELECT_MULTIPLE, TREE_SELECT_MULTIPLE -> buildSearchInCondition(tablePrefix,column, value);
            case DATE_RANGE, DATETIME_RANGE -> SEARCH_SQL.formatted(tablePrefix, column, "between #{field.value[0]} and #{field.value[1]}");
        };
    }

    private static String buildSearchInCondition(String tablePrefix, String column, Object value) {
        if (value instanceof List<?> list) {
            if (list.isEmpty()) {
                return "1=0";
            }
            StringJoiner placeholders = new StringJoiner(", ");
            for (int i = 0; i < list.size(); i++) {
                placeholders.add("#{field.value[" + i + "]}");
            }
            return SEARCH_SQL.formatted(tablePrefix, column, "in (" + placeholders + ")");
        }
        return SEARCH_SQL.formatted(tablePrefix, column, "in (#{field.value})");
    }
}
package com.wbyy.biz.common.form.modules.objectattribute.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wbyy.biz.common.drag.service.IBaseVxeService;
import com.wbyy.biz.common.drag.service.impl.BaseVxeServiceImpl;
import com.wbyy.biz.common.form.modules.objectattribute.dao.IObjectAttributeInfoDao;
import com.wbyy.biz.common.form.modules.objectattribute.domain.ObjectAttributeInfoPO;
import com.wbyy.biz.common.form.modules.objectattribute.domain.vo.ObjectAttributeInfoVo;
import com.wbyy.biz.common.form.modules.objectattribute.mapper.ObjectAttributeInfoMapper;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.SpringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 对象属性信息Dao实现层
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectAttributeInfoDaoImpl
        extends BaseVxeServiceImpl<ObjectAttributeInfoMapper, ObjectAttributeInfoPO>
        implements IObjectAttributeInfoDao {

    @Override
    protected void insertBefore(ObjectAttributeInfoPO po) {
        // 校验当前对象中是否包含当前属性
        checkDataExist(po);
    }

    @Override
    public void insert(ObjectAttributeInfoPO po) {
        checkDataExist(po);
        this.save(po);
    }

    @Override
    protected Integer getNextSort(ObjectAttributeInfoPO entity, Boolean next) {
        if (entity == null) {
            return 1;
        }
        Integer sort = entity.getSort();
        if (entity.getSystemBuildInFlag()) {
            sort = 0;
        }
        if (next) {
            return sort + 1;
        } else {
            return sort;
        }
    }

    /**
     * 校验当前对象中是否包含当前属性
     *
     * @param po po
     * <AUTHOR>
     * @date 2025/7/11 09:27
     */
    private void checkDataExist(ObjectAttributeInfoPO po) {
        // 必填校验，不能使用 注解，因为 修改 接口使用的也是这个入参对象
        if (StrUtil.isEmpty(po.getPropertyName())) {
            throw new ServiceException("字段英文名称不能为空");
        }
        if (StrUtil.isEmpty(po.getFieldType())) {
            throw new ServiceException("字段类型不能为空");
        }

        List<ObjectAttributeInfoPO> byPO = findByPO(ObjectAttributeInfoPO.builder()
                .objectInfoId(po.getObjectInfoId())
                .neId(po.getId())
                .propertyName(po.getPropertyName())
                .build());

        if (CollUtil.isNotEmpty(byPO)) {
            throw new ServiceException("当前对象中已存在此属性");
        }

        // 检查是否超过 100 个字段
        LambdaQueryWrapper<ObjectAttributeInfoPO> wrapper = getWrapper(po);
        if (this.count(wrapper) >= 100){
            throw new ServiceException("当前对象已超过 100 个字段");
        }
    }

    @Override
    public LambdaQueryWrapper<ObjectAttributeInfoPO> getWrapper(ObjectAttributeInfoPO entity) {
        return new LambdaQueryWrapper<ObjectAttributeInfoPO>().eq(ObjectAttributeInfoPO::getObjectInfoId, entity.getObjectInfoId());
    }

    @Override
    public IBaseVxeService<ObjectAttributeInfoPO> getProxy() {
        return SpringUtils.getBean(IObjectAttributeInfoDao.class);
    }

    @Override
    public ObjectAttributeInfoPO selectById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<ObjectAttributeInfoPO> selectList(ObjectAttributeInfoPO po) {
        return this.findByPO(po);
    }

    @Override
    public List<ObjectAttributeInfoPO> findByPO(ObjectAttributeInfoPO po) {
        if (po == null) {
            return List.of();
        }
        LambdaQueryWrapper<ObjectAttributeInfoPO> wrapper = Wrappers.lambdaQuery();
        wrapper.ne(po.getNeId() != null, ObjectAttributeInfoPO::getId, po.getNeId());
        wrapper.eq(po.getObjectInfoId() != null, ObjectAttributeInfoPO::getObjectInfoId, po.getObjectInfoId());
        wrapper.like(StrUtil.isNotEmpty(po.getName()), ObjectAttributeInfoPO::getName, po.getName());
        wrapper.eq(StrUtil.isNotEmpty(po.getPropertyName()), ObjectAttributeInfoPO::getPropertyName, po.getPropertyName());
        wrapper.eq(StrUtil.isNotEmpty(po.getDisabled()), ObjectAttributeInfoPO::getDisabled, po.getDisabled());
        wrapper.eq(StrUtil.isNotEmpty(po.getFieldType()), ObjectAttributeInfoPO::getFieldType, po.getFieldType());
        wrapper.eq(po.getFieldLength() != null, ObjectAttributeInfoPO::getFieldLength, po.getFieldLength());
        wrapper.eq(po.getFieldDecimalLength() != null, ObjectAttributeInfoPO::getFieldDecimalLength, po.getFieldDecimalLength());
        wrapper.eq(po.getSort() != null, ObjectAttributeInfoPO::getSort, po.getSort());
        wrapper.eq(StrUtil.isNotEmpty(po.getDefaultValue()), ObjectAttributeInfoPO::getDefaultValue, po.getDefaultValue());
        wrapper.eq(StrUtil.isNotEmpty(po.getUnit()), ObjectAttributeInfoPO::getUnit, po.getUnit());
        wrapper.eq(StrUtil.isNotEmpty(po.getUnitEn()), ObjectAttributeInfoPO::getUnitEn, po.getUnitEn());
        wrapper.eq(po.getSystemBuildInFlag() != null, ObjectAttributeInfoPO::getSystemBuildInFlag, po.getSystemBuildInFlag());
        wrapper.eq(po.getTableFieldExistFlag() != null, ObjectAttributeInfoPO::getTableFieldExistFlag, po.getTableFieldExistFlag());
        wrapper.orderByAsc(ObjectAttributeInfoPO::getSort);
        wrapper.orderByAsc(ObjectAttributeInfoPO::getId);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ObjectAttributeInfoPO po) {
        return this.updateById(po);
    }

    /**
     * 批量删除对象属性信息
     *
     * @param ids 需要删除的对象属性信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    public boolean alterColumn(String sql) {
        if (StrUtil.isNotEmpty(sql)) {
            log.info("[对象字段] alterColumn sql: {}", sql);
            boolean ok = this.getBaseMapper().alterColumn(sql);
            log.info("[对象字段] alterColumn ok: {}", ok);
            return ok;
        }
        return false;
    }

    @Override
    public boolean checkTableHasValue(String tableName, String fieldName) {
        if (StrUtil.isEmpty(tableName) || StrUtil.isEmpty(fieldName)){
            return false;
        }
        return this.getBaseMapper().checkTableHasValue(tableName, fieldName);
    }

    @Override
    public boolean deleteColumn(String tableName, String fieldName) {
        if (StrUtil.isEmpty(tableName) || StrUtil.isEmpty(fieldName)){
            return false;
        }
        return this.getBaseMapper().deleteColumn(tableName, fieldName);
    }

    @Override
    public boolean deleteByObjectInfoId(Long objectId) {
        if (objectId == null){
            return false;
        }
        LambdaQueryWrapper<ObjectAttributeInfoPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjectAttributeInfoPO::getObjectInfoId, objectId);
        return getBaseMapper().delete(wrapper) > 0;
    }

    @Override
    public List<ObjectAttributeInfoVo> getSimpleByObjectInfoId(Long objectInfoId) {
        return this.baseMapper.selectSimpleByObjectInfoId(objectInfoId);
    }
}

package com.wbyy.biz.common.form.modules.objectform.dao.impl;

import cn.hutool.core.util.StrUtil;
import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.biz.common.form.modules.objectform.mapper.ObjectFormDataSourceMapper;
import com.wbyy.biz.common.form.modules.objectform.dao.IObjectFormDataSourceDao;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormDataSourcePO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

/**
 * 对象单数据源配置Dao实现层
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectFormDataSourceDaoImpl extends ServiceImpl<ObjectFormDataSourceMapper, ObjectFormDataSourcePO> implements IObjectFormDataSourceDao {

    @Override
    public ObjectFormDataSourcePO selectById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<ObjectFormDataSourcePO> selectList(ObjectFormDataSourcePO po) {
        return this.findByPO(po);
    }

    @Override
    public List<ObjectFormDataSourcePO> findByPO(ObjectFormDataSourcePO po) {
        if (po == null) {
            return List.of();
        }
        LambdaQueryWrapper<ObjectFormDataSourcePO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotEmpty(po.getDataSourceConfig()), ObjectFormDataSourcePO::getDataSourceConfig, po.getDataSourceConfig());
        wrapper.orderByDesc(ObjectFormDataSourcePO::getId);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ObjectFormDataSourcePO po) {
        return this.save(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ObjectFormDataSourcePO po) {
        return this.updateById(po);
    }

    /**
     * 批量删除对象单数据源配置
     *
     * @param ids 需要删除的对象单数据源配置主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    public ObjectFormDataSourcePO getByConfigId(String configId) {
        LambdaQueryWrapper<ObjectFormDataSourcePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectFormDataSourcePO::getConfigId,configId);
        return this.getOne(queryWrapper,false);
    }

    @Override
    public void deleteByConfigId(String configId) {
        LambdaQueryWrapper<ObjectFormDataSourcePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectFormDataSourcePO::getConfigId,configId);
        this.remove(queryWrapper);
    }

}

package com.wbyy.biz.common.form.modules.objectform.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;

/**
 * 对象表单组件信息对象 object_form_widget
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "object_form_widget", autoResultMap = true)
@Schema(description = "对象表单组件信息实体类")
@EqualsAndHashCode(callSuper = true)
public class ObjectFormWidgetPO extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 对象表单表id */
    @Excel(name = "对象表单表id")
    @Schema(description = "对象表单表id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "对象表单表id不能为空")
    private Long objectFormId;

    /** 对象字段信息表id */
    @Excel(name = "对象字段信息表id")
    @Schema(description = "对象字段信息表id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "对象字段信息表id不能为空")
    private Integer objectAttributeInfoId;

    /** 对象表单组件配置 */
    @Excel(name = "对象表单组件配置")
    @Schema(description = "对象表单组件配置", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "对象表单组件配置不能为空")
    private String widgetConfig;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;


    /**
     * 清空审计信息
     *
     * @param
     * @return void
     * <AUTHOR>
     * @date 2025/7/12 10:39
     */
    public void clearAudit(){
        this.id=null;
        this.setCreateBy(null);
        this.setCreateNameBy(null);
        this.setCreateTime(null);
        this.setUpdateBy(null);
        this.setUpdateTime(null);
        this.setUpdateNameBy(null);
    }

}

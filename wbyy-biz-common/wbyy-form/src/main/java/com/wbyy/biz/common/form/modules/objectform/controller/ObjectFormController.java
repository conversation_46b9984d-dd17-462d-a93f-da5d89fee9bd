package com.wbyy.biz.common.form.modules.objectform.controller;

import com.wbyy.biz.common.form.modules.objectform.domain.dto.ObjectFormConfigDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormPO;
import com.wbyy.biz.common.form.modules.objectform.service.IObjectFormService;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.page.TableDataInfo;

/**
 * 对象表单Controller
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "对象表单")
@RequestMapping("/object/form")
public class ObjectFormController extends BaseController {

    private final IObjectFormService objectFormService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("form:objectform:list")
    @GetMapping("/page")
    public TableDataInfo<ObjectFormPO> page(@ParameterObject ObjectFormPO po) {
        startPage();
        List<ObjectFormPO> list = objectFormService.selectList(po);
        return getDataTable(list);
    }

    @Operation(summary = "导出列表")
    @RequiresPermissions("form:objectform:export")
    @Log(title = "对象表单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ObjectFormPO po) {
        List<ObjectFormPO> list = objectFormService.selectList(po);
        ExcelUtil<ObjectFormPO> util = new ExcelUtil<ObjectFormPO>(ObjectFormPO.class);
        util.exportExcel(response, list, "对象表单数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("form:objectform:query")
    @GetMapping(value = "/{id}")
    public R<ObjectFormPO> getInfo(@PathVariable("id") Long id) {
        return R.ok(objectFormService.selectById(id));
    }

    @Operation(summary = "获取详细信息-在业务中使用")
    @RequiresPermissions("form:objectform:query")
    @GetMapping(value = "/get-info-by-object")
    public R<ObjectFormPO> getInfoByObject(@RequestParam("objectInfoId")Long objectInfoId,
                                           @RequestParam("formType")String formType){
        return R.ok(objectFormService.getInfoByObject(objectInfoId,formType));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("form:objectform:add")
    @Log(title = "对象表单", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated ObjectFormPO po) {
        return R.ok(objectFormService.insert(po));
    }

    @Operation(summary = "复用")
    @RequiresPermissions("form:objectform:add")
    @Log(title = "对象表单", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public R<Long> copy(@RequestBody @Validated ObjectFormPO po){
        return R.ok(objectFormService.copy(po));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("form:objectform:edit")
    @Log(title = "对象表单", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated ObjectFormPO po) {
        return R.ok(objectFormService.update(po));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("form:objectform:remove")
    @Log(title = "对象表单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        return R.ok(objectFormService.deleteByIds(ids));
    }

    @Operation(summary = "表单配置")
    @RequiresPermissions("form:objectform:edit")
    @Log(title = "对象表单", businessType = BusinessType.UPDATE)
    @PostMapping("config-form")
    public R<Boolean> configForm(@RequestBody @Validated ObjectFormConfigDto dto) {
        return R.ok(objectFormService.configForm(dto));
    }
}

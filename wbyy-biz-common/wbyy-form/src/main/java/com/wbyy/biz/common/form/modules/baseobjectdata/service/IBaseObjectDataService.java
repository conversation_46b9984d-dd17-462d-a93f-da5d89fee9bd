package com.wbyy.biz.common.form.modules.baseobjectdata.service;

import java.util.List;

import com.wbyy.biz.common.form.modules.baseobjectdata.domain.BaseObjectDataPO;

/**
 * base业务数据与对象数据Id关联Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IBaseObjectDataService {
    /**
     * 查询base业务数据与对象数据Id关联
     *
     * @param id base业务数据与对象数据Id关联主键
     * @return base业务数据与对象数据Id关联
     */
    BaseObjectDataPO selectById(Long id);

    /**
     * 查询base业务数据与对象数据Id关联列表
     *
     * @param po base业务数据与对象数据Id关联
     * @return base业务数据与对象数据Id关联集合
     */
    List<BaseObjectDataPO> selectList(BaseObjectDataPO po);

    /**
     * 新增base业务数据与对象数据Id关联
     *
     * @param po base业务数据与对象数据Id关联
     * @return 结果
     */
    boolean insert(BaseObjectDataPO po);

    /**
     * 修改base业务数据与对象数据Id关联
     *
     * @param po base业务数据与对象数据Id关联
     * @return 结果
     */
    boolean update(BaseObjectDataPO po);

    /**
     * 批量删除base业务数据与对象数据Id关联
     *
     * @param ids 需要删除的base业务数据与对象数据Id关联主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

}

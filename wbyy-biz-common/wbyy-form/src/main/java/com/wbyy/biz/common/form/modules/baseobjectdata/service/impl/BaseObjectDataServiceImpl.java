package com.wbyy.biz.common.form.modules.baseobjectdata.service.impl;

import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.biz.common.form.modules.baseobjectdata.dao.IBaseObjectDataDao;
import com.wbyy.biz.common.form.modules.baseobjectdata.domain.BaseObjectDataPO;
import com.wbyy.biz.common.form.modules.baseobjectdata.service.IBaseObjectDataService;
import org.springframework.transaction.annotation.Transactional;

/**
 * base业务数据与对象数据Id关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaseObjectDataServiceImpl implements IBaseObjectDataService {
    private final IBaseObjectDataDao baseObjectDataDao;

    @Override
    public BaseObjectDataPO selectById(Long id) {

        return baseObjectDataDao.selectById(id);
    }

    @Override
    public List<BaseObjectDataPO> selectList(BaseObjectDataPO po) {

        return baseObjectDataDao.selectList(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(BaseObjectDataPO po) {

        return baseObjectDataDao.insert(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(BaseObjectDataPO po) {

        return baseObjectDataDao.update(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {

        return baseObjectDataDao.deleteByIds(ids);
    }
}

package com.wbyy.biz.common.form.modules.baseobjectdata.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import java.util.List;
import java.util.Arrays;

import com.wbyy.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.biz.common.form.modules.baseobjectdata.mapper.BaseObjectDataMapper;
import com.wbyy.biz.common.form.modules.baseobjectdata.dao.IBaseObjectDataDao;
import com.wbyy.biz.common.form.modules.baseobjectdata.domain.BaseObjectDataPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

/**
 * base业务数据与对象数据Id关联Dao实现层
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaseObjectDataDaoImpl extends ServiceImpl<BaseObjectDataMapper, BaseObjectDataPO> implements IBaseObjectDataDao {

    @Override
    public BaseObjectDataPO selectById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<BaseObjectDataPO> selectList(BaseObjectDataPO po) {
        return this.findByPO(po);
    }

    @Override
    public List<BaseObjectDataPO> findByPO(BaseObjectDataPO po) {
        if (po == null) {
            return List.of();
        }
        LambdaQueryWrapper<BaseObjectDataPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotEmpty(po.getGroupKey()), BaseObjectDataPO::getGroupKey, po.getGroupKey());
        wrapper.eq(po.getBaseId() != null, BaseObjectDataPO::getBaseId, po.getBaseId());
        wrapper.eq(po.getObjectInfoId() != null, BaseObjectDataPO::getObjectInfoId, po.getObjectInfoId());
        wrapper.eq(po.getObjectDataId() != null, BaseObjectDataPO::getObjectDataId, po.getObjectDataId());
        wrapper.orderByDesc(BaseObjectDataPO::getId);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(BaseObjectDataPO po) {
        return this.save(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(BaseObjectDataPO po) {
        return this.updateById(po);
    }

    /**
     * 批量删除base业务数据与对象数据Id关联
     *
     * @param ids 需要删除的base业务数据与对象数据Id关联主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    public List<BaseObjectDataPO> listByBaseId(Long baseId) {
        LambdaQueryWrapper<BaseObjectDataPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseObjectDataPO::getBaseId,baseId);
        return this.list(queryWrapper);
    }

    @Override
    public BaseObjectDataPO getByDataId(Long dataId) {
        if (null==dataId) return null;
        LambdaQueryWrapper<BaseObjectDataPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseObjectDataPO::getObjectDataId,dataId);
        return this.getOne(queryWrapper,false);
    }

    @Override
    public BaseObjectDataPO getByBaseId(Long baseId) {
        if (null==baseId) return null;
        LambdaQueryWrapper<BaseObjectDataPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseObjectDataPO::getBaseId,baseId);
        return this.getOne(queryWrapper,false);
    }

    @Override
    public List<BaseObjectDataPO> listByBaseIds(List<Long> baseIds) {
        if (CollectionUtil.isEmpty(baseIds))return List.of();
        LambdaQueryWrapper<BaseObjectDataPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BaseObjectDataPO::getBaseId,baseIds);
        return this.list(queryWrapper);
    }

}

package com.wbyy.biz.common.form.modules.objectattribute.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wbyy.biz.common.form.common.converter.AttributeConverter;
import com.wbyy.biz.common.form.common.enums.ObjectPublishEnum;
import com.wbyy.biz.common.form.common.utils.ObjectUtils;
import com.wbyy.biz.common.form.modules.commonattribute.dao.ICommonAttributeInfoDao;
import com.wbyy.biz.common.form.modules.commonattribute.domain.CommonAttributeInfoPO;
import com.wbyy.biz.common.form.modules.objectattribute.dao.IObjectAttributeInfoDao;
import com.wbyy.biz.common.form.modules.objectattribute.domain.ObjectAttributeInfoPO;
import com.wbyy.biz.common.form.modules.objectattribute.dto.AddBatchDTO;
import com.wbyy.biz.common.form.modules.objectattribute.dto.ObjectAttributeInfoDTO;
import com.wbyy.biz.common.form.modules.objectattribute.service.IObjectAttributeInfoService;
import com.wbyy.biz.common.form.modules.objectinfo.dao.IObjectInfoDao;
import com.wbyy.biz.common.form.modules.objectinfo.domain.ObjectInfoPO;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.DateUtils;
import com.wbyy.common.redis.annotation.RedisLock;
import com.wbyy.common.redis.helper.SimpleLockHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.wbyy.biz.common.form.common.constant.RedisConstants.OBJECT_INFO_LOCK;
import static com.wbyy.common.core.constant.GenConstants.COLUMNNAME_NOT_LIST;

/**
 * 对象属性信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectAttributeInfoServiceImpl implements IObjectAttributeInfoService {
    private final IObjectAttributeInfoDao objectAttributeInfoDao;
    private final ICommonAttributeInfoDao commonAttributeInfoDao;
    private final IObjectInfoDao objectInfoDao;
    private final SimpleLockHelper simpleLockHelper;
    private final TransactionTemplate transactionTemplate;

    @Override
    public ObjectAttributeInfoPO selectById(Long id) {
        ObjectAttributeInfoPO byId = objectAttributeInfoDao.selectById(id);
        if (null==byId) throw new ServiceException("数据不存在");
        // 去除字段前缀
        byId.setKeyValue(ObjectUtils.removeFiled(byId.getKeyValue()));
        return byId;
    }

    @Override
    public List<ObjectAttributeInfoPO> selectList(ObjectAttributeInfoDTO dto) {
        ObjectAttributeInfoPO po = AttributeConverter.INSTANCT.dto2Po(dto);
        return objectAttributeInfoDao.selectList(po)
                .stream().peek(item->{
                    if (StrUtil.isNotBlank(item.getKeyValue())){
                        // 去除字段前缀
                        item.setKeyValue(ObjectUtils.removeFiled(item.getKeyValue()));
                    }
                }).toList();
    }

    @Override
    @RedisLock(prefix = OBJECT_INFO_LOCK, key = "#po.objectInfoId")
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ObjectAttributeInfoPO po) {
        rewriteKeyValueIfNecessary(po);
        updateDatabaseColumnIfNecessary(po, "`{}` ADD COLUMN ");
        objectAttributeInfoDao.insert(po, new HashSet<>());
        return true;
    }

    /**
     * 改写字段的keyValue，如果是系统内置字段，则不加前缀，否则加上前缀
     * @param po
     */
    private static void rewriteKeyValueIfNecessary(ObjectAttributeInfoPO po) {
        po.setPropertyName(po.getPropertyName().trim());
        String databaseColumnName = ObjectUtils.camelToUnderline(po.getPropertyName());
        if (Arrays.stream(COLUMNNAME_NOT_LIST).anyMatch(databaseColumnName::equalsIgnoreCase)) {
            throw new ServiceException("字段名称不能为系统保留字段");
        }
        if (po.getSystemBuildInFlag()) {
            po.setKeyValue(databaseColumnName);
        } else {
            po.setKeyValue(ObjectUtils.appendField(databaseColumnName));
        }
    }

    @Override
    @RedisLock(prefix = OBJECT_INFO_LOCK, key = "#dto.objectInfoId")
    public String addBatch(AddBatchDTO dto) {
        // 查询对象信息
        ObjectInfoPO objectInfoPO = objectInfoDao.selectById(dto.getObjectInfoId());
        if (objectInfoPO == null) {
            throw new ServiceException("对象不存在");
        }
        // 查询所有通用字段信息
        List<CommonAttributeInfoPO> commonAttributeInfoPOList = commonAttributeInfoDao.findByPO(
                CommonAttributeInfoPO.builder()
                        .ids(dto.getCommonAttributeIds())
                        .build());
        if (CollUtil.isEmpty(commonAttributeInfoPOList)) {
            throw new ServiceException("通用字段不存在");
        }
        // 查询对象字段信息
        List<ObjectAttributeInfoPO> objectAttributeInfoPOList = objectAttributeInfoDao.findByPO(
                ObjectAttributeInfoPO.builder()
                        .objectInfoId(objectInfoPO.getId())
                        .build()
        );

        // 通过对比 keyValue 取出通用字段信息 与 对象字段的差集
        List<CommonAttributeInfoPO> addAttributeInfoPOList = commonAttributeInfoPOList.stream()
                .filter(commonAttributeInfoPO ->
                        objectAttributeInfoPOList.stream().noneMatch(objectAttributeInfoPO ->
                                // 库中对象已有的字段，把字段前缀去掉，再与公共字段库比较
                                (ObjectUtils.removeFiled(objectAttributeInfoPO.getKeyValue()))
                                        .equals(commonAttributeInfoPO.getKeyValue())))
                .toList();
        if (CollUtil.isEmpty(addAttributeInfoPOList)) {
            throw new ServiceException(StrUtil.format("您想要添加的通用字段“{}”，当前对象字段都已存在",
                    commonAttributeInfoPOList.stream().map(item ->
                                    item.getName() + "(" + item.getKeyValue() + ")")
                            .collect(Collectors.joining(","))));
        }

        // 描述信息
        String infoMsg = "";
        if (addAttributeInfoPOList.size() > dto.getCommonAttributeIds().size()) {
            // 说明前端选择的通用字段中 当前字段有已存在的字段
            // 取出本次添加的通用字段在当前对象字段中已存在的字段，组装提示信息
            List<CommonAttributeInfoPO> infoMsgAttributeList = commonAttributeInfoPOList.stream()
                    .filter(commonAttributeInfoPO ->
                            objectAttributeInfoPOList.stream().anyMatch(objectAttributeInfoPO ->
                                    objectAttributeInfoPO.getKeyValue().equals(commonAttributeInfoPO.getKeyValue())))
                    .toList();
            infoMsg = "已存在以下字段：" + infoMsgAttributeList.stream().map(item ->
                            item.getName() + "(" + item.getKeyValue() + ")")
                    .collect(Collectors.joining(","));
        }

        List<ObjectAttributeInfoPO> objectAttributeInfoPOS = AttributeConverter.INSTANCT.commonAttribute2ObjectAttribute(addAttributeInfoPOList);
        // 起始的 sort 值
        Integer startSort = objectAttributeInfoPOList.stream()
                .max(Comparator.comparing(ObjectAttributeInfoPO::getSort))
                .orElseGet(() -> ObjectAttributeInfoPO.builder().sort(0).build()).getSort();
        for (ObjectAttributeInfoPO item : objectAttributeInfoPOS){
            item.setObjectInfoId(objectInfoPO.getId())
                    .setId(null)
                    .setSort(++startSort);
        }
        // 保存
        transactionTemplate.execute(status -> {
            objectAttributeInfoDao.saveBatch(objectAttributeInfoPOS);
            for (ObjectAttributeInfoPO po : objectAttributeInfoPOS) {
                // 如果对象已发布，则需要表加字段
                if (ObjectPublishEnum.PUBLISHED.getCode().equals(objectInfoPO.getPublish())) {
                    // 对象已发布，需要添加表字段
                    String sql = getColumnSQL(po);
                    objectAttributeInfoDao.alterColumn(StrUtil.format("`{}` ADD COLUMN ", objectInfoPO.getKeyValue()) + sql);
                }
            }

            return true;
        });
        if (StrUtil.isNotEmpty(infoMsg)) {
            infoMsg = infoMsg + "</br>" +
                    "以下字段添加成功：" + objectAttributeInfoPOS.stream().map(item ->
                            item.getName() + "(" + item.getKeyValue() + ")")
                    .collect(Collectors.joining(","));
        } else {
            infoMsg = "以下字段添加成功：" + objectAttributeInfoPOS.stream().map(item ->
                            item.getName() + "(" + item.getKeyValue() + ")")
                    .collect(Collectors.joining(","));
        }
        return infoMsg;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(prefix = OBJECT_INFO_LOCK, key = "#po.objectInfoId")
    public boolean update(ObjectAttributeInfoPO po) {
        ObjectAttributeInfoPO byDB = objectAttributeInfoDao.selectById(po.getId());
        if (null==byDB) throw new ServiceException("数据不存在");
        // 下面的信息所有字段都不允许修改
        po.setKeyValue(byDB.getKeyValue());
        po.setFieldType(byDB.getFieldType());
        po.setSystemBuildInFlag(byDB.getSystemBuildInFlag());
        // 下面的信息内置字段不允许修改
        if (Objects.equals(byDB.getSystemBuildInFlag(), Boolean.TRUE)) {
            po.setName(byDB.getName());
            po.setFieldLength(byDB.getFieldLength());
        }

        updateDatabaseColumnIfNecessary(po, "`{}` MODIFY COLUMN ");
        return objectAttributeInfoDao.update(po);
    }

    /**
     * 如果对象已发布，则需要修改表字段
     * @param po
     * @param template
     */
    private void updateDatabaseColumnIfNecessary(ObjectAttributeInfoPO po, String template) {
        if (po.getSystemBuildInFlag()) {
            return;
        }
        ObjectInfoPO objectInfo = objectInfoDao.getById(po.getObjectInfoId());
        // 如果对象已发布，则需要修改表字段
        if (ObjectPublishEnum.PUBLISHED.getCode().equals(objectInfo.getPublish())) {
            // 判断字段信息，发生了哪些变化
            String sql = getColumnSQL(po);
            objectAttributeInfoDao.alterColumn(StrUtil.format(template, objectInfo.getKeyValue()) + sql);
        }
    }


    /**
     * 获取添加、修改字段的 SQL片段
     *
     * @param objectAttributeInfoPO 字段属性信息
     * @return String
     * <AUTHOR>
     * @date 2025/7/16 13:47
     */
    private String getColumnSQL(ObjectAttributeInfoPO objectAttributeInfoPO) {
        StringBuilder sql = new StringBuilder(
                StrUtil.format("`{}` {} ",
                        objectAttributeInfoPO.getKeyValue(),
                        objectAttributeInfoPO.getFieldType())
        );
        switch (objectAttributeInfoPO.getFieldType()) {
            case "varchar":
                // 需要设置长度
                sql.append(StrUtil.format("({}) ", objectAttributeInfoPO.getFieldLength()));

                // 默认值
                if (StrUtil.isNotEmpty(objectAttributeInfoPO.getDefaultValue())) {
                    sql.append(StrUtil.format("default '{}' ", objectAttributeInfoPO.getDefaultValue()));
                } else {
                    sql.append("default null ");
                }
                break;
            case "decimal":
                // 需要设置长度和精度
                sql.append(StrUtil.format("({}, {}) ",
                        objectAttributeInfoPO.getFieldLength(),
                        objectAttributeInfoPO.getFieldDecimalLength()
                ));
                // 默认值
                if (StrUtil.isNotEmpty(objectAttributeInfoPO.getDefaultValue())) {
                    sql.append(StrUtil.format("default {} ", new BigDecimal(objectAttributeInfoPO.getDefaultValue())));
                } else {
                    sql.append("default null ");
                }
                break;
            case "date":
            case "datetime":
                // 默认值
                if (StrUtil.isNotEmpty(objectAttributeInfoPO.getDefaultValue())) {
                    if (DateUtils.isDate(objectAttributeInfoPO.getDefaultValue())) {
                        sql.append(StrUtil.format("default '{}' ", objectAttributeInfoPO.getDefaultValue()));
                    } else {
                        sql.append(StrUtil.format("default {} ", objectAttributeInfoPO.getDefaultValue()));
                    }
                } else {
                    sql.append("default null ");
                }
                break;
            case "json":
            case "text":
            case "longtext":
                // 默认值
                if (StrUtil.isNotEmpty(objectAttributeInfoPO.getDefaultValue())) {
                    sql.append(StrUtil.format("default '{}' ", objectAttributeInfoPO.getDefaultValue()));
                } else {
                    sql.append("default null ");
                }
                break;
            case "int":
            case "bigint":
                // 默认值
                if (StrUtil.isNotEmpty(objectAttributeInfoPO.getDefaultValue())) {
                    sql.append(StrUtil.format("default {} ", objectAttributeInfoPO.getDefaultValue()));
                } else {
                    sql.append("default null ");
                }
                break;
            default:
                throw new ServiceException("字段类型错误");
        }
        // 字段描述
        sql.append(StrUtil.format("comment '{}' ", objectAttributeInfoPO.getName()));
        return sql.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        //  删除之前要判断 是否已经建表
        //   如果建表了，需要判断该字段是否有值，有值不允许删除，否则允许删除
        //   如果没有建表，直接删除字段

        List<ObjectAttributeInfoPO> objectAttributeInfoPOS =
                objectAttributeInfoDao.listByIds(Arrays.asList(ids));
        if (CollUtil.isEmpty(objectAttributeInfoPOS)) {
            throw new ServiceException("字段不存在");
        }
        if (objectAttributeInfoPOS.stream().anyMatch(ObjectAttributeInfoPO::getSystemBuildInFlag)) {
            throw new ServiceException("内置字段不允许删除");
        }
        ObjectAttributeInfoPO attributeInfoPO = objectAttributeInfoPOS.get(0);

        AtomicInteger minSort = new AtomicInteger(objectAttributeInfoPOS.stream()
                .min(Comparator.comparingInt(ObjectAttributeInfoPO::getSort)).get().getSort());
        // 加锁
        simpleLockHelper.execute(OBJECT_INFO_LOCK + attributeInfoPO.getObjectInfoId(),
                3L, 60L, TimeUnit.SECONDS, () -> {
                    // 查询对象信息
                    ObjectInfoPO objectInfoPO = objectInfoDao.getById(attributeInfoPO.getObjectInfoId());

                    for (ObjectAttributeInfoPO po : objectAttributeInfoPOS) {
                        if (ObjectPublishEnum.PUBLISHED.getCode().equals(objectInfoPO.getPublish())) {
                            // 对象已发布
                            // 校验字段是否有值，
                            // 如果有，不允许删除，如果没有，允许删除
                            boolean hasValue = objectAttributeInfoDao.checkTableHasValue(objectInfoPO.getKeyValue(),
                                    po.getKeyValue());
                            if (hasValue) {
                                throw new ServiceException(StrUtil.format("对象已发布，且字段【{}】有数据，请先删除该字段对应的数据", attributeInfoPO.getName()));
                            }
                            // 删除表字段
                            objectAttributeInfoDao.deleteColumn(objectInfoPO.getKeyValue(), po.getKeyValue());
                        }
                    }
                    boolean ok = objectAttributeInfoDao.deleteByIds(ids);

                    // 重排修改 sort 字段
                    LambdaQueryWrapper<ObjectAttributeInfoPO> wrapper = Wrappers.lambdaQuery();

                    wrapper.eq(ObjectAttributeInfoPO::getObjectInfoId, attributeInfoPO.getObjectInfoId())
                            .ge(ObjectAttributeInfoPO::getSort, minSort.get())
                            .orderByAsc(ObjectAttributeInfoPO::getSort);
                    List<ObjectAttributeInfoPO> afterList = objectAttributeInfoDao.list(wrapper);
                    if (CollUtil.isEmpty(afterList)) {
                        return ok;
                    }
                    for (ObjectAttributeInfoPO po : afterList) {
                        po.setSort(minSort.getAndIncrement());
                    }
                    objectAttributeInfoDao.updateBatchById(afterList);
                    return ok;
                });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ObjectAttributeInfoPO doDrag(ObjectAttributeInfoPO entity) {
        objectAttributeInfoDao.drag(entity);
        return this.selectById(entity.getId());
    }
}

package com.wbyy.biz.common.form.modules.objectform.service.impl;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wbyy.biz.common.form.common.constant.RedisConstants;
import com.wbyy.biz.common.form.modules.objectform.dao.IObjectFormWidgetDao;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormWidgetPO;
import com.wbyy.biz.common.form.modules.objectform.domain.dto.ObjectFormConfigDto;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.redis.helper.SimpleLockHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.biz.common.form.modules.objectform.dao.IObjectFormDao;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormPO;
import com.wbyy.biz.common.form.modules.objectform.service.IObjectFormService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import static com.wbyy.common.core.constant.Constants.DISABLE_STATUS;
import static com.wbyy.common.core.constant.Constants.ENABLE_STATUS;

/**
 * 对象表单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectFormServiceImpl implements IObjectFormService {
    private final IObjectFormDao objectFormDao;
    private final IObjectFormWidgetDao objectFormWidgetDao;
    private final SimpleLockHelper simpleLockHelper;
    private final TransactionTemplate transactionTemplate;

    @Override
    public ObjectFormPO selectById(Long id) {
        ObjectFormPO byId = objectFormDao.selectById(id);
        if (null == byId) throw new ServiceException("数据不存在");
        List<ObjectFormWidgetPO> widgets = objectFormWidgetDao.listByObjectFormId(id);
        byId.setWidgets(widgets);
        return byId;
    }

    @Override
    public ObjectFormPO getInfoByObject(Long objectInfoId, String formType) {
        Long formId = objectFormDao.getIdByObject(objectInfoId, formType);
        if (null==formId){
            throw new ServiceException("此对象在此场景下没有配置表单");
        }
        return this.selectById(formId);
    }

    @Override
    public List<ObjectFormPO> selectList(ObjectFormPO po) {

        return objectFormDao.selectList(po);
    }

    @Override
    public boolean insert(ObjectFormPO po) {
        String lockKey = String.format(RedisConstants.OBJECT_FORM_LOCK, po.getObjectInfoId(), po.getFormType());
        return simpleLockHelper.execute(lockKey, () -> {
            // 默认禁用
            po.setStatusFlag(DISABLE_STATUS);
            // 库中是否有默认
            ObjectFormPO defaultOne = objectFormDao.getDefaultByObjectIdByFormTypeNotById(po.getObjectInfoId(),
                    po.getFormType(), null);
            if (null == defaultOne) {
                // 默认
                po.setDefaultFlag(ENABLE_STATUS);
            } else {
                // 不默认
                po.setDefaultFlag(DISABLE_STATUS);
            }
            objectFormDao.insert(po);
            return true;
        });

    }

    @Override
    public Long copy(ObjectFormPO po) {
        String lockKey = String.format(RedisConstants.OBJECT_FORM_LOCK, po.getObjectInfoId(), po.getFormType());
        return simpleLockHelper.execute(lockKey, () -> {
            // 默认禁用
            po.setStatusFlag(DISABLE_STATUS);
            // 不默认
            po.setDefaultFlag(DISABLE_STATUS);
            Long oldId = po.getId();
            ObjectFormPO newData = BeanUtil.copyProperties(po, ObjectFormPO.class);
            newData.clearAudit();
            return transactionTemplate.execute(status -> {
                this.insert(newData);
                Long newId = newData.getId();
                objectFormWidgetDao.copy(oldId, newId);
                return newId;
            });
        });
    }

    @Override
    public boolean update(ObjectFormPO po) {
        String lockKey = String.format(RedisConstants.OBJECT_FORM_LOCK, po.getObjectInfoId(), po.getFormType());
        return simpleLockHelper.execute(lockKey, () -> {
            // 如果设置是否默认，判断唯一
            Long id = po.getId();
            ObjectFormPO byId = objectFormDao.getById(id);
            if (null == byId) return true;
            // 默认状态与库中不一样
            if (null!=po.getDefaultFlag()&&!byId.getDefaultFlag().equals(po.getDefaultFlag())) {
                // 设置不是默认，库中必须有默认
                if (DISABLE_STATUS == po.getDefaultFlag()) {
                    throw new ServiceException("此类型表单至少设置一条默认数据");
                }
            }
            transactionTemplate.execute(status -> {
                if (null!=po.getDefaultFlag()&&ENABLE_STATUS == po.getDefaultFlag()){
                    objectFormDao.unDefaultByObjectInfoIdByFormType(po.getObjectInfoId(), po.getFormType());
                }
                objectFormDao.update(po);
                List<ObjectFormWidgetPO> widgets = po.getWidgets();
                if (CollectionUtil.isNotEmpty(widgets)) {
                    objectFormWidgetDao.saveBatchByObjectFormId(widgets, po.getId());
                }
                return null;
            });
            return true;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        if (null == ids || ids.length == 0) {
            return true;
        }
        // 校验是否删除的是默认数据
        List<ObjectFormPO> toDeletes = objectFormDao.listByIds(List.of(ids));
        if (toDeletes.stream().anyMatch(item -> ENABLE_STATUS == item.getDefaultFlag())) {
            throw new ServiceException("不得删除默认数据");
        }
        objectFormDao.deleteByIds(ids);
        // 删除关联的表单组件
        objectFormWidgetDao.deleteByObjectFormIds(List.of(ids));

        return true;
    }

    @Override
    public boolean configForm(ObjectFormConfigDto dto) {
        ObjectFormPO byId = objectFormDao.selectById(dto.getId());
        if (null == byId) throw new ServiceException("数据不存在");
        byId.setFormConfig(dto.getFormConfig());
        this.objectFormDao.updateById(byId);
        return true;
    }
}

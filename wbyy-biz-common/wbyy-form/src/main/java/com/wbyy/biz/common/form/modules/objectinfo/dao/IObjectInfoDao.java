package com.wbyy.biz.common.form.modules.objectinfo.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.biz.common.form.modules.objectgroup.domain.ObjectGroupPO;
import com.wbyy.biz.common.form.modules.objectinfo.domain.ObjectInfoPO;
import com.wbyy.biz.common.form.modules.objectinfo.domain.vo.ObjectInfoVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 对象信息Dao接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IObjectInfoDao extends IService<ObjectInfoPO> {
    /**
     * 查询对象信息
     *
     * @param id 对象信息主键
     * @return 对象信息
     */
    ObjectInfoPO selectById(Long id);

    /**
     * 查询对象信息列表
     *
     * @param po 对象信息
     * @return 对象信息集合
     */
    List<ObjectInfoPO> selectList(ObjectInfoPO po);

    /**
     * 根据 po 中不为null的字段 查询对象信息列表
     *
     * @param po 对象信息
     * @return 对象信息列表
     */
    List<ObjectInfoPO> findByPO(ObjectInfoPO po);

    /**
     * 新增对象信息
     *
     * @param po 对象信息
     * @return 结果
     */
    boolean insert(ObjectInfoPO po);

    /**
     * 修改对象信息
     *
     * @param po 对象信息
     * @return 结果
     */
    boolean update(ObjectInfoPO po);

    /**
     * 批量删除对象信息
     *
     * @param ids 需要删除的对象信息主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 创建表
     *
     * @param params 参数
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/16 10:48
     */
    boolean createdTable(Map<String, Object> params);

    /**
     * 判断对象信息表是否存在数据
     *
     * @param tableName 对象KEY
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/16 10:48
     */
    boolean hasData(String tableName);

    /**
     * 删除表
     *
     * @param tableName 删除表名
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/16 10:48
     */
    boolean dropTable(String tableName);

    /**
     * 根据id集合查对象组id集合
     *
     * @param ids
     * @return List<Long>
     * <AUTHOR>
     * @date 2025/7/28 15:36
     */
    List<Long> listObjectGroupIdsByIds(Collection<?> ids);

    /**
     * 根据id获取对象信息
     *
     * @param id
     * @return ObjectInfoVo
     * <AUTHOR>
     * @date 2025/7/29 14:14
     */
    ObjectInfoVo getSimpleById(Long id);
    
    /**
     * 根据自定义对象信息ID，查询自定义对象组信息
     *
     * @param id 自定义对象信息ID
     * @return ObjectGroupPO
     * <AUTHOR>
     * @date 2025/8/5 10:59
     */
    ObjectGroupPO findObjectGroupByObjectId(Long id);
}

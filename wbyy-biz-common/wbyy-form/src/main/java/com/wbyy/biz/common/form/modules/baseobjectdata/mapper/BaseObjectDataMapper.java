package com.wbyy.biz.common.form.modules.baseobjectdata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wbyy.biz.common.form.modules.baseobjectdata.domain.BaseObjectDataPO;
import com.wbyy.system.api.model.LoginUser;
import org.apache.ibatis.annotations.Param;

/**
 * base业务数据与对象数据Id关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface BaseObjectDataMapper extends BaseMapper<BaseObjectDataPO> {

    /**
     * 删除数据
     *
     * @param id
     * @param tableName
     * @param loginUser
     * @return
     */
    boolean removeByTableId(@Param("id") Long id,
                            @Param("tableName") String tableName,
                            @Param("loginUser") LoginUser loginUser);

}

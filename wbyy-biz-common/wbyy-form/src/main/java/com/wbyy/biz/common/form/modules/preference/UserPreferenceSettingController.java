package com.wbyy.biz.common.form.modules.preference;

import com.wbyy.biz.common.preference.modules.setting.domain.PreferenceTypeEnum;
import com.wbyy.biz.common.preference.modules.setting.domain.vo.SearchAttributeInfoVO;
import com.wbyy.biz.common.preference.modules.setting.domain.vo.TableHeaderAttributeInfoVO;
import com.wbyy.biz.common.preference.modules.setting.domain.query.UserPreferenceSettingQuery;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.biz.common.form.modules.preference.service.IUserPreferenceSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 16:18
 */
@RestController("pmsUserPreferenceSettingController")
@RequiredArgsConstructor
@Tag(name = "用户偏好设置")
@RequestMapping("/user/preference/setting")
public class UserPreferenceSettingController extends BaseController {
    private final IUserPreferenceSettingService userPreferenceSettingService;

    @Operation(summary = "获取表头可选字段")
    @RequiresPermissions("preference:setting:optionalTableHeaderFields")
    @GetMapping("/optionalTableHeaderFields")
    public R<List<TableHeaderAttributeInfoVO>> getOptionalTableHeaderFields(@Validated @ParameterObject UserPreferenceSettingQuery query) {
        query.setUserId(SecurityUtils.getUserId());
        query.setPreferenceType(PreferenceTypeEnum.LIST);
        return R.ok(userPreferenceSettingService.getTableHeaderOptionalFields(query));
    }

    @Operation(summary = "获取表头已选字段")
    @RequiresPermissions("preference:setting:selectedTableHeaderFields")
    @GetMapping("/selectedTableHeaderFields")
    public R<List<TableHeaderAttributeInfoVO>> getSelectedTableHeaderFields(@Validated @ParameterObject UserPreferenceSettingQuery query) {
        query.setUserId(SecurityUtils.getUserId());
        query.setPreferenceType(PreferenceTypeEnum.LIST);
        return R.ok(userPreferenceSettingService.getTableHeaderSelectedFields(query));
    }

    @Operation(summary = "获取表头字段")
    @RequiresPermissions("preference:setting:tableHeaderFields")
    @GetMapping("/tableHeaderFields")
    public R<List<TableHeaderAttributeInfoVO>> getTableHeaderFields(@Validated @ParameterObject UserPreferenceSettingQuery query) {
        query.setUserId(SecurityUtils.getUserId());
        query.setPreferenceType(PreferenceTypeEnum.LIST);
        return R.ok(userPreferenceSettingService.getTableHeaderSelectedFields(query));
    }

    @Operation(summary = "获取筛选条件可选字段")
    @RequiresPermissions("preference:setting:optionalSearchFields")
    @GetMapping("/optionalSearchFields")
    public R<List<SearchAttributeInfoVO>> getOptionalSearchFields(@Validated @ParameterObject UserPreferenceSettingQuery query) {
        query.setUserId(SecurityUtils.getUserId());
        query.setPreferenceType(PreferenceTypeEnum.SEARCH);
        return R.ok(userPreferenceSettingService.getSearchOptionalFields(query));
    }

    @Operation(summary = "获取筛选条件已选字段")
    @RequiresPermissions("preference:setting:selectedSearchFields")
    @GetMapping("/selectedSearchFields")
    public R<List<SearchAttributeInfoVO>> getSelectedSearchFields(@Validated @ParameterObject UserPreferenceSettingQuery query) {
        query.setUserId(SecurityUtils.getUserId());
        query.setPreferenceType(PreferenceTypeEnum.SEARCH);
        return R.ok(userPreferenceSettingService.getSearchSelectedFields(query));
    }
}
package com.wbyy.biz.common.form.modules.objectinfo.service;

import com.wbyy.biz.common.form.modules.objectinfo.domain.dto.ObjectDataDto;
import com.wbyy.biz.common.form.modules.objectinfo.domain.dto.ObjectDataInfoDelDto;
import com.wbyy.biz.common.form.modules.objectinfo.domain.dto.ObjectDataInfoDto;
import com.wbyy.biz.common.form.modules.objectinfo.domain.vo.ObjectDataPostVo;
import com.wbyy.biz.common.form.modules.objectinfo.domain.vo.ObjectDataVo;
import com.wbyy.biz.common.form.modules.objectinfo.domain.vo.ObjectInfoVo;
import com.wbyy.biz.common.preference.modules.setting.domain.query.CustomSearchQuery;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/29 13:38
 */
public interface IObjectDataService {

    /**
     * 保存自定义表单数据
     *
     * @param dto
     * @return Long
     * <AUTHOR>
     * @date 2025/7/29 14:10
     */
    ObjectDataPostVo save(ObjectDataDto dto);

    /**
     * 修改自定义表单数据
     *
     * @param dto
     * @return void
     * <AUTHOR>
     * @date 2025/7/29 16:40
     */
    ObjectDataPostVo update(ObjectDataDto dto);

    /**
     * 查询自定义表单数据详情
     *
     * @param dto
     * @return ObjectDataVo
     * <AUTHOR>
     * @date 2025/7/29 17:09
     */
    ObjectDataVo info(ObjectDataInfoDto dto);

    /**
     * 对象数据删除
     *
     * @param dto 删除参数
     * <AUTHOR>
     * @date 2025/7/29 17:09
     * @return  Boolean
     */
    Boolean removeByDto(ObjectDataInfoDelDto dto);

    /**
     * 查询列表（分页）
     *
     * @param query 查询参数
     * <AUTHOR>
     * @date 2025/7/29 17:09
     * @return List<Map<String, Object>>
     */
    List<Map<String, Object>> selectList(CustomSearchQuery query);

}

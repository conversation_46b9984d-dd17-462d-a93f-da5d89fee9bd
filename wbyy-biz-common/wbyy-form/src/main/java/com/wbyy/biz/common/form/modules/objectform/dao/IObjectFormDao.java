package com.wbyy.biz.common.form.modules.objectform.dao;

import java.util.List;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormPO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 对象表单Dao接口
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IObjectFormDao extends IService<ObjectFormPO> {
    /**
     * 查询对象表单
     *
     * @param id 对象表单主键
     * @return 对象表单
     */
    ObjectFormPO selectById(Long id);

    /**
     * 查询对象表单列表
     *
     * @param po 对象表单
     * @return 对象表单集合
     */
    List<ObjectFormPO> selectList(ObjectFormPO po);

    /**
     * 根据 po 中不为null的字段 查询对象表单列表
     *
     * @param po 对象表单
     * @return 对象表单列表
     */
    List<ObjectFormPO> findByPO(ObjectFormPO po);

    /**
     * 新增对象表单
     *
     * @param po 对象表单
     * @return 结果
     */
    boolean insert(ObjectFormPO po);

    /**
     * 修改对象表单
     *
     * @param po 对象表单
     * @return 结果
     */
    boolean update(ObjectFormPO po);

    /**
     * 批量删除对象表单
     *
     * @param ids 需要删除的对象表单主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 获取是这个对象是这个表单类型和不是这个id(可选)的默认数据
     *
     * @param objectInfoId
     * @param formType
     * @param id
     * @return ObjectFormPO
     * <AUTHOR>
     * @date 2025/7/12 10:22
     */
    ObjectFormPO getDefaultByObjectIdByFormTypeNotById(Long objectInfoId,String formType, Long id);

    /**
     * 将这个对象这个类型的数据设置为不是默认
     *
     * @param objectInfoId
     * @param formType
     * @return void
     * <AUTHOR>
     * @date 2025/7/14 17:30
     */
    void unDefaultByObjectInfoIdByFormType(Long objectInfoId,String formType);

    Long getIdByObject(Long objectInfoId, String formType);

}

package com.wbyy.biz.common.form.modules.objectform.service;

import java.util.List;

import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormDataSourcePO;

/**
 * 对象单数据源配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IObjectFormDataSourceService {
    /**
     * 查询对象单数据源配置
     *
     * @param id 对象单数据源配置主键
     * @return 对象单数据源配置
     */
    ObjectFormDataSourcePO selectById(Long id);

    /**
     * 查询对象单数据源配置列表
     *
     * @param po 对象单数据源配置
     * @return 对象单数据源配置集合
     */
    List<ObjectFormDataSourcePO> selectList(ObjectFormDataSourcePO po);

    /**
     * 新增对象单数据源配置
     *
     * @param po 对象单数据源配置
     * @return 结果
     */
    boolean insert(ObjectFormDataSourcePO po);

    /**
     * 修改对象单数据源配置
     *
     * @param po 对象单数据源配置
     * @return 结果
     */
    boolean update(ObjectFormDataSourcePO po);

    /**
     * 批量删除对象单数据源配置
     *
     * @param ids 需要删除的对象单数据源配置主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    void deleteByConfigId(String configId);

}

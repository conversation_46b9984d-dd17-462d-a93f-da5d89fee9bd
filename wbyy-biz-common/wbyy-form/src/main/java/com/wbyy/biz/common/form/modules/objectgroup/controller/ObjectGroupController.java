package com.wbyy.biz.common.form.modules.objectgroup.controller;

import com.wbyy.biz.common.form.modules.objectgroup.domain.ObjectGroupPO;
import com.wbyy.biz.common.form.modules.objectgroup.service.IObjectGroupService;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 对象组信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "对象组信息")
@RequestMapping("/object/group")
public class ObjectGroupController extends BaseController {

    private final IObjectGroupService objectGroupService;

    @Operation(summary = "列表")
    @RequiresPermissions("objectgroup:group:list")
    @GetMapping("/list")
    public R<List<ObjectGroupPO>> list(@ParameterObject ObjectGroupPO po) {
        List<ObjectGroupPO> list = objectGroupService.selectList(po);
        return R.ok(list);
    }

//    @Operation(summary = "导出列表")
//    @RequiresPermissions("objectgroup:group:export")
//    @Log(title = "对象组信息", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ObjectGroupPO po) {
//        List<ObjectGroupPO> list = objectGroupService.selectList(po);
//        ExcelUtil<ObjectGroupPO> util = new ExcelUtil<ObjectGroupPO>(ObjectGroupPO.class);
//        util.exportExcel(response, list, "对象组信息数据");
//    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("objectgroup:group:query")
    @GetMapping(value = "/{id}")
    public R<ObjectGroupPO> getInfo(@PathVariable("id") Long id) {
        return R.ok(objectGroupService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("objectgroup:group:add")
    @Log(title = "对象组信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated ObjectGroupPO po) {
        return R.ok(objectGroupService.insert(po));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("objectgroup:group:edit")
    @Log(title = "对象组信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated ObjectGroupPO po) {
        return R.ok(objectGroupService.update(po));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("objectgroup:group:remove")
    @Log(title = "对象组信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        return R.ok(objectGroupService.deleteByIds(ids));
    }
}

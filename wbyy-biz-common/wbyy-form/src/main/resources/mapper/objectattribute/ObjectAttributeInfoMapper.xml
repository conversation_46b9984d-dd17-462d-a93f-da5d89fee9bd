<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.biz.common.form.modules.objectattribute.mapper.ObjectAttributeInfoMapper">

    <insert id="alterColumn">
        ALTER TABLE ${sql}
    </insert>
    <delete id="deleteColumn">
        ALTER TABLE ${tableName} DROP COLUMN ${fieldName}
    </delete>
    <select id="checkTableHasValue" resultType="java.lang.Boolean">
        select count(1) from ${tableName} where ${fieldName} is not null
    </select>

    <select id="selectSimpleByObjectInfoId"
            resultType="com.wbyy.biz.common.form.modules.objectattribute.domain.vo.ObjectAttributeInfoVo">
        select key_value            as keyValue,
               field_type           as fieldType,
               field_length         as fieldLength,
               field_decimal_length as fieldDecimalLength,
               property_name        as propertyName,
               system_build_in_flag as systemBuildInFlag,
               table_field_exist_flag as tableFieldExistFlag
        from object_attribute_info
        where object_info_id = #{objectInfoId}
        and del_flag = 0
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.biz.common.form.modules.objectinfo.mapper.ObjectDataMapper">

    <insert id="save">
        insert into ${tableName}
        <foreach collection="columns" separator="," item="column" open="(" close=")">
            ${column}
        </foreach>
        values
        <foreach collection="values" separator="," item="value" open="(" close=")">
            #{value}
        </foreach>
    </insert>

    <update id="update">
        update ${tableName} set
            <foreach collection="dbDtos" separator="," open="" close="" item="dbDto">
                ${dbDto.column} = #{dbDto.value}
            </foreach>
        where id = #{id} and del_flag=0
    </update>

    <update id="removeByTableId">
        update ${tableName} set
            del_flag = 1,
            update_time = now(),
            update_by = #{loginUser.userId},
            update_name_by = #{loginUser.realName}
        where id = #{id} and del_flag=0
    </update>

    <select id="info" resultType="java.util.Map">
        select
            <foreach collection="columns" item="column" open="" close="" separator=",">
                ${column}
            </foreach>
        from ${tableName}
        where id=#{id} and del_flag=0
    </select>
    <select id="executionSQL" resultType="java.util.Map">
        Select
            ${querySQL}
        <if test="querySQL != null and querySQL != ''">,</if>
            t1.id as objectDataId,
        <if test="systemBuildInFlag">
            t2.id as baseId,
        </if>
            t1.create_time as createTime,
            t1.update_time as updateTime,
            t1.create_by as createBy,
            t1.update_by as updateBy,
            t1.create_name_by as createNameBy,
            t1.update_name_by as updateNameBy
        ${fromSQL}
        where t1.del_flag = 0
        <if test="systemBuildInFlag">
            and t2.del_flag = 0
            and t3.del_flag = 0
        </if>
        <if test="searchSystemFields != null and searchSystemFields.size() > 0">
            and
            <foreach collection="searchSystemFields" item="field" separator="AND">
                <if test="field.value != null">
                    ${@com.wbyy.biz.common.form.common.query.QueryConditionBuilder@buildSearchCondition("t2", field)}
                </if>
            </foreach>
        </if>
        <if test="searchCustomFields != null and searchCustomFields.size() > 0">
            and
            <foreach collection="searchCustomFields" item="field" separator="AND">
                <if test="field.value != null">
                    ${@com.wbyy.biz.common.form.common.query.QueryConditionBuilder@buildSearchCondition("t1", field)}
                </if>
            </foreach>
        </if>
        <if test="permissionSql != null and permissionSql != ''">
            ${permissionSql}
        </if>
    </select>
</mapper>
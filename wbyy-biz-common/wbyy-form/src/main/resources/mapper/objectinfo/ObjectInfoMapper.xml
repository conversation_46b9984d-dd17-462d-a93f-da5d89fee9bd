<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.biz.common.form.modules.objectinfo.mapper.ObjectInfoMapper">

    <insert id="createdTable">
        CREATE TABLE `${tableName}`  (
            `id` bigint NOT NULL COMMENT '主键id',
            <foreach collection="fields" item="field">
                ${field}
            </foreach>
            `del_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
            `create_by` bigint NULL DEFAULT NULL COMMENT '创建者ID',
            `create_name_by` varchar(30)  NULL DEFAULT NULL COMMENT '创建者姓名',
            `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
            `update_name_by` varchar(30)  NULL DEFAULT NULL COMMENT '更新者姓名',
            `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
            PRIMARY KEY (`id`) USING BTREE
        ) COMMENT = '${tableComment}'


    </insert>
    <delete id="dropTable">
        RENAME TABLE ${tableName} TO del_${tableName}
    </delete>
    <select id="hasData" resultType="java.lang.Boolean">
        select exists(select 1 from ${tableName})
    </select>

    <select id="selectSimpleById" resultType="com.wbyy.biz.common.form.modules.objectinfo.domain.vo.ObjectInfoVo">
        select oi.id,oi.object_group_id as objectGroupId,
               oi.key_value as keyValue,
               oi.publish,
               og.group_key as groupKey,
               og.system_build_in_flag as systemBuildInFlag
        from object_info oi
        left join object_group og on oi.object_group_id=og.id
        where oi.id=#{id} and oi.del_flag=0 and og.del_flag=0
        limit 1
    </select>
</mapper>
-- 按钮父菜单ID
SELECT @parentId := 1944593904203526146;

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '通用属性信息查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'commonattribute:attribute:list',        '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '通用属性信息新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'commonattribute:attribute:add',          '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '通用属性信息修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'commonattribute:attribute:edit',         '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '通用属性信息删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'commonattribute:attribute:remove',       '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '通用属性信息导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'commonattribute:attribute:export',       '#', 1, sysdate(), null, null, '', 'pms-system');
<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!--不显示 Logback 默认的启动信息-->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />

    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <include resource="org/springframework/boot/logging/logback/console-appender.xml" />

    <!-- 日志存放路径 -->
    <springProperty scope="context" name="LOG_PATH" source="spring.application.name"/>
    <!-- 日志最大存活时间（天），默认30天 -->
    <springProperty scope="context" name="MAX_HISTORY" source="logging.logback.rollingpolicy.max-history" defaultValue="30"/>
    <springProperty scope="context" name="MAX_FILE_SIZE" source="logging.logback.rollingpolicy.max-file-size" defaultValue="100"/>


    <!-- 日志存放路径 -->
	<property name="log.path" value="logs/${LOG_PATH}" />
   <!-- 日志输出格式 -->
    <!--
    <property name="log.pattern" value="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{20} - [%method,%line] - %msg%n" />
    -->
    <property name="log.pattern"
              value="%d{HH:mm:ss.SSS} %clr(%-5level) [%.20thread] %clr(%logger{40}){cyan} [%method,%line] - %msg%n"/>
    <property name="log.file"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%.30thread] %logger{40} [%method,%line] - %msg%n"/>

    <!-- 控制台输出 -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>${log.pattern}</pattern>
		</encoder>
	</appender>

    <!-- 系统日志输出 -->
    <appender name="file_debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/debug.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/debug.%d{yyyy-MM-dd}-%i.log.gz</fileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}MB</maxFileSize>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>${MAX_HISTORY}</maxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>DEBUG</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>${log.file}</pattern>
        </encoder>
    </appender>

    <!-- 系统日志输出 -->
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/info.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/info.%d{yyyy-MM-dd}-%i.log.gz</fileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}MB</maxFileSize>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>${MAX_HISTORY}</maxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>INFO</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>${log.file}</pattern>
        </encoder>
    </appender>


    <!-- 系统日志输出 -->
    <appender name="file_warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/warn.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/warn.%d{yyyy-MM-dd}-%i.log.gz</fileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}MB</maxFileSize>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>${MAX_HISTORY}</maxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>WARN</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>${log.file}</pattern>
        </encoder>
    </appender>

    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
	    <file>${log.path}/error.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/error.%d{yyyy-MM-dd}-%i.log.gz</fileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}MB</maxFileSize>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>${MAX_HISTORY}</maxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
			<!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
			<!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>${log.file}</pattern>
        </encoder>
    </appender>

    <!--关闭某些包日志输出-->
    <!-- Spring日志级别控制  -->
    <logger name="org.springframework" level="warn" />
    <logger name="org" level="info" />
    <!-- 系统模块日志级别控制  -->
    <logger name="com.wbyy" level="debug"/>
    <logger name="io.lettuce" level="error"/>
    <logger name="com.alibaba" level="info"/>
    <logger name="com.xxl" level="info"/>
    <!--系统操作日志-->
    <root level="info">
        <appender-ref ref="console" />
        <appender-ref ref="file_debug" />
        <appender-ref ref="file_info" />
        <appender-ref ref="file_warn" />
        <appender-ref ref="file_error" />
    </root>
</configuration>
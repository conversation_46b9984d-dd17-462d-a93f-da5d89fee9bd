package com.wbyy.pms.api.factory;

import com.alibaba.fastjson2.JSONArray;
import com.wbyy.common.core.domain.R;
import com.wbyy.pms.api.BusinessTypeApi;
import com.wbyy.pms.api.domain.ApiBusinessType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/28 17:57
 */
@Component
@Slf4j
public class RemoteBusinessTypeFallbackFactory implements FallbackFactory<BusinessTypeApi> {
    @Override
    public BusinessTypeApi create(Throwable throwable) {
        log.error("PMS服务调用失败:{}", throwable.getMessage());
        return new BusinessTypeApi() {
            @Override
            public R<List<ApiBusinessType>> simpleTree(String source) {
                return R.fail("获取项目业务类型失败:" + throwable.getMessage());            }
        };
    }
}

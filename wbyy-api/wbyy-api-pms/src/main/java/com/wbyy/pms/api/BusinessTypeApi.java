package com.wbyy.pms.api;

import com.alibaba.fastjson2.JSONArray;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.pms.api.domain.ApiBusinessType;
import com.wbyy.pms.api.factory.RemoteBusinessTypeFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/28 17:56
 */
@FeignClient(contextId = "remoteProjectTypeService", value = ServiceNameConstants.PMS_SERVICE, fallbackFactory = RemoteBusinessTypeFallbackFactory.class)
public interface BusinessTypeApi {

    @GetMapping("/business/type/simple-tree")
    R<List<ApiBusinessType>> simpleTree(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

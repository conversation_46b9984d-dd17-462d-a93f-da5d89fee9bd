package com.wbyy.message.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.message.api.domain.ApiSenMessage;
import com.wbyy.message.api.domain.SendVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import com.wbyy.message.api.SendMessageApi;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteMessgaeFallbackFactory implements FallbackFactory<SendMessageApi>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteMessgaeFallbackFactory.class);

    @Override
    public SendMessageApi create(Throwable throwable)
    {
        log.error("消息服务调用失败:{}", throwable.getMessage());
        return new SendMessageApi()
        {
            @Override
            public R<SendVO> send(ApiSenMessage senMessage, String source) {
                return R.fail("发送消息失败:" + throwable.getMessage());
            }
        };
    }
}

package com.wbyy.message.api.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 发送消息VO
 *
 * <AUTHOR>
 * @date 2025/6/30 09:29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "")
public class SendVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 6820053228468409036L;


    @Schema(description = "消息服务任务ID")
    private String msgServerTaskId;
    @Schema(description = "失败原因")
    private String failureReason;
    @Schema(description = "是否成功")
    private Boolean isOk;
}
package com.wbyy.message.api.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * 发送消息对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "发送消息")
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiSenMessage implements Serializable {


    @Serial
    private static final long serialVersionUID = -3149889022451032235L;

    /**
     * 任务ID
     */
    @Schema(description = "任务ID(客户端保证唯一)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "任务ID不能为空")
    @Size(max = 36, message = "任务ID不能超过 36 个字符")
    private String taskId;

    /**
     * 渠道编码
     */
    @Schema(description = "渠道编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "渠道编码不能为空")
    private String channelCode;

    /**
     * 消息类型
     * 
     * @see import com.wbyy.message.common.constant.MessageTypeConst;
     */
    @Schema(description = "消息类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "消息类型不能为空")
    private String type;

    /**
     * 消息模板编码
     */
    @Schema(description = "消息模板编码")
    @NotNull(message = "消息模板编码不能为空")
    private String messageTemplateCode;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "参数（除横向标题列表部分参数）")
    private Map<String, String> params;

    @Schema(description = "参数 - 横向标题列表")
    private Map<String, String> horizontalContentParams;

}

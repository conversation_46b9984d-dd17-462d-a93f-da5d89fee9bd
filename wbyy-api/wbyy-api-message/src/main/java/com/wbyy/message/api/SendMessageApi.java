package com.wbyy.message.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.message.api.domain.ApiSenMessage;
import com.wbyy.message.api.domain.SendVO;
import com.wbyy.message.api.factory.RemoteMessgaeFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 消息服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteMessageService", value = ServiceNameConstants.MESSAGE_SERVICE, fallbackFactory = RemoteMessgaeFallbackFactory.class)
public interface SendMessageApi {
    /**
     * 通过用户名查询用户信息
     *
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/send")
    R<SendVO> send(@RequestBody ApiSenMessage senMessage, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}

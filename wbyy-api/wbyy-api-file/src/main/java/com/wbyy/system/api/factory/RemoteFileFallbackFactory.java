package com.wbyy.system.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.FileApi;
import com.wbyy.system.api.domain.ApiDownloadFile;
import com.wbyy.system.api.domain.ApiSysFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteFileFallbackFactory implements FallbackFactory<FileApi> {
    private static final Logger log = LoggerFactory.getLogger(RemoteFileFallbackFactory.class);

    @Override
    public FileApi create(Throwable throwable) {
        log.error("文件服务调用失败:{}", throwable.getMessage());
        return new FileApi() {
            @Override
            public R<ApiSysFile> upload(MultipartFile file) {
                return R.fail("上传文件失败:" + throwable.getMessage());
            }

            /**
             * 上传文件
             *
             * @param path 上传路径
             * @param file 文件信息
             * @return 结果
             */
            @Override
            public R<ApiSysFile> uploadFile(String path, MultipartFile file,String source) {
                return R.fail("上传文件失败:" + throwable.getMessage());
            }

            /**
             * 批量上传文件
             *
             * @param path  上传路径
             * @param files 文件信息
             * @return 结果
             */
            @Override
            public R<List<ApiSysFile>> uploadBatchFile(String path, List<MultipartFile> files) {
                return R.fail("上传文件失败:" + throwable.getMessage());
            }

            /**
             * 获取文件访问路径
             *
             * @param fileId 文件id
             */
            @Override
            public R<String> getFilePath(Long fileId) {
                return R.fail("获取文件访问路径失败:" + throwable.getMessage());
            }

            /**
             * 文件下载
             *
             * @param fileId 文件id
             */
            @Override
            public R<ApiDownloadFile> downloadFile(Long fileId,String source) {
                return R.fail("文件下载失败:" + throwable.getMessage());
            }

            /**
             * 多文件下载zip
             *
             * @param fileIds 文件id
             */
            @Override
            public R<ApiDownloadFile> downloadZip(List<Long> fileIds) {
                return null;
            }

            @Override
            public R<ApiSysFile> getFileById(Long fileId) {
                return R.fail("查询文件信息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<ApiSysFile>> getFilesByIdList(List<Long> fileIds) {
                return R.fail("查询文件信息失败:" + throwable.getMessage());
            }
        };
    }
}

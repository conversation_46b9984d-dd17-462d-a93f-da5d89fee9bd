package com.wbyy.system.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.domain.ApiDownloadFile;
import com.wbyy.system.api.domain.ApiSysFile;
import com.wbyy.system.api.factory.RemoteFileFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteFileService", value = ServiceNameConstants.FILE_SERVICE, fallbackFactory = RemoteFileFallbackFactory.class, path = "file")
public interface FileApi
{
    /**
     * 上传文件
     *
     * @param file 文件信息
     * @return 结果
     */
    @Deprecated
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<ApiSysFile> upload(@RequestPart(value = "file") MultipartFile file);

    /**
     * 上传文件
     *
     * @param path 上传路径
     * @param file 文件信息
     * @return 结果
     */
    @PostMapping(value = "/uploadFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    R<ApiSysFile> uploadFile(@RequestParam(value = "path") String path,
                             @RequestPart(value = "file") MultipartFile file,
                             @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 批量上传文件
     *
     * @param path 上传路径
     * @param files 文件信息
     * @return 结果
     */
    @PostMapping(value = "/uploadBatchFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    R<List<ApiSysFile>> uploadBatchFile(@RequestParam(value = "path") String path, @RequestPart(value = "files") List<MultipartFile> files);

    /**
     * 获取文件可访问路径
     */
    @GetMapping("/getFilePath")
    R<String> getFilePath(@RequestParam Long fileId);

    /**
     * 文件下载
     */
    @GetMapping("/downloadFile")
    R<ApiDownloadFile> downloadFile(@RequestParam Long fileId,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 多文件下载zip
     */
    @GetMapping("downloadZip")
    R<ApiDownloadFile> downloadZip(@RequestParam List<Long> fileIds);

    @GetMapping("getFileById")
    R<ApiSysFile> getFileById(@RequestParam Long fileId);

    @GetMapping("getFilesByIdList")
    R<List<ApiSysFile>> getFilesByIdList(@RequestParam List<Long> fileIds);
}

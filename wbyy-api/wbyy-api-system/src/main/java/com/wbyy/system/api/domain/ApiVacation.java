package com.wbyy.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wbyy.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: 王金都
 * @date: 2025/6/16 9:58
 */
@Data
public class ApiVacation implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 用户id */
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    /** 假期名称 */
    @Schema(description = "假期名称")
    private String name;

    /** 假期开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "假期开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Date startTime;

    /** 假期结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "假期结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Date endTime;

    /** 假期id（泛微） */
    @Schema(description = "假期id（泛微）")
    private String vacationId;

    /** 请假时长（小时） */
    @Schema(description = "请假时长（小时）")
    private BigDecimal timeLength;

    // user_id-name-start_time-end_time
    private String checkKey;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    private Integer delFlag;
}

package com.wbyy.system.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.domain.ApiPermissionSql;
import com.wbyy.system.api.factory.RemotePermissionFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 权限服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remotePermissionService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemotePermissionFallbackFactory.class)
public interface PermissionApi {
    /**
     * 获取用户权限SQL
     *
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/permission/permission-sql")
    public R<String> getPermissionSql(@SpringQueryMap ApiPermissionSql apiPermissionSql,
                                      @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}

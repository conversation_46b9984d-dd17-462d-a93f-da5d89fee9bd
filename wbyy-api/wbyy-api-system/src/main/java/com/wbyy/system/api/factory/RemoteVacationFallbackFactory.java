package com.wbyy.system.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.UserApi;
import com.wbyy.system.api.VacationApi;
import com.wbyy.system.api.domain.ApiVacation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/6/16 9:57
 */
@Component
@Slf4j
public class RemoteVacationFallbackFactory implements FallbackFactory<VacationApi> {
    @Override
    public VacationApi create(Throwable throwable) {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new  VacationApi() {

            @Override
            public R<List<ApiVacation>> listUserVacation(Long userId, String startDate, String endDate, String source) {
                return R.fail("获取用户假期失败:" + throwable.getMessage());
            }

            @Override
            public R<List<ApiVacation>> listUsersVacation(List<Long> userIds, String startDate, String endDate, String source) {
                return R.fail("获取用户假期失败:" + throwable.getMessage());
            }
        };
    }
}

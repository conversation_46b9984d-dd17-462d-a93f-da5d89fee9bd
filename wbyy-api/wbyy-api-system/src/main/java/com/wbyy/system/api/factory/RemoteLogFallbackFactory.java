package com.wbyy.system.api.factory;

import com.wbyy.system.api.domain.ApiLogininfor;
import com.wbyy.system.api.domain.ApiOperLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.SystemLogApi;

/**
 * 日志服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteLogFallbackFactory implements FallbackFactory<SystemLogApi>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteLogFallbackFactory.class);

    @Override
    public SystemLogApi create(Throwable throwable)
    {
        log.error("日志服务调用失败:{}", throwable.getMessage());
        return new SystemLogApi()
        {
            @Override
            public R<Boolean> saveLog(ApiOperLog sysOperLog, String source)
            {
                return R.fail("保存操作日志失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> saveLogininfor(ApiLogininfor sysLogininfor, String source)
            {
                return R.fail("保存登录日志失败:" + throwable.getMessage());
            }
        };

    }
}

package com.wbyy.system.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.DeptApi;
import com.wbyy.system.api.domain.ApiDept;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteDeptFallbackFactory implements FallbackFactory<DeptApi>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteDeptFallbackFactory.class);

    @Override
    public DeptApi create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage(),throwable);
        return new DeptApi()
        {
            @Override
            public R<ApiDept> getDept(Long deptId, String source) {
                return R.fail("获取用户部门失败:" + throwable.getMessage());
            }

            @Override
            public R<ApiDept> getCenterById(Long deptId, String source) {
                return R.fail("获取用户部门失败:" + throwable.getMessage());
            }

            @Override
            public R<List<ApiDept>> centerList(String source) {
                return R.fail("获取用户部门失败:" + throwable.getMessage());
            }

            @Override
            public R<List<ApiDept>> selectByAncestors(Long deptId, String source) {
                return R.fail("获取用户部门失败:" + throwable.getMessage());
            }

            @Override
            public R<List<ApiDept>> findByDeptIds(Set<Long> deptIds, String source) {
                return R.fail("获取部门失败:" + throwable.getMessage());
            }
        };
    }
}

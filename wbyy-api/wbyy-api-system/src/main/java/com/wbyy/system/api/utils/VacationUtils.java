package com.wbyy.system.api.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.wbyy.common.core.utils.DateUtils;
import com.wbyy.system.api.domain.ApiVacation;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/6/16 19:16
 */
public class VacationUtils {

    /**
     * @Description: 获取这天的请假数据
     * @param startDay
     * @param endDay
     * @param vacations
     * @return: List<ApiVacation>
     * @author: 王金都
     * @Date: 2025/6/16 15:51
     */
    public static List<ApiVacation> getVacations(Date startDay, Date endDay, List<ApiVacation> vacations){
        if (CollectionUtil.isEmpty(vacations)){
            return Collections.emptyList();
        }
        return vacations.stream().filter(item->{
            Date startTime = item.getStartTime();
            Date endTime = item.getEndTime();
            // 开始时间在查询时间之间
            return (DateUtil.compare(startTime,startDay)>=0 && DateUtil.compare(startTime,endDay)<=0)||
                    // 结束时间在查询时间之间
                    (DateUtil.compare(endTime,startDay)>=0 && DateUtil.compare(endTime,startDay)<=0)||
                    // 开始和结束时间横跨查询时间
                    (DateUtil.compare(startTime,startDay)<=0 && DateUtil.compare(endTime,endDay)>=0);
        }).toList();
    }

    private static final BigDecimal WORKING_HOURS_OF_DAY = new BigDecimal("7.5");
    private static final long NOON_REST_MINUTES = 90L;
    private static final BigDecimal MINUTES_OF_HOUR = new BigDecimal("60");

    /**
     * @Description: 获取请假时长
     * @param startDay
    * @param endDay
    * @param endMorning
    * @param startAfternoon
    * @param vacations
     * @return: BigDecimal
     * @author: 王金都
     * @Date: 2025/6/18 17:24
     */
    public static BigDecimal getVacationHour(Date startDay,
                                       Date endDay,
                                       Date endMorning,
                                       Date startAfternoon,
                                       List<ApiVacation> vacations) {
        // 先找今天请假数据
        List<ApiVacation> dateVacations = VacationUtils.getVacations(startDay,endDay, vacations);

        BigDecimal vacationHour = BigDecimal.ZERO;
        for (ApiVacation vacation : dateVacations) {
            // 假期开始和结束时间
            Date startVacation = DateUtils.workStart(vacation.getStartTime());
            Date endVacation = DateUtils.workEnd(vacation.getEndTime());
            // 请假时间在这一天之内
            if (DateUtil.compare(startVacation, startDay) >= 0
                    && DateUtil.compare(endVacation, endDay) <= 0) {
                vacationHour = vacationHour.add(vacation.getTimeLength());
                continue;
            }
            // 请假开始和结束时间横跨今天的
            if (DateUtil.compare(startVacation, startDay) <= 0
                    && DateUtil.compare(endVacation, endDay) >= 0) {
                vacationHour = vacationHour.add(WORKING_HOURS_OF_DAY);
                continue;
            }
            // 请假开始时间在这一天但是结束时间不在
            if (DateUtil.compare(startVacation, startDay) >= 0
                    && DateUtil.compare(endVacation, endDay) >= 0) {
                // 休假分钟
                long vacationMinutes = DateUtil.between(startVacation, endDay, DateUnit.MINUTE);
                // 开始时间是否在上午,减去午休时间
                if (DateUtil.compare(startVacation, endMorning) <= 0){
                    vacationMinutes-=NOON_REST_MINUTES;
                }
                vacationHour = vacationHour
                        .add(
                                // 分钟转小时
                                (BigDecimal.valueOf(vacationMinutes).
                                        divide(MINUTES_OF_HOUR, 1, RoundingMode.HALF_UP))
                        );
                continue;
            }
            // 剩的就是请假结束时间在这一天,但是开始时间不在
            // 休假分钟
            long vacationMinutes = DateUtil.between(endVacation, startDay, DateUnit.MINUTE);
            // 结束时间是否在下午,减去午休时间
            if (DateUtil.compare(endVacation, startAfternoon) >= 0){
                vacationMinutes-=NOON_REST_MINUTES;
            }
            vacationHour = vacationHour
                    .add(
                            // 分钟转小时
                            (BigDecimal.valueOf(vacationMinutes).
                                    divide(MINUTES_OF_HOUR, 1, RoundingMode.HALF_UP))
                    );
        }
        return vacationHour;
    }
}

package com.wbyy.system.api.model;

import com.wbyy.system.api.domain.ApiUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Data
public class LoginUser implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 用户名id
     */
    private Long userId;

    private String weaverUserId;

    /**
     * 用户名
     */
    private String username;

    private String realName;
    private String workNumber;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * 角色列表
     */
    private Set<String> roles;

    /**
     * 部门列表
     */
    private Set<Long> depts;

    private Set<Long> sonDepts;
    @Schema(description = "入职时间")
    private Date hireDate;

    @Schema(description = "离职时间")
    private Date departureDate;

    @Schema(description = "登录来源")
    private String applicationCode;

    /**
     * 用户信息
     */
    private ApiUser sysUser;
}

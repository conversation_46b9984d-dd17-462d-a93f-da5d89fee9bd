package com.wbyy.system.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.domain.ApiVacation;
import com.wbyy.system.api.factory.RemoteUserFallbackFactory;
import com.wbyy.system.api.factory.RemoteVacationFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/6/16 9:56
 */
@FeignClient(contextId = "remoteVacationService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteVacationFallbackFactory.class)
public interface VacationApi {

    @GetMapping("/vacation/list-user-vacation")
    public R<List<ApiVacation>> listUserVacation(@RequestParam("userId")Long userId,
                                                 @RequestParam("startDate") String startDate,
                                                 @RequestParam("endDate")String endDate,
                                                 @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/vacation/list-users-vacation")
    public R<List<ApiVacation>> listUsersVacation(@RequestParam("userIds")List<Long> userIds,
                                                  @RequestParam("startDate") String startDate,
                                                  @RequestParam("endDate") String endDate,
                                                  @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

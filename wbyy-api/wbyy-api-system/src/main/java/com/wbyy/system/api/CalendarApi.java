package com.wbyy.system.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.domain.ApiCalendar;
import com.wbyy.system.api.factory.RemoteCalendarFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/5/15 10:16
 */
@FeignClient(contextId = "remoteCalendarService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteCalendarFallbackFactory.class)
public interface CalendarApi {

    @GetMapping("/calendar/list")
    R<List<ApiCalendar>> list(@RequestParam("startDate")String startDate, @RequestParam("endDate")String endDate, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

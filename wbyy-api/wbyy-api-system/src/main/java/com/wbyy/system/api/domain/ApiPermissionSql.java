package com.wbyy.system.api.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiPermissionSql implements Serializable {

    @Serial
    private static final long serialVersionUID = -5514953387297401112L;

    /**
     * 部门表别名
     */
    private String deptAlias;

    /**
     * 用户表别名
     */
    private String userAlias;

    /**
     * 权限字符串
     */
    private String permission;
}

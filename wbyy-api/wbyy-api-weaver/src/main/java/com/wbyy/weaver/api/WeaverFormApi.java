package com.wbyy.weaver.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.factory.RemoteEmployeeFallbackFactory;
import com.wbyy.weaver.api.factory.RemoteWeaverFormFallbackFactory;
import com.weaver.openapi.pojo.form.params.FormVo;
import com.weaver.openapi.pojo.form.res.vo.FormDataV3;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * @date 2025/8/1 10:20
 */
@FeignClient(contextId = "weaverFormService",
        value = ServiceNameConstants.WEAVER_SERVICE,
        fallbackFactory = RemoteWeaverFormFallbackFactory.class)
public interface WeaverFormApi {

    @PostMapping("/form/query-data-report-by-id-v3")
    R<FormDataV3> queryDataReportByIdV3(@RequestBody FormVo vo,
                                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

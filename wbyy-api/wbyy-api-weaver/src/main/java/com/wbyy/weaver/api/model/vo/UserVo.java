package com.wbyy.weaver.api.model.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/4/28 14:04
 */
@Data
public class UserVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String position;
    private List<String> otherPositions;
    private List<String> otherDepartments;
    private String sex;
    private String superior;
    private String status;
    private String graduate_Date;
    private List<Long> department;
    private String hireDate;
    private Long userid;
    private String education;
    private String jobNum;
    private String graduate_School;
    private String first_work_date;
    private String degree;
    private String email;
    private String name;
    private String account;
    private String grade;
    private String telephone;
    private String mobile;
    private String username;
    private String birthday;
    private String marital_Status;
    private String native_Place;
    private String id_No;
    private String family_Contact;
    private String age;
    private String politicsStatus;
    private String householdType;
    private String child_Status;
    private String updateTime;
    private String nation;
    private String work_Year;
    private String residence_Place;
    private String tenantKey;
}

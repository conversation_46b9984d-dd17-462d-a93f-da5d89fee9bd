package com.wbyy.weaver.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.domain.RemoteDeptInfo;
import com.wbyy.weaver.api.factory.RemoteDepartmentFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/5/20 10:10
 */
@FeignClient(contextId = "remotePositionService", value = ServiceNameConstants.WEAVER_SERVICE, fallbackFactory = RemoteDepartmentFallbackFactory.class)
public interface PositionApi {

    @GetMapping("/position/list-v2")
    R<List<RemoteDeptInfo>> listPositionV2(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

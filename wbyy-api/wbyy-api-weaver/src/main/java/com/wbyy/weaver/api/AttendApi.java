package com.wbyy.weaver.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.factory.RemoteAttendServiceFallback;
import com.weaver.openapi.pojo.attend.params.AttendVo;
import com.weaver.openapi.pojo.attend.res.vo.AttendResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/6/13 14:12
 */
@FeignClient(contextId = "remoteAttendService", value = ServiceNameConstants.WEAVER_SERVICE, fallbackFactory = RemoteAttendServiceFallback.class)
public interface AttendApi {
    @PostMapping("/attend/get-leave-v2")
    public R<List<AttendResultVo>> getLeaveV2(@RequestBody AttendVo vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/attend/get-sign-in-data")
    public R<List<AttendResultVo>> getSignInData(@RequestBody AttendVo vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/attend/get-sign-out-data")
    public R<List<AttendResultVo>> getSignOutData(@RequestBody AttendVo vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

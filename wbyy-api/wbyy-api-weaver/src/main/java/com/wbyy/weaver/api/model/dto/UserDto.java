package com.wbyy.weaver.api.model.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @author: 王金都
 * @date: 2025/4/28 14:03
 */
@Data
public class UserDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String accessToken;
    private String name;
    private String mobile;
    private List<Long> department;
    private String email;
    private String jobNum;
    private String telephone;
    private String hireDate;
    private String sex;
    private Long userid;
    private List<Long> position;
    private Long grade;
    private List<Long> superior;
    private List<Long> assistant;
    private List<Long> access;
    private String idNo;
    private String birthday;
    private String age;
    private String maritalStatus;
    private String education;
    private String degree;
    private String graduateSchool;
    private String graduateDate;
    private String nativePlace;
    private String nation;
    private String householdType;
    private String politicsStatus;
    private String residencePlace;
    private String familyContact;
    private String firstWorkDate;
    private String workYear;
    private String alias;
    private String childStatus;
    private String formDataMap;
    private List<Long> otherSuperior;
    private String password;
    private Long optId;
    private String wechatUserId;
    private Integer page;
    private Long depid;
    private String status;
    private String fetch_child;
    private String beginDate;
    private String endDate;
    private List<Map<String, String>> datas;
    private List<String> jobNumList;
    private List<String> subcompanyIds;
    private List<String> departmentIds;
    private List<String> ids;
    private boolean containUserInfo;
    private boolean containEmployeeExtend;
    private boolean containUserInfoExtend;
    private Integer current;
    private Integer pageSize;
    private List<String> userStatusList;
    private List<String> nameLikeList;
    private List<String> returnFieldList;
    private List<String> multiFieldList;
    private String account;
    private boolean needAccountInfo;
}

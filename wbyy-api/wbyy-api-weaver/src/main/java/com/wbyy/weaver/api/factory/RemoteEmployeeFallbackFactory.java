package com.wbyy.weaver.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.EmployeeApi;
import com.wbyy.weaver.api.domain.RemoteEmployeeData;
import com.wbyy.weaver.api.model.dto.QueryEmployeeDto;
import com.wbyy.weaver.api.model.dto.UserDto;
import com.wbyy.weaver.api.model.vo.UserVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/4/27 16:54
 */
@Component
public class RemoteEmployeeFallbackFactory implements FallbackFactory<EmployeeApi> {
    @Override
    public EmployeeApi create(Throwable throwable) {
        return new EmployeeApi() {
            @Override
            public R<RemoteEmployeeData> query(QueryEmployeeDto dto, String source) {
                return R.fail("获取用户列表失败："+throwable.getMessage());
            }

            @Override
            public R<List<UserVo>> departmentUsersV3(UserDto dto, String source) {
                return R.fail("获取用户列表失败："+throwable.getMessage());
            }
        };
    }
}

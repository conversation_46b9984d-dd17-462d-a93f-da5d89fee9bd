package com.wbyy.weaver.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.OaWorkFlowApi;
import com.wbyy.weaver.api.model.vo.OAProjectInitiationVO;
import com.weaver.openapi.pojo.flow.params.FlowVo;
import com.weaver.openapi.pojo.flow.res.vo.WorkFlowRequestInfo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2025/8/5 17:55
 */
@Component
public class RemoteOaWorkFlowFallbackFactory implements FallbackFactory<OaWorkFlowApi> {
    @Override
    public OaWorkFlowApi create(Throwable cause) {
        return new OaWorkFlowApi() {
            @Override
            public R<OAProjectInitiationVO> getByOaFlowId(Long oaWorkFlowId) {
                return R.fail("获取流程数据失败："+cause.getMessage());
            }

            @Override
            public R<WorkFlowRequestInfo> getWorkflowRequestV1(FlowVo vo, String source) {
                return R.fail("获取流程数据失败："+cause.getMessage());
            }
        };
    }
}

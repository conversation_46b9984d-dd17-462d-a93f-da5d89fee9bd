package com.wbyy.weaver.api.model;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/8/6 10:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UploadFileParam implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "文件不能为空")
    private String fileId;
    private String fileName;
    @NotNull(message = "文件保存路径不能为空")
    private String savePath;
}
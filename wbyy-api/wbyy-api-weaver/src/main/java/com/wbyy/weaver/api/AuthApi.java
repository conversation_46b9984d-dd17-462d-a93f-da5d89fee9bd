package com.wbyy.weaver.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.factory.RemoteAuthServiceFallback;
import com.wbyy.weaver.api.model.vo.UserInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: 王金都
 * @date: 2025/5/28 15:09
 */
@FeignClient(contextId = "remoteAuthService", value = ServiceNameConstants.WEAVER_SERVICE, fallbackFactory = RemoteAuthServiceFallback.class)
public interface AuthApi {
    @GetMapping("/auth/get-user-info")
    R<UserInfoVo> getUserInfo(@RequestParam("eteamsToken") String eteamsToken, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}

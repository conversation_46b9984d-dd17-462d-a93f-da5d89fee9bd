package com.wbyy.weaver.api.domain;

import com.wbyy.weaver.api.model.EmployeeData;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/4/28 8:29
 */
@Data
public class RemoteEmployeeData implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Integer current;
    private Long total;
    private List<EmployeeData> data;
    private Integer pageSize;
}

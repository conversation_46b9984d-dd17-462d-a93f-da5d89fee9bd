package com.wbyy.weaver.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.WeaverFormApi;
import com.weaver.openapi.pojo.form.params.FormVo;
import com.weaver.openapi.pojo.form.res.vo.FormDataV3;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/8/1 10:31
 */
@Component
public class RemoteWeaverFormFallbackFactory implements FallbackFactory<WeaverFormApi> {
    @Override
    public WeaverFormApi create(Throwable throwable) {
        return new WeaverFormApi() {
            @Override
            public R<FormDataV3> queryDataReportByIdV3(FormVo vo, String source) {
                return R.fail("获取表单数据失败："+throwable.getMessage());
            }
        };
    }
}

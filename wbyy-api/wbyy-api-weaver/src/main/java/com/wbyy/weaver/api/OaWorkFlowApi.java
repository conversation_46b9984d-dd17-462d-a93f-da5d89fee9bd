package com.wbyy.weaver.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.factory.RemoteOaWorkFlowFallbackFactory;
import com.wbyy.weaver.api.model.vo.OAProjectInitiationVO;
import com.weaver.openapi.pojo.flow.params.FlowVo;
import com.weaver.openapi.pojo.flow.res.vo.WorkFlowRequestInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(contextId = "oaWorkFlowService",
        value = ServiceNameConstants.WEAVER_SERVICE,
        fallbackFactory = RemoteOaWorkFlowFallbackFactory.class)
public interface OaWorkFlowApi {

    /**
     * 根据 oaWorkFlowId 查询项目立项表单信息
     * @param oaWorkFlowId
     * @return
     */
    @GetMapping("/oa/work/flow/get-one")
    public R<OAProjectInitiationVO> getByOaFlowId(
            @RequestParam(value = "oaWorkFlowId") Long oaWorkFlowId);

    @PostMapping("/oa/work/flow/get-workflow-request-v1")
    R<WorkFlowRequestInfo> getWorkflowRequestV1(@RequestBody FlowVo vo,
                                                @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

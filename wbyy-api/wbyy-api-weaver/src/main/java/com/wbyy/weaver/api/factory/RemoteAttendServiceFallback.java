package com.wbyy.weaver.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.AttendApi;
import com.weaver.openapi.pojo.attend.params.AttendVo;
import com.weaver.openapi.pojo.attend.res.vo.AttendResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/6/13 14:13
 */
@Component
@Slf4j
public class RemoteAttendServiceFallback implements FallbackFactory<AttendApi> {
    @Override
    public AttendApi create(Throwable throwable) {
        log.error("weaver调用失败:{}", throwable.getMessage());
        return new  AttendApi() {
            @Override
            public R<List<AttendResultVo>> getLeaveV2(AttendVo vo, String source) {
                return R.fail("获取用户请假信息失败："+throwable.getMessage());
            }

            @Override
            public R<List<AttendResultVo>> getSignInData(AttendVo vo, String source) {
                return R.fail("获取用户签到信息失败："+throwable.getMessage());
            }

            @Override
            public R<List<AttendResultVo>> getSignOutData(AttendVo vo, String source) {
                return R.fail("获取用户签退信息失败："+throwable.getMessage());
            }
        };
    }
}

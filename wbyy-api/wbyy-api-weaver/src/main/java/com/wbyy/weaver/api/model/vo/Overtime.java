package com.wbyy.weaver.api.model.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @author: 王金都
 * @date: 2025/6/13 14:40
 */
@Data
public class Overtime implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private boolean isLieuLeave;
    private String timeLength;
    private String start;
    private String dayType;
    private String end;
}

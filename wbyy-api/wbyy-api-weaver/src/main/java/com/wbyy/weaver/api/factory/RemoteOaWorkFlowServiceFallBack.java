package com.wbyy.weaver.api.factory;

import com.wbyy.weaver.api.OaWorkFlowApi;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * OaWorkFlowApi服务降级处理
 */
@Component
public class RemoteOaWorkFlowServiceFallBack implements FallbackFactory<OaWorkFlowApi> {
    @Override
    public OaWorkFlowApi create(Throwable cause) {

        return null;
    }
}

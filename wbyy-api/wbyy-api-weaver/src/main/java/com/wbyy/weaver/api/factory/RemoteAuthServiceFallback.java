package com.wbyy.weaver.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.AuthApi;
import com.wbyy.weaver.api.DepartmentApi;
import com.wbyy.weaver.api.model.vo.UserInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @author: 王金都
 * @date: 2025/5/28 15:12
 */
@Component
@Slf4j
public class RemoteAuthServiceFallback implements FallbackFactory<AuthApi> {
    @Override
    public AuthApi create(Throwable throwable) {
        log.error("weaver调用失败:{}", throwable.getMessage());
        return new AuthApi() {
            @Override
            public R<UserInfoVo> getUserInfo(String eteamsToken, String source) {
                return R.fail("获取用户信息失败："+throwable.getMessage());
            }
        };
    }
}

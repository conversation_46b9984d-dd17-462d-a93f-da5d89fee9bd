package com.wbyy.weaver.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.domain.ApiSysFile;
import com.wbyy.weaver.api.WeaverFileApi;
import com.wbyy.weaver.api.model.UploadFileParam;
import com.wbyy.weaver.api.model.vo.FileData;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @author: 王金都
 * @date: 2025/6/3 10:56
 */
@Component
public class RemoteWeaverFileFallback implements FallbackFactory<WeaverFileApi> {
    @Override
    public WeaverFileApi create(Throwable throwable) {
        return new WeaverFileApi() {
            @Override
            public R<FileData> downloadFileV2(String fileId, String source) {
                return R.fail("获取OA文件失败：" + throwable.getMessage());
            }

            @Override
            public R<ApiSysFile> uploadFile2SystemFile(UploadFileParam fileParam, String source) {
                return R.fail("上传OA文件失败：" + throwable.getMessage());
            }
        };
    }
}

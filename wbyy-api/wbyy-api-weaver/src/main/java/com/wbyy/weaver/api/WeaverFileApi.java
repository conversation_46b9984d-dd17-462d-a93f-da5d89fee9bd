package com.wbyy.weaver.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.domain.ApiSysFile;
import com.wbyy.weaver.api.factory.RemoteWeaverFileFallback;
import com.wbyy.weaver.api.model.UploadFileParam;
import com.wbyy.weaver.api.model.vo.FileData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @author: 王金都
 * @date: 2025/6/3 10:54
 */
@FeignClient(contextId = "remoteWeaverFileService", value = ServiceNameConstants.WEAVER_SERVICE, fallbackFactory = RemoteWeaverFileFallback.class)
public interface WeaverFileApi {

    @GetMapping("/file/download-file-v2")
    R<FileData> downloadFileV2(@RequestParam("fileId") String fileId,
                               @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/file/upload-file-2-system-file")
    R<ApiSysFile> uploadFile2SystemFile(@RequestBody UploadFileParam fileParam,
                                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

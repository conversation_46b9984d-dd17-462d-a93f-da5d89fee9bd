package com.wbyy.auth.service;

import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.utils.security.RSAUtils;
import com.wbyy.common.redis.constant.RedisConst;
import com.wbyy.system.api.domain.ApiUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.wbyy.common.core.constant.CacheConstants;
import com.wbyy.common.core.constant.Constants;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.UserConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.enums.UserStatus;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.text.Convert;
import com.wbyy.common.core.utils.DateUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.core.utils.ip.IpUtils;
import com.wbyy.common.redis.service.RedisService;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.system.api.UserApi;
import com.wbyy.system.api.model.LoginUser;

/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class SysLoginService
{
    @Autowired
    private UserApi userApi;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysRecordLogService recordLogService;

    @Autowired
    private RedisService redisService;

    public LoginUser ssoLogin(String code){
        String cacheKey = String.format(RedisConst.SSO_INFO_CODE,code);
        String workNumber = redisService.getCacheObject(cacheKey);
        redisService.deleteObject(cacheKey);
        R<LoginUser> userResult = userApi.getUserInfo(workNumber, SecurityConstants.INNER);

        if (R.FAIL == userResult.getCode())
        {
            throw new ServiceException(userResult.getMsg());
        }

        LoginUser userInfo = userResult.getData();
        ApiUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            recordLogService.recordLogininfor(workNumber, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + workNumber + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            recordLogService.recordLogininfor(workNumber, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
            throw new ServiceException("对不起，您的账号：" + workNumber + " 已停用");
        }
        recordLogService.recordLogininfor(workNumber, Constants.LOGIN_SUCCESS, "登录成功");
        recordLoginInfo(user.getUserId());
        return userInfo;
    }

    /**
     * 登录
     */
    public LoginUser login(String username, String password,String keyId)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写");
            throw new ServiceException("用户/密码必须填写");
        }
        String cacheKey = String.format(CacheConstants.RSA_PRIVATE_KEY,keyId);
        String base64PrivateKey = redisService.getCacheObject(cacheKey);
        String newPassword;
        try {
            // 首先尝试使用OAEP-SHA256解密
            newPassword = RSAUtils.decrypt(password, RSAUtils.base64ToPrivateKey(base64PrivateKey));
        } catch (Exception e) {
            log.warn("OAEP-SHA256解密失败，尝试其他解密方式: {}", e.getMessage());
            try {
                // 尝试使用SHA-1 OAEP解密（兼容前端jsrsasign库）
                newPassword = RSAUtils.decryptWithSHA1OAEP(password, RSAUtils.base64ToPrivateKey(base64PrivateKey));
                log.info("使用SHA-1 OAEP解密成功");
            } catch (Exception e2) {
                log.warn("SHA-1 OAEP解密失败，尝试PKCS1填充: {}", e2.getMessage());
                try {
                    // 最后尝试使用PKCS1填充解密
                    newPassword = RSAUtils.decryptWithPKCS1(password, RSAUtils.base64ToPrivateKey(base64PrivateKey));
                    log.info("使用PKCS1填充解密成功");
                } catch (Exception e3) {
                    log.error("所有解密方式都失败了", e3);
                    throw new ServiceException("密码格式错误");
                }
            }
        }finally {
            redisService.deleteObject(cacheKey);
        }
        if (StrUtil.isBlank(newPassword)){
            throw new ServiceException("密码不得为空");
        }
        // 密码如果不在指定范围内 错误
        if (newPassword.length() < UserConstants.PASSWORD_MIN_LENGTH
                || newPassword.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围");
            throw new ServiceException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围");
            throw new ServiceException("用户名不在指定范围");
        }
        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
            throw new ServiceException("很遗憾，访问IP已被列入系统黑名单");
        }
        // 查询用户信息
        R<LoginUser> userResult = userApi.getUserInfo(username, SecurityConstants.INNER);

        if (R.FAIL == userResult.getCode())
        {
            throw new ServiceException(userResult.getMsg());
        }

        LoginUser userInfo = userResult.getData();
        ApiUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }
        passwordService.validate(user, newPassword);
        recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功");
        recordLoginInfo(user.getUserId());
        return userInfo;
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId)
    {
        ApiUser sysUser = new ApiUser();
        sysUser.setUserId(userId);
        // 更新用户登录IP
        sysUser.setLoginIp(IpUtils.getIpAddr());
        // 更新用户登录时间
        sysUser.setLoginDate(DateUtils.getNowDate());
        userApi.recordUserLogin(sysUser, SecurityConstants.INNER);
    }

    public void logout(String loginName)
    {
        recordLogService.recordLogininfor(loginName, Constants.LOGOUT, "退出成功");
    }

    /**
     * 注册
     */
    public void register(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password))
        {
            throw new ServiceException("用户/密码必须填写");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            throw new ServiceException("账户长度必须在2到20个字符之间");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            throw new ServiceException("密码长度必须在5到20个字符之间");
        }

        // 注册用户信息
        ApiUser sysUser = new ApiUser();
        sysUser.setUserName(username);
        sysUser.setRealName(username);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        R<?> registerResult = userApi.registerUserInfo(sysUser, SecurityConstants.INNER);

        if (R.FAIL == registerResult.getCode())
        {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功");
    }
}

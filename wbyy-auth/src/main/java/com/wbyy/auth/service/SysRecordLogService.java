package com.wbyy.auth.service;

import com.wbyy.system.api.domain.ApiLogininfor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.wbyy.common.core.constant.Constants;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.core.utils.ip.IpUtils;
import com.wbyy.system.api.SystemLogApi;

/**
 * 记录日志方法
 * 
 * <AUTHOR>
 */
@Component
public class SysRecordLogService
{
    @Autowired
    private SystemLogApi systemLogApi;

    /**
     * 记录登录信息
     * 
     * @param username 用户名
     * @param status 状态
     * @param message 消息内容
     * @return
     */
    public void recordLogininfor(String username, String status, String message)
    {
        ApiLogininfor logininfor = new ApiLogininfor();
        logininfor.setUserName(username);
        logininfor.setIpaddr(IpUtils.getIpAddr());
        logininfor.setMsg(message);
        // 日志状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER))
        {
            logininfor.setStatus(Constants.LOGIN_SUCCESS_STATUS);
        }
        else if (Constants.LOGIN_FAIL.equals(status))
        {
            logininfor.setStatus(Constants.LOGIN_FAIL_STATUS);
        }
        systemLogApi.saveLogininfor(logininfor, SecurityConstants.INNER);
    }
}

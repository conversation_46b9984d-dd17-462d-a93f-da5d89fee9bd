package com.wbyy.auth;

import com.wbyy.common.core.constant.SecurityConstants;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import com.wbyy.common.security.annotation.EnableRyFeignClients;

/**
 * 认证授权中心
 * 
 * <AUTHOR>
 */
@Slf4j
@EnableRyFeignClients
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class })
public class WbyyAuthApplication
{
    public static void main(String[] args)
    {
        MDC.put(SecurityConstants.TRACE_ID, "auth");
        SpringApplication.run(WbyyAuthApplication.class, args);
        log.info("(♥◠‿◠)ﾉﾞ  认证授权中心启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    }
}

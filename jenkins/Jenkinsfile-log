pipeline {
    agent any
    // 声明全局变量
    environment {
        harborAddress = 'harbor.wbcro.com'
        harborRepo = 'base-server'
        appNameAbbr = 'log'
        modulePath = 'wbyy-modules/wbyy-log'
        appName = 'wbyy-modules-log'
        targetPath = './wbyy-modules/wbyy-log/target'
        dockerFilePath = './docker/wbyy/modules/log/'
    }

    stages {
        stage('拉取Git仓库代码') {
            steps {
                checkout scmGit(branches: [[name: '*/develop']], extensions: [], userRemoteConfigs: [[credentialsId: '916a3d88-2372-40b2-b87f-353bc7237252', url: 'https://gitlab.wbcro.com/base/base-server.git']])
            }
        }
        stage('通过Maven构建项目') {
            steps {
                sh '''/var/jenkins_home/apache-maven-3.9.9/bin/mvn clean package -DskipTests -pl $modulePath -am --also-make
                    cp $targetPath/$appName.jar $dockerFilePath
                '''
            }
        }
        stage('通过Docker构建镜像') {
            steps {
                sh '''docker buildx build -t $harborAddress/$harborRepo/$appName:$BUILD_NUMBER $dockerFilePath
                '''
            }
        }
        stage('将镜像推送到Harbor') {
            steps {
                sh '''echo "docker push $appName"
                    docker push $harborAddress/$harborRepo/$appName:$BUILD_NUMBER
                '''
            }
        }
        stage('通过SSH通知目标服务器') {
            steps {
                sh '''echo "通知消息脚本参数：$BUILD_USER - $BUILD_TIMESTAMP - $BUILD_NUMBER - $BUILD_URL - $JOB_BASE_NAME - $RUN_DISPLAY_URL - $RUN_CHANGES_DISPLAY_URL"
                    sh ./docker/send_message.sh begin $BUILD_USER $BUILD_TIMESTAMP $BUILD_NUMBER $BUILD_URL $JOB_BASE_NAME $RUN_DISPLAY_URL $RUN_CHANGES_DISPLAY_URL
                '''
                sshPublisher(publishers: [sshPublisherDesc(configName: 'test-*************',
                                    transfers: [sshTransfer(cleanRemote: false, excludes: '', execCommand:
                                    "sh /home/<USER>/wbyy-cloud-server/.shell/deploy.sh  $BUILD_NUMBER $harborAddress $harborRepo $appName",
                                    execTimeout: 600000, flatten: false, makeEmptyDirs: false,
                                    noDefaultExcludes: false, patternSeparator: '[, ]+', remoteDirectory: '',
                                    remoteDirectorySDF: false, removePrefix: '', sourceFiles: '')],
                                    usePromotionTimestamp: false, useWorkspaceInPromotion: false, verbose: false)])

            }
        }

    }

    post {
        success {
            script {
                sh "sh ./docker/send_message.sh success $BUILD_USER $BUILD_TIMESTAMP $BUILD_NUMBER $BUILD_URL $JOB_BASE_NAME $RUN_DISPLAY_URL $RUN_CHANGES_DISPLAY_URL"
            }
        }
        failure {
            script {
                sh "sh ./docker/send_message.sh failure $BUILD_USER $BUILD_TIMESTAMP $BUILD_NUMBER $BUILD_URL $JOB_BASE_NAME $RUN_DISPLAY_URL $RUN_CHANGES_DISPLAY_URL"
            }
        }
    }
}

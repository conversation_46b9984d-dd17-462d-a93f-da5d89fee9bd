# API参数拦截功能测试指南

## 功能说明

我已经为你实现了完整的API接口入参和出参拦截功能，包括：

### 1. 核心组件

- **@ApiParamLog注解** (`wbyy-common-log/annotation/ApiParamLog.java`)
  - 用于标记需要拦截参数的接口方法
  - 支持配置是否记录请求参数、响应参数、请求头
  - 支持排除敏感参数
  - 支持设置最大参数长度

- **ApiParamLogAspect切面** (`wbyy-common-log/aspect/ApiParamLogAspect.java`)
  - 使用AOP环绕通知拦截标记了@ApiParamLog注解的方法
  - 记录请求和响应的详细信息
  - 计算方法执行时间
  - 处理异常情况

- **ApiParamLogInfo日志实体** (`wbyy-common-log/domain/ApiParamLogInfo.java`)
  - 存储拦截到的API参数信息
  - 包含请求ID、URL、方法、参数、响应、执行时间等

### 2. 已应用的示例

- **SendController** - 在发送消息接口上添加了@ApiParamLog注解
- **ApiParamTestController** - 创建了专门的测试控制器，包含多种测试场景

## 测试方法

### 1. 启动应用
```bash
# 启动message模块
cd wbyy-modules/wbyy-message
mvn spring-boot:run
```

### 2. 测试接口

#### 简单GET测试
```bash
curl -X GET "http://localhost:8080/test/api-param/simple?name=张三&age=25"
```

#### 简单POST测试
```bash
curl -X POST "http://localhost:8080/test/api-param/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "李四",
    "age": 30,
    "email": "<EMAIL>",
    "description": "这是一个测试用户"
  }'
```

#### 敏感信息测试
```bash
curl -X POST "http://localhost:8080/test/api-param/with-sensitive" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "secret123",
    "email": "<EMAIL>",
    "secret": "topsecret",
    "token": "abc123token"
  }'
```

#### 异常测试
```bash
curl -X POST "http://localhost:8080/test/api-param/error-test" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "error",
    "age": 25
  }'
```

### 3. 查看日志

在应用日志中查找包含 "API参数拦截日志" 的日志条目，你将看到类似以下的JSON格式日志：

```json
{
  "requestId": "abc123def456",
  "operation": "简单POST测试",
  "requestUrl": "/test/api-param/simple",
  "requestMethod": "POST",
  "requestIp": "127.0.0.1",
  "username": "admin",
  "className": "com.wbyy.message.modules.test.controller.ApiParamTestController",
  "methodName": "simplePost",
  "requestParams": "{\"name\":\"李四\",\"age\":30,\"email\":\"<EMAIL>\",\"description\":\"这是一个测试用户\"}",
  "requestHeaders": "{\"content-type\":\"application/json\",\"user-agent\":\"curl/7.68.0\"}",
  "responseParams": "{\"code\":200,\"msg\":\"操作成功\",\"data\":{\"message\":\"处理成功\",\"requestData\":{...}}}",
  "executionTime": 15,
  "success": true,
  "createTime": "2024-01-15 10:30:45",
  "threadId": "123",
  "traceId": "trace-abc-123"
}
```

## 注解参数说明

```java
@ApiParamLog(
    value = "操作描述",           // 操作描述
    logRequest = true,          // 是否记录请求参数
    logResponse = true,         // 是否记录响应参数
    logHeaders = false,         // 是否记录请求头
    excludeParams = {"password", "token", "secret"}, // 排除的敏感参数
    maxParamLength = 2000       // 最大参数长度
)
```

## 使用建议

1. **在重要的业务接口上添加@ApiParamLog注解**
2. **对于包含敏感信息的接口，配置excludeParams排除敏感字段**
3. **根据需要调整maxParamLength避免日志过大**
4. **在生产环境中可以通过日志级别控制是否输出详细日志**

## 扩展功能

如果需要将日志存储到数据库或发送到其他系统，可以修改`ApiParamLogAspect.outputLog()`方法，添加相应的处理逻辑。
